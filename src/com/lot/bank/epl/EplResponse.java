package com.lot.bank.epl;

import com.lot.bank.bean.BankResponse;
import com.lot.bank.bean.TradeStatus;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/5 14:35
 */
public class EplResponse implements BankResponse {
    private String requestNo;
    private String version;
    private String productId;
    private String transType;
    private String agentId;
    private String merNo;
    private String currencyCode;
    private String payUrl;
    private String orderTime;
    private String orderId;
    private String tradeSatus;
    private String acquirer;
    private String orderNo;
    private String respCode;
    private String respMsg;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getMerNo() {
        return merNo;
    }

    public void setMerNo(String merNo) {
        this.merNo = merNo;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getPayUrl() {
        return payUrl;
    }

    public void setPayUrl(String payUrl) {
        this.payUrl = payUrl;
    }

    public String getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(String orderTime) {
        this.orderTime = orderTime;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTradeSatus() {
        return tradeSatus;
    }

    public void setTradeSatus(String tradeSatus) {
        this.tradeSatus = tradeSatus;
    }

    public String getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getRespMsg() {
        return respMsg;
    }

    public void setRespMsg(String respMsg) {
        this.respMsg = respMsg;
    }

    @Override
    public TradeStatus getTradeStatus() {
        if (tradeSatus != null) {
            if ("FAILED".equals(tradeSatus)) {
                return TradeStatus.FAIL;
            }

            if ("PAIED".equals(tradeSatus)) {
                return TradeStatus.SUCCESS;
            }
        } else if (respCode != null) {

            switch (respCode) {
                case "P000":
                case "0000":
                case "9997":
                case "9998":
                case "9999":
                    return TradeStatus.PROCESS;
                default:
                    return TradeStatus.FAIL;
            }
        }

        return TradeStatus.PROCESS;

    }

    @Override
    public String getChannelOrderNo() {
        return orderId;
    }

    @Override
    public String getResponseCode() {
        return respCode;
    }

    @Override
    public String getResponseMessage() {
        return respMsg;
    }
}
