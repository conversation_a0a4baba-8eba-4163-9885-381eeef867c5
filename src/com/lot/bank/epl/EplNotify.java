package com.lot.bank.epl;

import com.lot.bank.bean.BankResponse;
import com.lot.bank.bean.TradeStatus;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/5 14:35
 */
public class EplNotify implements BankResponse {
    private String agentId;
    private String bankMsg;
    private String merNo;
    private String orderDate;
    private String orderId;
    private String orderNo;
    private String productId;
    private String respCode;
    private String respDesc;
    private String tradeState;
    private String transAmt;
    private String transType;
    private String version;

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getBankMsg() {
        return bankMsg;
    }

    public void setBankMsg(String bankMsg) {
        this.bankMsg = bankMsg;
    }

    public String getMerNo() {
        return merNo;
    }

    public void setMerNo(String merNo) {
        this.merNo = merNo;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getRespDesc() {
        return respDesc;
    }

    public void setRespDesc(String respDesc) {
        this.respDesc = respDesc;
    }

    public String getTradeState() {
        return tradeState;
    }

    public void setTradeState(String tradeState) {
        this.tradeState = tradeState;
    }

    public String getTransAmt() {
        return transAmt;
    }

    public void setTransAmt(String transAmt) {
        this.transAmt = transAmt;
    }

    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Override
    public TradeStatus getTradeStatus() {
        if ("FAILED".equals(tradeState)) {
            return TradeStatus.FAIL;
        }

        if ("PAIED".equals(tradeState)) {
            return TradeStatus.SUCCESS;
        }

        return TradeStatus.PROCESS;
    }

    @Override
    public String getChannelOrderNo() {
        return orderId;
    }

    @Override
    public String getResponseCode() {
        return respCode;
    }

    @Override
    public String getResponseMessage() {
        return respDesc;
    }
}
