
package com.lot.bank.ws;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.lot.bank.ws package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.lot.bank.ws
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link CompletePaymentResponse }
     * 
     */
    public CompletePaymentResponse createCompletePaymentResponse() {
        return new CompletePaymentResponse();
    }

    /**
     * Create an instance of {@link CorrectionResponse }
     * 
     */
    public CorrectionResponse createCorrectionResponse() {
        return new CorrectionResponse();
    }

    /**
     * Create an instance of {@link CanclePayment }
     * 
     */
    public CanclePayment createCanclePayment() {
        return new CanclePayment();
    }

    /**
     * Create an instance of {@link CompletePayment }
     * 
     */
    public CompletePayment createCompletePayment() {
        return new CompletePayment();
    }

    /**
     * Create an instance of {@link CanclePaymentResponse }
     * 
     */
    public CanclePaymentResponse createCanclePaymentResponse() {
        return new CanclePaymentResponse();
    }

    /**
     * Create an instance of {@link Correction }
     * 
     */
    public Correction createCorrection() {
        return new Correction();
    }

    /**
     * Create an instance of {@link AuthPaymentResponse }
     * 
     */
    public AuthPaymentResponse createAuthPaymentResponse() {
        return new AuthPaymentResponse();
    }

    /**
     * Create an instance of {@link PaymentResponse }
     * 
     */
    public PaymentResponse createPaymentResponse() {
        return new PaymentResponse();
    }

    /**
     * Create an instance of {@link AuthPayment }
     * 
     */
    public AuthPayment createAuthPayment() {
        return new AuthPayment();
    }

    /**
     * Create an instance of {@link Payment }
     * 
     */
    public Payment createPayment() {
        return new Payment();
    }

}
