package com.lot.bank.ws;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 2.2.12
 * Wed Nov 04 15:47:45 CST 2015
 * Generated source version: 2.2.12
 * 
 */
 
@WebService(targetNamespace = "com/TransCT", name = "PaymentPortType")
@XmlSeeAlso({ObjectFactory.class})
public interface PaymentPortType {

    @WebResult(name = "out", targetNamespace = "com/TransCT")
    @RequestWrapper(localName = "correction", targetNamespace = "com/TransCT", className = "com.lot.bank.ws.Correction")
    @WebMethod
    @ResponseWrapper(localName = "correctionResponse", targetNamespace = "com/TransCT", className = "com.lot.bank.ws.CorrectionResponse")
    public java.lang.String correction(
        @WebParam(name = "in0", targetNamespace = "com/TransCT")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "com/TransCT")
    @RequestWrapper(localName = "canclePayment", targetNamespace = "com/TransCT", className = "com.lot.bank.ws.CanclePayment")
    @WebMethod
    @ResponseWrapper(localName = "canclePaymentResponse", targetNamespace = "com/TransCT", className = "com.lot.bank.ws.CanclePaymentResponse")
    public java.lang.String canclePayment(
        @WebParam(name = "in0", targetNamespace = "com/TransCT")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "com/TransCT")
    @RequestWrapper(localName = "payment", targetNamespace = "com/TransCT", className = "com.lot.bank.ws.Payment")
    @WebMethod
    @ResponseWrapper(localName = "paymentResponse", targetNamespace = "com/TransCT", className = "com.lot.bank.ws.PaymentResponse")
    public java.lang.String payment(
        @WebParam(name = "in0", targetNamespace = "com/TransCT")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "com/TransCT")
    @RequestWrapper(localName = "authPayment", targetNamespace = "com/TransCT", className = "com.lot.bank.ws.AuthPayment")
    @WebMethod
    @ResponseWrapper(localName = "authPaymentResponse", targetNamespace = "com/TransCT", className = "com.lot.bank.ws.AuthPaymentResponse")
    public java.lang.String authPayment(
        @WebParam(name = "in0", targetNamespace = "com/TransCT")
        java.lang.String in0
    );

    @WebResult(name = "out", targetNamespace = "com/TransCT")
    @RequestWrapper(localName = "completePayment", targetNamespace = "com/TransCT", className = "com.lot.bank.ws.CompletePayment")
    @WebMethod
    @ResponseWrapper(localName = "completePaymentResponse", targetNamespace = "com/TransCT", className = "com.lot.bank.ws.CompletePaymentResponse")
    public java.lang.String completePayment(
        @WebParam(name = "in0", targetNamespace = "com/TransCT")
        java.lang.String in0
    );
}
