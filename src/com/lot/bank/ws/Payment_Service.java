
/*
 * 
 */

package com.lot.bank.ws;

import java.net.MalformedURLException;
import java.net.URL;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;

import com.lot.bean.ConstantsBean;

/**
 * This class was generated by Apache CXF 2.2.12
 * Wed Nov 04 15:47:45 CST 2015
 * Generated source version: 2.2.12
 * 
 */


@WebServiceClient(name = "Payment", 
                  //wsdlLocation = "http://192.168.7.43:8080/BankWS/services/Payment?wsdl",
                  targetNamespace = "com/TransCT") 
public class Payment_Service extends Service {

    public final static URL WSDL_LOCATION;
    public final static QName SERVICE = new QName("com/TransCT", "Payment");
    public final static QName PaymentHttpPort = new QName("com/TransCT", "PaymentHttpPort");
    static {
        URL url = null;
        try {
            url = new URL(ConstantsBean.SYSSET.get("bankws"));
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(Payment_Service.class.getName())
                .log(java.util.logging.Level.INFO, 
                     "Can not initialize the default wsdl from {0}", ConstantsBean.SYSSET.get("bankws"));
        }
        WSDL_LOCATION = url;
    }

    public Payment_Service(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public Payment_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public Payment_Service() {
        super(WSDL_LOCATION, SERVICE);
    }
    

    /**
     * 
     * @return
     *     returns PaymentPortType
     */
    @WebEndpoint(name = "PaymentHttpPort")
    public PaymentPortType getPaymentHttpPort() {
        return super.getPort(PaymentHttpPort, PaymentPortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns PaymentPortType
     */
    @WebEndpoint(name = "PaymentHttpPort")
    public PaymentPortType getPaymentHttpPort(WebServiceFeature... features) {
        return super.getPort(PaymentHttpPort, PaymentPortType.class, features);
    }

}
