package com.lot.bank.cbs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/29 16:30
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateDmResponse extends CbsResponse {
    private RiskInformation riskInformation;
    private PaymentInformation paymentInformation;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RiskInformation {
        private Profile profile;
        private List<Rule> rules;
        private InfoCodes infoCodes;
        private Velocity velocity;
        private Score score;
        private IpAddress ipAddress;
        private String localTime;

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Profile {
            private String name;

            private String desinationQueue;

            private String selectorRule;
        }

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Rule {
            private String name;

            private String decision;
        }

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class InfoCodes {
            private List<String> velocity;

            private List<String> address;

            private List<String> customerList;

            private List<String> deviceBehavior;

            private List<String> identityChange;

            private List<String> internet;

            private List<String> phone;

            private List<String> suspicious;

            private List<String> globalVelocity;
        }

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Velocity {
            private List<Morphing> morphing;

            private List<String> address;

            @Data
            @JsonIgnoreProperties(ignoreUnknown = true)
            public static class Morphing {
                private Integer count;

                private String fieldName;

                private String informationCode;
            }
        }

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Score {
            private List<String> factorCodes;

            private String modelUsed;

            private String result;
        }

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class IpAddress {
            private String anonymizerStatus;

            private String locality;

            private String country;

            private String administrativeArea;

            private String routingMethod;

            private String carrier;

            private String organization;
        }

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PaymentInformation {
        private String binCountry;

        private String accountType;

        private String issuer;

        private String scheme;

        private String bin;
    }
}
