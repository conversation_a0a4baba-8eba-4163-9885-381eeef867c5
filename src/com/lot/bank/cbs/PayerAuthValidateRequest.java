package com.lot.bank.cbs;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/29 16:30
 */
@Data
public class PayerAuthValidateRequest extends CbsRequest<PayerAuthValidateResponse> {
    @Override
    public String getRequestUrl() {
        return "/risk/v1/authentication-results";
    }

    private OrderInformation orderInformation;

    private PaymentInformation paymentInformation;

    private ConsumerAuthenticationInformation consumerAuthenticationInformation;

    @Data
    public static class ConsumerAuthenticationInformation {
        private String authenticationTransactionId;
    }

    @Data
    public static class OrderInformation {
        private AmountDetails amountDetails;

        @Data
        public static class AmountDetails {
            private String currency;
            private String totalAmount;
        }
    }
}
