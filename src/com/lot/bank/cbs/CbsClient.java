package com.lot.bank.cbs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.RandomUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.lot.error.code.PgCode;
import com.lot.error.prediction.AppAssert;
import com.lot.util.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/5 9:16
 */
@Slf4j
public class CbsClient {
    private CbsClient(CbsConfig config, RSAPrivateKeyx myPrivateKey, RSAPublicKeyx yourPublicKey) {
        this.config = config;
        this.myPrivateKey = myPrivateKey;
        this.yourPublicKey = yourPublicKey;
    }

    private final CbsConfig config;
    private final RSAPrivateKeyx myPrivateKey;
    private final RSAPublicKeyx yourPublicKey;

    public static CbsClient config(CbsConfig config) {
        return new CbsClient(config,
                RSAPrivateKeyx.pkcs12(config.getMyPrivateKeyFile(), config.getMyPrivateKeyPassword()),
                RSAPublicKeyx.cer(config.getYourCertFile()));
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AgentCybersourceRequest {
        private String seqNo;

        private String api;

        private String requestData;

        private String nonceStr;
    }

    @Data
    public static class AgentCybersourceResponse {
        private String returnCode;
        private String returnMsg;
        private RData data;

        @Data
        public static class RData {
            private String seqNo;
            private String statusCode;
            private String responseData;
        }

        public boolean isSuccess() {
            return "0000".equals(returnCode);
        }
    }


    public <T extends CbsResponse> T execute(CbsRequest<T> request, String tradeNo) {
        Map<String, String> headers = new HashMap<>();
        headers.put("x-efps-sign-no", config.getSignNo());
        headers.put("x-efps-version", "1.0");
        headers.put("x-efps-timestamp", DateTime.now().toString(DatePattern.PURE_DATETIME_PATTERN));

        AgentCybersourceRequest agentCybersourceRequest = new AgentCybersourceRequest();
        agentCybersourceRequest.setNonceStr(RandomUtil.randomString(32));
        agentCybersourceRequest.setSeqNo(tradeNo + RandomUtil.randomString(6));
        agentCybersourceRequest.setApi(request.getRequestUrl());
        agentCybersourceRequest.setRequestData(JsonUtils.objToJson(request));

        String json = JsonUtils.objToJson(agentCybersourceRequest);

        Bytes signContent = Bytes.fromText(json, StandardCharsets.UTF_8);
        String sign = RSA.sign(signContent, myPrivateKey).toBase64();
        headers.put("x-efps-sign-type", "RSAwithSHA256");
        headers.put("x-efps-sign", sign);

        ResponseEntity<String> responseEntity = RestClient.builder().log().build()
                .post(config.getHost())
                .contentType(MediaType.APPLICATION_JSON, StandardCharsets.UTF_8)
                .headers(headers)
                .body(json)
                .exchangeForEntity(String.class);

        List<String> x = responseEntity.getHeaders().get("x-efps-sign");
        AppAssert.state(CollUtil.isNotEmpty(x), PgCode.BIZ_ERR.msg("efps sign is empty"));

        sign = x.get(0);
        json = responseEntity.getBody();
        AppAssert.notNull(json, PgCode.BIZ_ERR.msg("efps response body is empty"));

        boolean signOk = RSA.verify(Bytes.fromText(json, StandardCharsets.UTF_8), Bytes.fromBase64(sign), yourPublicKey);
        AppAssert.state(signOk, PgCode.BIZ_ERR.msg("efps verify sign error"));

        AgentCybersourceResponse agentCybersourceResponse = JsonUtils.jsonToObj(json, AgentCybersourceResponse.class);

        AppAssert.state(agentCybersourceResponse.isSuccess(), PgCode.BIZ_ERR.msg("efps response fail " + agentCybersourceResponse.getReturnCode() + ":" + agentCybersourceResponse.getReturnMsg()));

        return JsonUtils.jsonToObj(agentCybersourceResponse.getData().getResponseData(), request.getResponseClass());
    }

}
