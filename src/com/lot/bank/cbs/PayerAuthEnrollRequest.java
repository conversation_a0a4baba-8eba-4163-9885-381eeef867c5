package com.lot.bank.cbs;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/29 16:30
 */
@Data
public class PayerAuthEnrollRequest extends CbsRequest<PayerAuthEnrollResponse> {
    @Override
    public String getRequestUrl() {
        return "/risk/v1/authentications";
    }

    private OrderInformation orderInformation;

    private PaymentInformation paymentInformation;

    private BuyerInformation buyerInformation;

    private DeviceInformation deviceInformation;

    private ConsumerAuthenticationInformation consumerAuthenticationInformation;

    private MerchantInformation merchantInformation;

    private AcquirerInformation acquirerInformation;

    @Data
    public static class AcquirerInformation {
        private String acquirerBin;
        private String country;
        private String merchantId;
    }


    @Data
    public static class MerchantInformation {
        private String merchantName;
        private MerchantDescriptor merchantDescriptor;

        @Data
        public static class MerchantDescriptor {
            private String name;

            private String url;
        }
    }

    @Data
    public static class DeviceInformation {
        private String ipAddress;

//        private String httpAcceptBrowserValue;

        private String httpAcceptContent;

        private String httpBrowserLanguage;

        private Boolean httpBrowserJavaEnabled;

        private Boolean httpBrowserJavaScriptEnabled;

        private String httpBrowserColorDepth;

        private String httpBrowserScreenHeight;

        private String httpBrowserScreenWidth;

        private String httpBrowserTimeDifference;

        private String userAgentBrowserValue;
    }

    @Data
    public static class ConsumerAuthenticationInformation {
        private String referenceId;
        private String returnUrl;
        private String deviceChannel;
        private String transactionMode;
        private String mcc;
    }

    @Data
    public static class BuyerInformation {
        private String mobilePhone;
    }

    @Data
    public static class OrderInformation {
        private AmountDetails amountDetails;
        private BillTo billTo;

        @Data
        public static class AmountDetails {
            private String currency;
            private String totalAmount;
        }

        @Data
        public static class BillTo {
            private String address1;

            private String address2;

            private String administrativeArea;

            private String country;

            private String locality;

            private String firstName;

            private String lastName;

            private String phoneNumber;

            private String email;

            private String postalCode;
        }
    }
}
