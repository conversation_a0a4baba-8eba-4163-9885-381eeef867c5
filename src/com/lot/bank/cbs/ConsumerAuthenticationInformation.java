package com.lot.bank.cbs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lot.model.CardType;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/16 14:08
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConsumerAuthenticationInformation {
    private String accessToken;

    private String acsRenderingType;

    private String acsTransactionId;

    private String acsUrl;

    private String authenticationPath;

    private String authorizationPayload;

    private String authenticationType;

    private String authenticationTransactionId;

    private String authenticationTransactionContextId;

    private Integer validityPeriod;

    private String cardholderMessage;

    private String cavv;

    private String cavvAlgorithm;

    private String challengeCancelCode;

    private String challengeRequired;

    private String decoupledAuthenticationIndicator;

    private String directoryServerErrorCode;

    private String directoryServerErrorDescription;

    private String ecommerceIndicator;

    private String eci;

    private String eciRaw;

    private String effectiveAuthenticationType;

    private String exemptionDataRaw;

    private String networkScore;

    private String pareq;

    private String paresStatus;

    private String proofXml;

    private String proxyPan;

    private String sdkTransactionId;

    private String signedParesStatusReason;

    private String specificationVersion;

    private String stepUpUrl;

    private String threeDSServerTransactionId;

    private String ucafAuthenticationData;

    private String ucafCollectionIndicator;

    private String veresEnrolled;

    private String whiteListStatusSource;

    private String xid;

    private String directoryServerTransactionId;

    private String acsOperatorID;

    private String acsReferenceNumber;

    private String idciDecision;

    private String idciReasonCode1;

    private String idciReasonCode2;

    private Integer idciScore;

    public boolean isLiabilityShift(CardType cardType) {
        if (cardType == CardType.MASTERCARD) {
            return "1".equals(ucafCollectionIndicator) || "2".equals(ucafCollectionIndicator);
        } else if (cardType == CardType.VISA) {
            return "05".equals(eci) || "06".equals(eci);
        } else {
            return false;
        }
    }

    public String getAuthenticationValue(CardType cardType) {
        if (cardType == CardType.MASTERCARD) {
            return ucafAuthenticationData;
        } else if (cardType == CardType.VISA) {
            return cavv;
        } else {
            return null;
        }
    }
}
