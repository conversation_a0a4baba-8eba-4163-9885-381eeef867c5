package com.lot.bank.cbs;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/4 16:58
 */
@Data
public abstract class CbsRequest<T extends CbsResponse> {
    @JsonIgnore
    public Class<T> getResponseClass() {
        Type[] types = ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments();
        return (Class<T>) (types[types.length - 1]);
    }

    @JsonIgnore
    public abstract String getRequestUrl();

    private ClientReferenceInformation clientReferenceInformation;
}
