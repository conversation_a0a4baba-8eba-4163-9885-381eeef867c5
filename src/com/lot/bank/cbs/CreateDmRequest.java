package com.lot.bank.cbs;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/29 16:30
 */
@Data
public class CreateDmRequest extends CbsRequest<CreateDmResponse> {
    @Override
    public String getRequestUrl() {
        return "/risk/v1/decisions";
    }

    private OrderInformation orderInformation;

    private PaymentInformation paymentInformation;

    private BuyerInformation buyerInformation;

    private DeviceInformation deviceInformation;

    private List<MerchantDefinedInformation> merchantDefinedInformation;

    @Data
    public static class MerchantDefinedInformation {
        private String key;
        private String value;
    }

    @Data
    public static class DeviceInformation {
        private String ipAddress;

        private String fingerprintSessionId;

        //隐藏参数，官方文档没有，前端使用的session_id不需要拼接MID
        private boolean useRawFingerprintSessionId = true;
    }

    @Data
    public static class BuyerInformation {
        private String merchantCustomerId;
        private String dateOfBirth;
    }

    @Data
    public static class OrderInformation {
        private AmountDetails amountDetails;
        private BillTo billTo;
        private ShipTo shipTo;
        private List<LineItems> lineItems;

        @Data
        public static class LineItems {
            private String unitPrice;
            private Integer quantity;
            private String productCode;
            private String productSKU;
            private String productName;
        }

        @Data
        public static class AmountDetails {
            private String currency;
            private String totalAmount;
        }

        @Data
        public static class BillTo {
            private String address1;

            private String address2;

            private String administrativeArea;

            private String country;

            private String locality;

            private String firstName;

            private String lastName;

            private String phoneNumber;

            private String email;

            private String postalCode;
        }

        @Data
        public static class ShipTo {
            private String address1;

            private String address2;

            private String administrativeArea;

            private String country;

            private String locality;

            private String firstName;

            private String lastName;

            private String phoneNumber;

            private String postalCode;
        }
    }
}
