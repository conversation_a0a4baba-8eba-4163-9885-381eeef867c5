package com.lot.bank.sxy;

import org.apache.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

public class CipherWrapper {

    private static PathCertificateReader certificateReader = new PathCertificateReader();
    
    public static JSONObject bothEncryptWrap(String path, String merchantId, String password, JSONObject jsonObject)
            throws Exception {
        String data;
        String encryptKey;
        // String keyPath, String merchantId, String password
        try {
            jsonObject.put("hmac", SxyRSAUtils.sign(jsonObject.getBytes("hmac"),
                    certificateReader.readPrivateKey(path, merchantId, password)));
            String randomKey = ShouxinyiUtils.generateAlphaAndDigit(16);
            data = SxyAESUtils.encryptToBase64(jsonObject.toJSONString(), randomKey);
            encryptKey = SxyRSAUtils.encryptByPublicKey(randomKey, certificateReader.readPublicKey(path));
        } catch (Exception e) {
            throw new Exception(e);
        }
        JSONObject json = new JSONObject();
        json.put("data", data);
        json.put("encryptKey", encryptKey);
        json.put("merchantId", merchantId);
        json.put("requestId", jsonObject.getString("requestId"));
        return json;
    }

    public static JSONObject bothDecryptWrap(String keyPath, String merchantId, String password, JSONObject json)
            throws Exception {
        String realData;
        String data = (json.get("data") == null) ? null : String.valueOf(json.get("data"));
        String encryptKey = (json.get("encryptKey") == null) ? null : String.valueOf(json.get("encryptKey"));
        // String merchantId = (json.get("merchantId") == null) ? null :
        // String.valueOf(json.get("merchantId"));
        // String partnerId = (json.get("partnerId") == null) ? null :
        // String.valueOf(json.get("partnerId"));
        if ((data == null) || (encryptKey == null) || (merchantId == null))
            return null;
        try {
            realData = SxyAESUtils.decryptFromBase64(data, SxyRSAUtils.decryptByPrivateKey(encryptKey,
                    certificateReader.readPrivateKey(keyPath, merchantId, password)));
        } catch (Exception e) {
            throw new Exception(e);
        }
        return ((StringUtils.isBlank(realData)) ? null : JSON.parseObject(realData));
    }
}
