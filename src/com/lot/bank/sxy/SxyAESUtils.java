package com.lot.bank.sxy;

import org.apache.log4j.Logger;
import java.io.UnsupportedEncodingException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

public class SxyAESUtils {

    public static final int KEY_SIZE_BIT_128 = 128;

    public static final int KEY_SIZE_BIT_256 = 256;

    public static final String CHARSTER_ENCODING = "UTF-8";

    static Logger LOGGER = Logger.getLogger(SxyAESUtils.class);

    public static String encrypt(String content, String key) {
        Cipher cipher;
        try {
            cipher = Cipher.getInstance("AES");
            byte[] byteContent = content.getBytes("UTF-8");
            cipher.init(1, gen<PERSON>ey(key));
            byte[] result = cipher.doFinal(byteContent);
            return ShouxinyiUtils.toHexStr(result);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    public static String decrypt(String content, String key) {
        byte[] decryptFrom;
        try {
            decryptFrom = ShouxinyiUtils.toByte(content);
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(2, genKey(key));
            byte[] result = cipher.doFinal(decryptFrom);
            return new String(result);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    private static SecretKeySpec genKey(String sourceKey) {
        byte[] enCodeFormat = new byte[1];
        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(sourceKey.getBytes());
            kgen.init(128, secureRandom);
            SecretKey secretKey = kgen.generateKey();
            enCodeFormat = secretKey.getEncoded();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return new SecretKeySpec(enCodeFormat, "AES");
    }

    public static byte[] encrypt(byte[] data, byte[] key) {
        if (key.length != 16)
            throw new RuntimeException("Invalid AES key length (must be 16 bytes)");
        try {
            SecretKeySpec secretKey = new SecretKeySpec(key, "AES");
            byte[] enCodeFormat = secretKey.getEncoded();
            SecretKeySpec seckey = new SecretKeySpec(enCodeFormat, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(1, seckey);
            byte[] result = cipher.doFinal(data);
            return result;
        } catch (Exception e) {
            throw new RuntimeException("encrypt fail!", e);
        }
    }

    public static byte[] decrypt(byte[] data, byte[] key) {
        if (key.length != 16)
            throw new RuntimeException("Invalid AES key length (must be 16 bytes)");
        try {
            SecretKeySpec secretKey = new SecretKeySpec(key, "AES");
            byte[] enCodeFormat = secretKey.getEncoded();
            SecretKeySpec seckey = new SecretKeySpec(enCodeFormat, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(2, seckey);
            byte[] result = cipher.doFinal(data);
            return result;
        } catch (Exception e) {
            throw new RuntimeException("decrypt fail!", e);
        }
    }

    public static String encryptToBase64(String data, String key) {
        byte[] valueByte;
        try {
            valueByte = encrypt(data.getBytes("UTF-8"), key.getBytes("UTF-8"));
            return new String(Base64.encode(valueByte));
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("encrypt fail!", e);
        }
    }

    public static String decryptFromBase64(String data, String key) {
        byte[] originalData;
        try {
            originalData = Base64.decode(data.getBytes());
            byte[] valueByte = decrypt(originalData, key.getBytes("UTF-8"));
            return new String(valueByte, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("decrypt fail!", e);
        }
    }

    public static String encryptWithKeyBase64(String data, String key) {
        byte[] valueByte;
        try {
            valueByte = encrypt(data.getBytes("UTF-8"), Base64.decode(key.getBytes()));
            return new String(Base64.encode(valueByte));
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("encrypt fail!", e);
        }
    }

    public static String decryptWithKeyBase64(String data, String key) {
        byte[] originalData;
        try {
            originalData = Base64.decode(data.getBytes());
            byte[] valueByte = decrypt(originalData, Base64.decode(key.getBytes()));
            return new String(valueByte, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("decrypt fail!", e);
        }
    }

    public static byte[] genarateRandomKey() {
        KeyGenerator keygen = null;
        try {
            keygen = KeyGenerator.getInstance("AES/ECB/PKCS5Padding");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(" genarateRandomKey fail!", e);
        }
        SecureRandom random = new SecureRandom();
        keygen.init(random);
        Key key = keygen.generateKey();
        return key.getEncoded();
    }

    public static String genarateRandomKeyWithBase64() {
        return new String(Base64.encode(genarateRandomKey()));
    }
}
