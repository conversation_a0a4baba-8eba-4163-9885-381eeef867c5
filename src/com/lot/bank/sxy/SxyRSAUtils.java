package com.lot.bank.sxy;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.security.Key;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.KeyStore;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.Security;
import java.security.Signature;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;
import javax.crypto.Cipher;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

public class SxyRSAUtils {

    public static final String KEY_STORE = "JKS";

    public static final String X509 = "X.509";

    static Logger LOGGER = Logger.getLogger(SxyRSAUtils.class);

    public static KeyStore getKeyLibs(String keyStorePath, String password) throws Exception {
        InputStream is = null;
        try {
            is = new FileInputStream(keyStorePath);
        } catch (FileNotFoundException e) {
            LOGGER.warn(
                    "not found key file in the absolute path , try the relative path , path:[" + keyStorePath + "]");
        }
        if (is == null) {
            is = Thread.currentThread().getClass().getResourceAsStream(keyStorePath);
        }
        if (is == null) {
            throw new IllegalArgumentException("not found key file, path:[" + keyStorePath + "]");
        }
        KeyStore ks = KeyStore.getInstance("JKS");
        ks.load(is, password.toCharArray());
        is.close();
        return ks;
    }

    public static Certificate getCertificate(String certificatePath) throws Exception {
        InputStream in = null;
        try {
            in = new FileInputStream(certificatePath);
        } catch (FileNotFoundException e) {
            LOGGER.warn("not found cert file in the absolute path , try the relative path , path:[" + certificatePath
                    + "]");
        }
        if (in == null) {
            in = Thread.currentThread().getClass().getResourceAsStream(certificatePath);
        }
        if (in == null) {
            throw new IllegalArgumentException("not found cert file, path:[" + certificatePath + "]");
        }
        CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
        Certificate certificate = certificateFactory.generateCertificate(in);
        in.close();
        return certificate;
    }

    public static boolean verify(byte[] data, String sign, X509Certificate cert) throws Exception {
        PublicKey publicKey = cert.getPublicKey();
        Signature signature = Signature.getInstance(cert.getSigAlgName());
        signature.initVerify(publicKey);
        signature.update(data);
        return signature.verify(decryptBASE64(sign));
    }

    public static byte[] decryptBASE64(String key) throws Exception {
        return Base64.decodeBase64(key.getBytes());
    }

    public static byte[] encryptByPrivateKey(byte[] data, String keyStorePath, String alias, String password)
            throws Exception {
        InputStream in = null;
        try {
            in = new FileInputStream(keyStorePath);
        } catch (FileNotFoundException e) {
            LOGGER.warn(
                    "not found key file in the absolute path , try the relative path , path:[" + keyStorePath + "]");
        }
        if (in == null) {
            in = Thread.currentThread().getClass().getResourceAsStream(keyStorePath);
        }
        if (in == null) {
            throw new IllegalArgumentException("not found key file, path:[" + keyStorePath + "]");
        }
        KeyStore ks = KeyStore.getInstance("JKS");
        ks.load(in, password.toCharArray());
        in.close();
        PrivateKey privateKey = (PrivateKey) ks.getKey(alias, password.toCharArray());
        Cipher cipher = Cipher.getInstance(privateKey.getAlgorithm());
        cipher.init(1, privateKey);
        return cipher.doFinal(data);
    }

    public static String encryptBASE64(byte[] key) throws Exception {
        return org.apache.commons.codec.binary.Base64.encodeBase64String(key);
    }

    public static String sign(byte[] data, String privateKey) throws Exception {
        byte[] keyBytes = decryptBASE64(privateKey);
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey2 = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        Signature signature = Signature.getInstance("MD5withRSA");
        signature.initSign(privateKey2);
        signature.update(data);
        return encryptBASE64(signature.sign());
    }

    public static String sign2(byte[] data, PrivateKey privateKey) throws Exception {
        Signature signature = Signature.getInstance("MD5withRSA");
        signature.initSign(privateKey);
        signature.update(data);
        return encryptBASE64(signature.sign());
    }

    public static boolean verify(byte[] data, String publicKey, String sign) throws Exception {
        byte[] keyBytes = decryptBASE64(publicKey);
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey2 = keyFactory.generatePublic(x509EncodedKeySpec);
        Signature signature = Signature.getInstance("MD5withRSA");
        signature.initVerify(publicKey2);
        signature.update(data);
        return signature.verify(decryptBASE64(sign));
    }

    public static byte[] encryptMD5(byte[] data) throws Exception {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update(data);
        return md5.digest();
    }

    public static byte[] encryptSHA(byte[] data) throws Exception {
        MessageDigest sha = MessageDigest.getInstance("SHA");
        sha.update(data);
        return sha.digest();
    }

    public static String getPublicKey(Map<String, Object> keyMap) throws Exception {
        Key key = (Key) keyMap.get("RSAPublicKey");
        return encryptBASE64(key.getEncoded());
    }

    public static String getPrivateKey(Map<String, Object> keyMap) throws Exception {
        Key key = (Key) keyMap.get("RSAPrivateKey");
        return encryptBASE64(key.getEncoded());
    }

    public static Map<String, Object> initKey() throws Exception {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        Map keyMap = new HashMap(2);
        keyMap.put("RSAPublicKey", publicKey);
        keyMap.put("RSAPrivateKey", privateKey);
        return keyMap;
    }

    public static byte[] encryptByPrivateKey(byte[] data, String key) throws Exception {
        byte[] keyBytes = decryptBASE64(key);
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(1, privateKey);
        return cipher.doFinal(data);
    }

    public static byte[] encryptByPublicKeyF(byte[] data, String publicKey) throws Exception {
        byte[] keyBytes = decryptBASE64(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key publicK = keyFactory.generatePublic(x509KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(1, publicK);
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        int i = 0;
        while (inputLen - offSet > 0) {
            byte[] cache;
            if (inputLen - offSet > 117)
                cache = cipher.doFinal(data, offSet, 117);
            else
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            out.write(cache, 0, cache.length);
            ++i;
            offSet = i * 117;
        }
        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptedData;
    }

    public static byte[] decryptByPrivateKeyF(byte[] encryptedData, String privateKey) throws Exception {
        byte[] keyBytes = decryptBASE64(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key privateK = keyFactory.generatePrivate(pkcs8KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(2, privateK);
        int inputLen = encryptedData.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        int i = 0;
        while (inputLen - offSet > 0) {
            byte[] cache;
            if (inputLen - offSet > 128)
                cache = cipher.doFinal(encryptedData, offSet, 128);
            else
                cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
            out.write(cache, 0, cache.length);
            ++i;
            offSet = i * 128;
        }
        byte[] decryptedData = out.toByteArray();
        out.close();
        return decryptedData;
    }

    public static byte[] decryptByPublicKey(byte[] data, String key) throws Exception {
        byte[] keyBytes = decryptBASE64(key);
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(2, publicKey);
        return cipher.doFinal(data);
    }

    public static Key getPublicKeyByString(String key) throws Exception {
        if (StringUtils.isBlank(key))
            LOGGER.error("key is null.");
        byte[] keyBytes = decryptBASE64(key);
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
        return publicKey;
    }

    public static byte[] encryptByPublicKey(byte[] data, String key) throws Exception {
        byte[] keyBytes = decryptBASE64(key);
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(1, publicKey);
        return cipher.doFinal(data);
    }

    public static byte[] decryptByPrivateKey(byte[] data, String key) throws Exception {
        byte[] keyBytes = decryptBASE64(key);
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(2, privateKey);
        return cipher.doFinal(data);
    }

    public static Key getPrivateKeyByString(String key) throws Exception {
        byte[] keyBytes = decryptBASE64(key);
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        return privateKey;
    }

    public static Map<String, String> getKeyMap(int keyNum) throws Exception {
        Map map = new HashMap();
        Security.addProvider(new BouncyCastleProvider());
        KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
        SecureRandom random = SecureRandom.getInstance("SHA1PRNG", "SUN");
        keyGen.initialize(keyNum, random);
        KeyPair pair = keyGen.generateKeyPair();
        PrivateKey priv = pair.getPrivate();
        PublicKey pub = pair.getPublic();
        String privateStr = encryptBASE64(priv.getEncoded());
        String publicStr = encryptBASE64(pub.getEncoded());
        map.put("publicKey", publicStr);
        map.put("privateKey", privateStr);
        return map;
    }

    public static String encreptData(String content, String pulicKey) throws Exception {
        return encreptData(content, pulicKey, "UTF-8");
    }

    public static String decreptData(String content, String privateKey) throws Exception {
        return decreptData(content, privateKey, "UTF-8");
    }

    public static String encreptData(String content, String pulicKey, String encoding) throws Exception {
        Cipher rsaCipher = null;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache = null;
        int i = 0;
        int inputLen = content.getBytes(encoding).length;
        rsaCipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        rsaCipher.init(1, getPublicKeyByString(pulicKey));
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > 117)
                cache = rsaCipher.doFinal(content.getBytes(encoding), offSet, 117);
            else
                cache = rsaCipher.doFinal(content.getBytes(encoding), offSet, inputLen - offSet);
            out.write(cache, 0, cache.length);
            ++i;
            offSet = i * 117;
        }
        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptBASE64(encryptedData);
    }

    public static String decreptData(String content, String privateKey, String encoding) throws Exception {
        Cipher rsaCipher = null;
        int offSet2 = 0;
        byte[] cache2 = null;
        int i2 = 0;
        byte[] contentByte = decryptBASE64(content);
        int inputLen2 = contentByte.length;
        ByteArrayOutputStream out2 = new ByteArrayOutputStream();
        rsaCipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        rsaCipher.init(2, getPrivateKeyByString(privateKey));
        while (inputLen2 - offSet2 > 0) {
            if (inputLen2 - offSet2 > 128)
                cache2 = rsaCipher.doFinal(contentByte, offSet2, 128);
            else
                cache2 = rsaCipher.doFinal(contentByte, offSet2, inputLen2 - offSet2);
            out2.write(cache2, 0, cache2.length);
            ++i2;
            offSet2 = i2 * 128;
        }
        byte[] decryptedData2 = out2.toByteArray();
        out2.close();
        return new String(decryptedData2, encoding);
    }

    public static String encryptByPublicKey(String source, String publicKey) throws Exception {
        Key key = getPublicKeyByString(publicKey);
        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        cipher.init(1, key);
        byte[] b = source.getBytes();
        byte[] b1 = cipher.doFinal(b);
        return new String(Base64.encodeBase64(b1), "UTF-8");
    }

    public static String encryptByPrivateKey(String source, String privateKey) throws Exception {
        Key key = getPrivateKeyByString(privateKey);
        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        cipher.init(1, key);
        byte[] b = source.getBytes();
        byte[] b1 = cipher.doFinal(b);
        return new String(Base64.encodeBase64(b1), "UTF-8");
    }

    public static String decryptByPrivateKey(String cryptograph, String privateKey) throws Exception {
        Key key = getPrivateKeyByString(privateKey);
        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        cipher.init(2, key);
        byte[] b1 = Base64.decodeBase64(cryptograph.getBytes());
        byte[] b = cipher.doFinal(b1);
        return new String(b);
    }

    public static String decryptByPublicKey(String cryptograph, String publishKey) throws Exception {
        Key key = getPublicKeyByString(publishKey);
        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        cipher.init(2, key);
        byte[] b1 = Base64.decodeBase64(cryptograph.getBytes());
        byte[] b = cipher.doFinal(b1);
        return new String(b);
    }
}
