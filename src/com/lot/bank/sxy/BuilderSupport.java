package com.lot.bank.sxy;

import com.alibaba.fastjson.JSONObject;

import java.lang.reflect.Field;

public class BuilderSupport {

    public JSONObject build(String merchantId) {
        JSONObject json = new JSONObject(true);
        json.put("merchantId", merchantId);
        return json;
    }

    protected JSONObject assembleBuild() throws Exception {
        JSONObject json;
        Class clazz = super.getClass();
        try {
            json = findField(clazz);
        } catch (Exception e) {
            throw new Exception(e);
        }
        return json;
    }

    private void putField(Class clazz, JSONObject json) throws IllegalAccessException {
        Field[] arrayOfField;
        int j = (arrayOfField = clazz.getDeclaredFields()).length;
        for (int i = 0; i < j; ++i) {
            Field field = arrayOfField[i];
            field.setAccessible(true);
            Object obj = field.get(this);
            if (obj == null)
                continue;
            json.put(field.getName(), obj);
        }
    }

    private JSONObject findField(Class clazz) throws IllegalAccessException {
        JSONObject json = new JSONObject();
        for (Class parent = clazz.getSuperclass(); (parent != null)
                && (parent != Object.class); parent = parent.getSuperclass()){
            putField(parent, json);
        }
        putField(clazz, json);
        return json;
    }

    protected String orderGenerateHmac() throws Exception {
        String hmacSource;
        try {
            hmacSource = SignPublisher.generateHmacSource(this);
        } catch (Exception e) {
            throw new Exception(e);
        }
        return hmacSource;
    }
}
