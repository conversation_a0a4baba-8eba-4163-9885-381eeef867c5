package com.lot.bank.abc;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.Socket;
import java.util.Iterator;

import org.apache.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import com.lot.bank.ChinaPayBank;
import com.lot.bank.bean.TCPIPCommBean;
import com.lot.bank.ws.PaymentPortType;
import com.lot.bank.ws.Payment_Service;
import com.lot.util.CommunicationUtil;
import com.lot.util.InterfaceUtil;
import com.lot.util.ParamCheck;
import com.lot.util.SendMessage;
import com.lot.util.ThreadPoolManager;

/**
 * 
 * <p>
 * @Title:
 * </p>
 * <p>
 * @Description:调用农业银行接口
 * </p>
 * <p>
 * @Copyright: Copyright (c) 2010 版权
 * </p>
 * <p>
 * @Company:
 * </P>
 *
 * @Auth zhaomingming
 * @version V1.0
 * @date 2013-8-20 下午04:42:53
 */
public class ABCBankService {

	private static Logger logger = Logger.getLogger(ABCBankService.class);
	

	
	
	private static String length(String str){
		
		
		if(str.length()<10){
			str="0"+str.length();
		}
		return str;
	}
	
	

    
    
	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: sendMsg
	 * @Description: 发送银行
	 * @Time: 2013-8-20 下午04:42:30
	 * @Return: AgriculBankBean
	 * @Throws:
	 */
	
	
public static ABCBankBean sendMsg1(ABCBankBean sendBean){  
	
	
	
	
	Payment_Service ft = new Payment_Service();
	PaymentPortType fs = ft.getPaymentHttpPort();
	 String result="";

     String errorCode = "";
     String message="";
	try{
	
	Document document = DocumentHelper.createDocument();
	document.setXMLEncoding("UTF-8");
	logger.info("开始组装参数");
	Element root = document.addElement("pay");
	Element trade = root.addElement("payment");



		
	String tradeNo = ParamCheck.str_null_to_empty(sendBean.getSystrace());//提交到银行的订单号
	
	String bankcode=ParamCheck.str_null_to_empty("P001");
	String amount = ParamCheck.str_null_to_empty(sendBean.getTxnamount());//提现金额    取交易表中的支出金额的绝对值
    String cardno=ParamCheck.str_null_to_empty(sendBean.getPan());
	String expiredate=ParamCheck.str_null_to_empty(sendBean.getExpdate());
	
	String cvv=ParamCheck.str_null_to_empty(sendBean.getCvv2());//
    String currency = ParamCheck.str_null_to_empty(sendBean.getCurcode());//

	
	String mid = ParamCheck.str_null_to_empty(sendBean.getMid());
	String tid = ParamCheck.str_null_to_empty(sendBean.getTid());
	String procode = ParamCheck.str_null_to_empty(sendBean.getProccode());
    
    String condCode=ParamCheck.str_null_to_empty(sendBean.getCondcode());//
    
    
    
    String is3d=ParamCheck.str_null_to_empty(sendBean.getThreedf());
	 String  xid=ParamCheck.str_null_to_empty(sendBean.getXid());
	 String eci=ParamCheck.str_null_to_empty(sendBean.getEci());
	 String cavv=ParamCheck.str_null_to_empty(sendBean.getCavv());
	 String  cvv2=ParamCheck.str_null_to_empty(sendBean.getCvv2());
	 String  avs=ParamCheck.str_null_to_empty(sendBean.getAvsaddr());
	 String code=ParamCheck.str_null_to_empty(sendBean.getAvspostcode());
	 String refference="";
  
	 StringBuffer yu=new StringBuffer();
		if ("1".equals(is3d)) {
			yu.append("3DF011");
		} else {
			yu.append("3DF010");
		}
		if ("".equals(xid)) {
	
			
			refference=sendBean.getSystrace();
			yu.append("XID20"+refference);
		} else {
			yu.append("XID"+ABCBankService.length(xid)+xid);
		}
		if ("".equals(eci)) {
			yu.append("ECI00");
		} else {
			yu.append("ECI02"+eci);
		}
		if ("".equals(cavv)) {
			yu.append("CAV00");
		} else {
		
			yu.append("CAV"+ABCBankService.length(cavv)+cavv);
		}
		if ("".equals(cvv2)) {
			yu.append("CV200");
		} else {
			yu.append("CV2"+ABCBankService.length(cvv2)+cvv2);
		}
		if ("".equals(avs)) {
			yu.append("ADR00");
		} else {
			yu.append("ADR"+ABCBankService.length(avs)+avs);
		}
		if ("".equals(code)) {
			yu.append("APC00");
		} else {
			yu.append("APC"+ABCBankService.length(code)+code);
		}

		 
		String info = sendBean.getBankPayUrl();
       
        
    String ipaddress=ParamCheck.str_null_to_empty(info.split(",")[0].trim());	//
   
    

	
	
	      String ipport=ParamCheck.str_null_to_empty(info
             .split(",")[1].trim());//
	    String adddata=ParamCheck.str_null_to_empty(yu.toString()+"IPD00");
	    
	    
	    
		logger.info("ipport="+ipport);
		logger.info("ipaddress="+ipaddress);
		logger.info("adddata="+adddata);
		logger.info("amount="+amount);
		logger.info("tradeNo="+tradeNo);
		
		logger.info("expiredate="+expiredate);

		logger.info("bankcode="+bankcode);
		logger.info("refference="+refference);
	
		//ipaddress="*************";
		//ipport="11651";
    trade.addElement("bankcode").addText(bankcode);
	trade.addElement("tradeNo").addText(tradeNo);

	trade.addElement("amount").addText(amount);

	trade.addElement("cardno").addText(cardno);
	trade.addElement("expiredate").addText(expiredate);

	trade.addElement("cvv").addText(cvv);
	trade.addElement("currency").addText(currency);
	trade.addElement("mid").addText(mid);
	trade.addElement("tid").addText(tid);
	trade.addElement("procode").addText(procode);
	trade.addElement("adddata").addText(adddata);
	trade.addElement("ipaddress").addText(ipaddress);
	trade.addElement("ipport").addText(ipport);
	trade.addElement("condCode").addText(condCode);
	

	String xmlParams = document.asXML();
	//logger.info("xmlParams = " + xmlParams);
	
	 result=ParamCheck.str_null_to_empty(fs.payment(xmlParams));



	logger.info("result = " + result);
	
	}catch (NoClassDefFoundError ec) {
		message = "流水订单号：" + sendBean.getSystrace() + ";银行代码：ABC;" + "交易发送银行前置失败NoClassDefFoundError!";
        logger.error(message);
    	logger.error(InterfaceUtil.getExceptionInfo(ec));
    	ThreadPoolManager.getsInstance(0,1,0L).execute(new SendMessage(message, ABCBankService.class));
       // new Thread(new SendMessage(message, ABCBankService.class)).start();
	
		errorCode = "CE";
	} catch (Exception e) {
		message = "流水订单号：" + sendBean.getSystrace() + ";银行代码：ABC;" + "交易发送银行前置失败Exception!";
        logger.error(message);
    	logger.error(InterfaceUtil.getExceptionInfo(e));
    	ThreadPoolManager.getsInstance(0,1,0L).execute(new SendMessage(message, ABCBankService.class));
        //new Thread(new SendMessage(message, ABCBankService.class)).start();
		errorCode = "CE";
	}

	
	
	
       
		ABCBankBean agr = new ABCBankBean();
		agr.setRespcode(errorCode);
		if(!"".equals(result)){
			//解析返回的信息		
			agr  = getData1(result);
		}else{
			
			errorCode = "RRE";
			agr.setRespcode(errorCode);
			logger.info("银行返回结果为空=== "   );
		}
		
		return agr;
	}




	public static ABCBankBean sendMsg(ABCBankBean sendBean){   	
		
    	//构造发送参数
    	Document document = DocumentHelper.createDocument();
        document.setXMLEncoding("GB2312");
        Element root = document.addElement("ap");
        root.addElement("msg_type").addText(sendBean.getMsgtype()) ;
        root.addElement("pan").addText(sendBean.getPan()) ;                   			 // 卡号 
        root.addElement("proc_code").addText(sendBean.getProccode()) ;         			 // 处理码　 
        root.addElement("txn_amt").addText(sendBean.getTxnamount()) ;               	 //交易金额 以分为单位　
        root.addElement("cur_code").addText(sendBean.getCurcode()) ;               		 //币种，不传默认用156
        root.addElement("systrace").addText(sendBean.getSystrace()) ;               	 //流水号，需保证唯一
        root.addElement("exp_date").addText(sendBean.getExpdate()) ;               		 //卡有效期
        root.addElement("cvv").addText(sendBean.getCvv()) ;               				 //cvv校验码
        root.addElement("cond_code").addText(sendBean.getCondcode()) ;               	 //条件码
        root.addElement("func_code").addText(sendBean.getFunctioncode()) ;               //功能码
        root.addElement("mid").addText(sendBean.getMid()) ;               	//商户号　
        root.addElement("tid").addText(sendBean.getTid()) ;               	//终端号
        root.addElement("df").addText(ParamCheck.str_null_to_empty(sendBean.getThreedf())) ;             //必输项 1表示3D交易，其他非3D交易　
        root.addElement("xid").addText(ParamCheck.str_null_to_empty(sendBean.getXid())) ;               	//最大长度2   3D交易时必输 
        root.addElement("eci").addText(ParamCheck.str_null_to_empty(sendBean.getEci())) ;               	//最大长度2   3D交易时必输 
        root.addElement("cavv").addText(ParamCheck.str_null_to_empty(sendBean.getCavv())) ;               //最大长度28  3D交易时必输
        root.addElement("cvv2").addText(ParamCheck.str_null_to_empty(sendBean.getCvv2())) ;				// 最大长度3   选项输
        root.addElement("avsaddr").addText(ParamCheck.str_null_to_empty(sendBean.getAvsaddr())) ;			// 最大长度40  AVS服务地址 选项输
        root.addElement("avspostcode").addText(ParamCheck.str_null_to_empty(sendBean.getAvspostcode())) ;                      // 最大长度9   AVS代码  选项输
        
    	String xml = root.asXML();
    	xml ="0" + xml.length() + xml ;     // 长度值4位，　不够前补0　
		Socket socket = null;
		BufferedReader br = null;
		//DataOutputStream out = null;
		PrintWriter out=null;
        String message = null;
        String result = "";
        String errorCode = "";
		
		CommunicationUtil commUtil = new CommunicationUtil();
		String info = sendBean.getBankPayUrl();
        TCPIPCommBean commBean = new TCPIPCommBean(info.split(",")[0].trim(),Integer.parseInt(info
                .split(",")[1].trim()),xml);
        commBean.setConnectTimeOut(commUtil.getBOC_Conn_TimeOut());	//设置连接超时时间
    	commBean.setReadTimeOut(commUtil.getBOC_Read_TimeOut()); // 设置读取超时时间		
    	try{
	    	//**建立连接并发送数据**//*
			logger.info("agricultural send start:" + "systrace:"+ sendBean.getSystrace() + ", txn_amt:"+ sendBean.getTxnamount() + ", xid:"+ sendBean.getXid() + ", eci:" + sendBean.getEci());						
    		socket = commBean.getConnection();
    		result = ParamCheck.str_null_to_empty(commBean.sendMessage(socket,out,br));
    		//logger.info("result = " + result  );
    		  if("out".equals(result)){
    			  errorCode = "RRE";
    		  }
			
    	}catch(IOException e){
            // 日志、发送短信内容
			message = "流水订单号：" + sendBean.getSystrace() + ";银行代码：ABC;" + "交易发送银行失败!";
            logger.error(message);
            logger.error(InterfaceUtil.getExceptionInfo(e));
            
            ThreadPoolManager.getsInstance(0,1,0L).execute(new SendMessage(message, ABCBankService.class));
            //new Thread(new SendMessage(message, ABCBankService.class)).start();
            //区分连接异常和读写异常设置错误信息
            errorCode = "CE";
            if(e instanceof java.net.SocketTimeoutException){
            	errorCode = "RE";
            }
            if(e instanceof java.net.ConnectException){
            	errorCode = "CE";
            }
    	}finally {
            commUtil.closeSocket(socket, br, out); // 关闭socket连接和IO流
        }
		ABCBankBean agr = new ABCBankBean();
		agr.setRespcode(errorCode);
		if(!"".equals(result)){
		if(result.length() >= 4) {
			String res = result.substring(4, result.length());
			//解析返回的信息		
			agr  = getData(res);
		}
		}else{
			logger.info("银行返回结果为空=== "   );
		}
		return agr;
	}
	

private static boolean isXML(String xml) {
	return !(xml == null || "".equals(xml.trim())) && xml.contains("<?xml");
}
	private static  ABCBankBean getData1(String xml) {
		ABCBankBean bankReturnBean = null;
	
		if (isXML(xml)) {
			// 取值

			// 转换文本格式
			Document document;
			try {
				document = DocumentHelper.parseText(xml);

				// 迭代循环读取元素
				Iterator iterator = document.selectNodes("//pay/payment")
						.iterator();
				while (iterator.hasNext()) {
					// k++;
					// 获取元素值
					Element element = (Element) iterator.next();
					String tradeNo = ParamCheck.str_null_to_empty(element
							.elementText("tradeNo"));
					String bankTradeNo = ParamCheck
							.str_null_to_empty(element
									.elementText("bankTradeNo"));
					String responsecode = ParamCheck.str_null_to_empty(element
							.elementText("responsecode"));
					
					String authcode = ParamCheck.str_null_to_empty(element
							.elementText("authcode"));
					
					String amount = ParamCheck.str_null_to_empty(element
							.elementText("amount"));
					
					
					String errorCode = ParamCheck.str_null_to_empty(element
							.elementText("errorCode"));
					String msg = ParamCheck.str_null_to_empty(element
							.elementText("msg"));
				
					
					logger.info("解析responseXML得到的数据开始........................");
					logger.info("tradeNo = " + tradeNo);
					logger.info("bankTradeNo = " + bankTradeNo);
					logger.info("authcode = " + authcode);
					logger.info("amount = " + amount); // 
				
			
					logger.info("responsecode = " + responsecode);// 1、成功 2、处理中 3、失败
					logger.info("errorCode = " + errorCode);
					logger.info("msg = " + msg);
				
					logger.info("解析responseXML得到的数据结束........................");
					
					
					if("I03".equals(responsecode)){//说明连接超时
					    String	message = "流水订单号：" + tradeNo + ";银行代码：ABC;" + "交易发送银行超时!";
				        logger.error(message);
				        responsecode="RE";
				        ThreadPoolManager.getsInstance(0,1,0L).execute(new SendMessage(message, ABCBankService.class));
				        //new Thread(new SendMessage(message, ABCBankService.class)).start();
						
					}
					if("I02".equals(responsecode)){//说明发送银行异常
					    String	message = "流水订单号：" + tradeNo + ";银行代码：ABC;" + "交易发送银行异常I02!";
				        logger.error(message);
				        responsecode="CE";
				        ThreadPoolManager.getsInstance(0,1,0L).execute(new SendMessage(message, ABCBankService.class));
				        //new Thread(new SendMessage(message, ABCBankService.class)).start();
						
					}
					
					if("".equals(amount)){
						amount="0";
					}
					bankReturnBean = new ABCBankBean();
					bankReturnBean.setTxnamount(amount);
					bankReturnBean.setSystrace(tradeNo);
					bankReturnBean.setBatchno(bankTradeNo);
					bankReturnBean.setRespcode(responsecode);
					bankReturnBean.setAuthcode(authcode);
				
					
				}
			} catch (DocumentException e) {
				bankReturnBean=null;
				logger.error(InterfaceUtil.getExceptionInfo(e));
			}

		}

		return bankReturnBean;
	}
	
	
	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: getData
	 * @Description: 解析返回信息
	 * @Time: 2013-8-21 上午09:30:37
	 * @Return: AgriculBankBean
	 * @Throws:
	 */
	private static  ABCBankBean getData(String res) {
		// TODO Auto-generated method stub
		ABCBankBean agr = new ABCBankBean() ;
		try {
			res = res.replace("3df", "df");
			Document document = DocumentHelper.parseText(res);
			Element root = document.getRootElement();
			
			agr.setBatchno(root.element("batchno").getText()) ;
			agr.setRespcode(root.element("resp_code").getText()) ;
			agr.setMsgtype(root.element("msg_type").getText()) ;
			agr.setPan(root.element("pan").getText()) ;
			
			agr.setProccode(root.element("proc_code").getText()) ;
			agr.setTxnamount(root.element("txn_amt").getText()) ;
			agr.setSystrace(root.element("systrace").getText()) ;
			agr.setExpdate(root.element("exp_date").getText());
			agr.setCvv(root.element("cvv").getText());
			
			agr.setCondcode(root.element("cond_code").getText());
			agr.setFunctioncode(root.element("func_code").getText());
			agr.setMid(root.element("mid").getText());
			agr.setTid(root.element("tid").getText());
			agr.setCurcode(root.element("cur_code").getText());
			if(root.element("dcc") != null){
				agr.setDcc(root.element("dcc").getText());
			}
			if(root.element("auth_code") != null){
				agr.setAuthcode(root.element("auth_code").getText());
			}
			logger.info("systrace:" + agr.getSystrace() +", batchno:"+agr.getBatchno() + ", respcode:"+ agr.getRespcode() +", dcc:"+agr.getDcc() + ",authcode:" + agr.getAuthcode());
			
		} catch (DocumentException e) {
			logger.error("解析农行返回信息异常");
			logger.error(InterfaceUtil.getExceptionInfo(e));
		}
		
	    return agr;
	}
	
	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: showErrorMsg
	 * @Description: 错误信息
	 * @Time: 2013-8-21 下午04:15:04
	 * @Return: String
	 * @Throws:
	 */
	public static String showErrorMsg(String code) {
		// TODO Auto-generated method stub
		String msg = "";    
	    if (null==code || "CE".equals(code)) {
	        msg = "failed";
	    } else if ("RE".equals(code)) {
	        msg = "failed";
	    }else if (code.equalsIgnoreCase("RRE")) {
	    	msg = "failed ";
	    }else if (code.equalsIgnoreCase("CS")) {
	    	msg = "failed";
	    } else if (code.equalsIgnoreCase("00")) {
	    	msg = "Approved";		//交易成功
	    } else if (code.equalsIgnoreCase("01")) {
	        msg = "Refer to card issuer";		//请联系发卡银行
	    } else if (code.equalsIgnoreCase("02")) {
	        msg = "Refferral";		//电话授权
	    } else if (code.equalsIgnoreCase("03")) {
	    	msg = "Invalid merchant"; //无效商户
	    }else if (code.equalsIgnoreCase("04")) {
	        msg = "Pickup Card"; //没收卡
	    }else if (code.equalsIgnoreCase("05")) {
	        msg = "Declined"; 	//此交易被拒绝，请联系银行
	    } else if (code.equalsIgnoreCase("07")) {
	        msg = "Transaction cannot be completed"; 	//不成功
	    } else if (code.equalsIgnoreCase("12")) {
	        msg = "Invalid Transaction";	//无效交易
	    } else if (code.equalsIgnoreCase("13")) {
	        msg = "Invalid amount"; 	//无效金额
	    } else if (code.equalsIgnoreCase("14")) {
	        msg = "Invalid card number";	//无效卡号
	    }else if(code.equalsIgnoreCase("15")){
	        msg="Invalid Issuer";		//无发卡行
	    }else if(code.equalsIgnoreCase("21")){
	        msg="Transaction cannot be completed";  // 不成功
	    }else if(code.equalsIgnoreCase("23")){
	        msg="Transaction cannot be completed"; 	// 不成功
	    }else if(code.equalsIgnoreCase("25")){
	        msg="Unable to locate record on file"; // 找原交易失败
	    }else if(code.equalsIgnoreCase("30")){
	        msg="Format error";			//格式错误
	    }else if (code.equalsIgnoreCase("33")) {
	        msg = "Expired card"; 	//过期卡
	    } else if (code.equalsIgnoreCase("38")) {
	        msg = "Exceeds PIN Retry"; //密码超限
	    } else if (code.equalsIgnoreCase("41")) {
	        msg = "Lost Card";		//挂失卡
	    } else if (code.equalsIgnoreCase("43")) {
	        msg = "Stolen Card"; 	//被盗卡
	    } else if (code.equalsIgnoreCase("51")) {
	        msg = "Not sufficient funds"; 		//可用余额不足
	    } else if (code.equalsIgnoreCase("54")) {
	        msg = "Expired card"; //过期的卡
	    } else if (code.equalsIgnoreCase("55")) {
	        msg = "Incorrect PIN"; //不正确的PIN
	    }else if (code.equalsIgnoreCase("57")) {
	        msg = "Transaction not permitted on card"; //不允许持卡人进行的交易
	    } else if (code.equalsIgnoreCase("58")) {
	        msg = "Txn Not Permitted On Terminal"; //不允许终端进行的交易
	    }else if (code.equalsIgnoreCase("61")) {
            msg = "Exceeds withdrawal amount limit"; //单笔金额超出金额限制
        }else if (code.equalsIgnoreCase("64")) {
	        msg = "Original Transaction amount incorrect"; //原交易金额不正确
	    } else if (code.equalsIgnoreCase("65")) {
	        msg = "Exceeds frequency limit"; //超出取现次数
	    }else if (code.equalsIgnoreCase("75")) {
	        msg = "Exceeds PIN Retry"; //允许的密码输入次数超限
	    } else if (code.equalsIgnoreCase("76")) {
	        msg = "Manual key in not allowed"; //不允许手工输卡号
	    } else if (code.equalsIgnoreCase("77")) {
	        msg = "Invalid Business Date"; //结算不平/
	    } else if (code.equalsIgnoreCase("78")) {
	        msg = "Certification No. error"; //凭证号错误
	    }else if (code.equalsIgnoreCase("80")) {
	        msg = "Batch No.error"; //批次号错误
	    }else if (code.equalsIgnoreCase("82")) {
	        msg = "Authorization No. error"; // 授权码错误
	    }else if (code.equalsIgnoreCase("86")) {
	        msg = "Original Transaction not match"; // 原交易已改变
	    }  else if (code.equalsIgnoreCase("87")) {
	        msg = "No response from card issuer"; //没有收到发卡行应答
	    }else if (code.equalsIgnoreCase("89")) {
	        msg = "Invalid Terminal"; //无效终端
	    }else if (code.equalsIgnoreCase("90")) {
	        msg = "Date switch in progress";//日期切换正在处理
	    } else if (code.equalsIgnoreCase("91")) {
	        msg = "Cutoff in progress";//正在日切
	    } else if (code.equalsIgnoreCase("92")) {
	        msg = "Cars issuer or switch Unavailable";//发卡行或交换中心不可用
	    } else if (code.equalsIgnoreCase("93")) {
	        msg = "Transaction can not complete";//不成功
	    } else if (code.equalsIgnoreCase("94")) {
	        msg = "Duplicate Transaction";//交易重复
	    }else if (code.equalsIgnoreCase("95")) {
	        msg = "Unable to route transaction";//对账不平 ，批上送
	    } else if (code.equalsIgnoreCase("96")) {
	        msg = "Issuer Bank Declined";//system-down系统故障 2021 改为 Issuer Bank Declined
	    } else {
	        msg = "Transaction was blocked by the Payment Server because it did not pass all fraud checks. "; //未知错误
	    }
	    return msg;
	}
}
