package com.lot.bank.abc;

public class ABCBankBean {

	private String msgtype;				//消息类型
	
	private String pan;			
	
	private String proccode;			//处理码
	
	private String txnamount;			//交易金额 以分为单位
	
	private String curcode;				// 币种，不传默认用156
	
	private String systrace;			//流水号，需保证唯一
	
	private String expdate;			
	
	private String cvv;
	
	private String condcode;			//条件码
	
	private String functioncode;		//功能码
	
	private String mid;					//商户号
	
	private String tid;					//终端号
	
	private String threedf;				//必输项 1表示3D交易，其他非3D交易
	
	private String xid;
	
	private String eci;					//最大长度2   3D交易时必输 
	
	private String cavv;				// 最大长度28  3D交易时必输
	
	private String cvv2;				//最大长度3   选项输
	
	private String avsaddr;				//最大长度40  AVS服务地址 选项输
	
	private String avspostcode;			//最大长度9   AVS代码  选项输
	
	private String batchno;				//返回批次号
	
	private String respcode;			//返回代码
	
	private String dcc;

	private String authcode;			//授权码
	
	//** 银行支付地址 **//*
	private String bankPayUrl;
	
	public String getMsgtype() {
		return msgtype;
	}

	public void setMsgtype(String msgtype) {
		this.msgtype = msgtype;
	}

	public String getPan() {
		return pan;
	}

	public void setPan(String pan) {
		this.pan = pan;
	}

	public String getProccode() {
		return proccode;
	}

	public void setProccode(String proccode) {
		this.proccode = proccode;
	}

	public String getTxnamount() {
		return txnamount;
	}

	public void setTxnamount(String txnamount) {
		this.txnamount = txnamount;
	}

	public String getCurcode() {
		return curcode;
	}

	public void setCurcode(String curcode) {
		this.curcode = curcode;
	}

	public String getSystrace() {
		return systrace;
	}

	public void setSystrace(String systrace) {
		this.systrace = systrace;
	}

	public String getExpdate() {
		return expdate;
	}

	public void setExpdate(String expdate) {
		this.expdate = expdate;
	}

	public String getCvv() {
		return cvv;
	}

	public void setCvv(String cvv) {
		this.cvv = cvv;
	}

	public String getCondcode() {
		return condcode;
	}

	public void setCondcode(String condcode) {
		this.condcode = condcode;
	}

	public String getFunctioncode() {
		return functioncode;
	}

	public void setFunctioncode(String functioncode) {
		this.functioncode = functioncode;
	}

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	public String getThreedf() {
		return threedf;
	}

	public void setThreedf(String threedf) {
		this.threedf = threedf;
	}

	public String getXid() {
		return xid;
	}

	public void setXid(String xid) {
		this.xid = xid;
	}

	public String getEci() {
		return eci;
	}

	public void setEci(String eci) {
		this.eci = eci;
	}

	public String getCavv() {
		return cavv;
	}

	public void setCavv(String cavv) {
		this.cavv = cavv;
	}

	public String getCvv2() {
		return cvv2;
	}

	public void setCvv2(String cvv2) {
		this.cvv2 = cvv2;
	}

	public String getAvsaddr() {
		return avsaddr;
	}

	public void setAvsaddr(String avsaddr) {
		this.avsaddr = avsaddr;
	}

	public String getAvspostcode() {
		return avspostcode;
	}

	public void setAvspostcode(String avspostcode) {
		this.avspostcode = avspostcode;
	}

	public void setBatchno(String batchno) {
		this.batchno = batchno;
	}

	public String getBatchno() {
		return batchno;
	}

	public void setRespcode(String respcode) {
		this.respcode = respcode;
	}

	public String getRespcode() {
		return respcode;
	}

	public void setDcc(String dcc) {
		this.dcc = dcc;
	}

	public String getDcc() {
		return dcc;
	}

	public void setAuthcode(String authcode) {
		this.authcode = authcode;
	}

	public String getAuthcode() {
		return authcode;
	}

	public String getBankPayUrl() {
		return bankPayUrl;
	}

	public void setBankPayUrl(String bankPayUrl) {
		this.bankPayUrl = bankPayUrl;
	}
	

}
