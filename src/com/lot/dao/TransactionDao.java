package com.lot.dao;

import com.lot.bean.RefundInBean;
import com.lot.bean.RefundOutBean;

/**
 * 
 * <p>
 * @Title:
 * </p>
 * <p>
 * @Description:交易接口处理
 * </p>
 * <p>
 * @Copyright: Copyright (c) 2010 版权
 * </p>
 * <p>
 * @Company:
 * </P>
 *
 * @Auth zhaomingming
 * @version V1.0
 * @date 2013-8-26 下午06:04:49
 */
public interface TransactionDao {
	
	/**
	 * 申请退款，直接退款到银行
	 * 
	 * @author: <PERSON><PERSON><PERSON>
	 * @Title applyrefundInterface
	 * @Time: 2014-6-18下午04:03:46
	 * @Description: 
	 * @return: RefundOutBean 
	 * @throws: 
	 * @param refundIn
	 * @param remoteIp
	 * @return
	 * @throws SystemException
	 */
	public RefundOutBean realTimeRefundBean(RefundInBean refundIn,
			String remoteIp);
}
