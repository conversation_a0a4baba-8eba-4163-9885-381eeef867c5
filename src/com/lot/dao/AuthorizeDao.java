package com.lot.dao;

import java.util.List;

import com.lot.bean.ParamBean;
import com.lot.bean.ReturnBean;
import com.lot.bean.ScbFunctionBean;



 /**
  * 预授权接口数据操作类
  * <p>Title: </p>
  * <p>Description: 描述该类或文件功能</p>
  * <p>Copyright: Copyright (c) 2011 版权</p>
  * <p>Company: </p>
  * <AUTHOR>
  * @version V1.0 
  * @date 2012-9-6上午09:45:30
  */
public interface AuthorizeDao{
	/**
	 * 保存预授权处理bean
	 * @author: guozb
	 * @Title saveAuthDeal
	 * @Time: 2012-9-6上午09:47:55
	 * @Description: 
	 * @return: ReturnBean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public boolean saveAuthDeal(ParamBean paramBean); 
	
	/**
	 * 得到网关接入信息
	 * @author: guozb
	 * @Title getMerGateWayInfo
	 * @Time: 2012-9-11上午11:46:45
	 * @Description: 
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	public void getMerGateWayInfo(ParamBean paramBean) ;
	
	/**
	 * 校验流水订单号
	 * @author: guozb
	 * @Title chekcTradeNoInfo
	 * @Time: 2012-9-7上午10:41:47
	 * @Description: 
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	public void chekcTradeNoInfo(ParamBean paramBean) ;
	
	/**
	 * 保存挡掉的信息
	 * @author: guozb
	 * @Title saveUnAuthDeal
	 * @Time: 2012-9-7上午11:20:58
	 * @Description: 
	 * @return: ReturnBean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public ReturnBean saveUnAuthDeal(ParamBean paramBean,ReturnBean returnBean); 
	
	/**
	 * 根据交易流水号得到查询授权day的数据
	 * @author: guozb
	 * @Title getTradeInfoForAuth
	 * @Time: 2012-9-7下午12:22:55
	 * @Description: 
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	public void getTradeInfoForAuth(ParamBean paramBean) ;
	
	/**
	 * 获取授权天数
	 * @author: guozb
	 * @Title getTAuthDay
	 * @Time: 2012-9-7下午02:13:29
	 * @Description: 
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	public void getTAuthDay(ParamBean paramBean) ;
	
	 /**
	  * 更改交易表
	  * @author: guozb
	  * @Title changeTrade
	  * @Time: 2012-9-10下午03:06:20
	  * @Description: 
	  * @return: ReturnBean 
	  * @throws: 
	  * @param paramBean
	  * @return
	  */
	public boolean changeTrade(ParamBean paramBean,int status);
	
	/**
	 * 撤销授权  
	 * 查询引用流水订单号
	 * @author: guozb
	 * @Title getRefNoByCXAuth
	 * @Time: 2012-9-11下午02:56:53
	 * @Description: 
	 * @return: String 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public List<String>  getRefNoByCXAuth(ParamBean paramBean);
	
	/**
	 * 交易表中查询 终端号
	 * @author: guozb
	 * @Title getAuthCodeByComAuth
	 * @Time: 2012-9-12下午03:18:46
	 * @Description: 
	 * @return: String 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public String getAuthCodeByComAuth(ParamBean paramBean);
	
	/**
	 * 查询信息
	 * @author: guozb
	 * @Title getAuthInfo
	 * @Time: 2012-9-12下午05:25:57
	 * @Description: 
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	public void getAuthInfo(ParamBean paramBean) ;
	/**
	 * 查询渣打辅助信息
	 * @author: guozb
	 * @Title getScbFunctionBean
	 * @Time: 2013-3-25下午04:17:54
	 * @Description: 
	 * @return: ScbFunctionBean 
	 * @throws: 
	 * @param paramBean
	 * @param type
	 * @return
	 */
	public ScbFunctionBean getScbFunctionBean(ParamBean paramBean,  String type) ;
	
	
	public String queryVerCode(String telesign);
}
