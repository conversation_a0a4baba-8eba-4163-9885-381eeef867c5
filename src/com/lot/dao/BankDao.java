package com.lot.dao;

import java.util.List;
import java.util.Map;

import com.lot.bank.bean.BankReturnBean;
import com.lot.bank.motopn.bean.RateObj;
import com.lot.bank.motopn.bean.SalesObj;
import com.lot.bean.Moto3DBean;
import com.lot.bean.ParamBean;
import com.lot.bean.ReturnBean;
import com.lot.bean.WebMoneyShopBean;

/**
 * 
 * <p>Title: </p>
 * <p>Description: 银行数据操作类</p>
 * <p>Copyright: Copyright (c) 2011 版权</p>
 * <p>Company: </p>
 * <AUTHOR>
 * @version V1.0 
 * @date 2011-6-27下午03:22:51
 */
public interface BankDao{
	
	/**
	 * 
	 * @author: kevin
	 * @Title getBankChannel
	 * @Time: 2011-8-4下午03:39:08
	 * @Description: 根据通道代码,获取通道以及银行的信息
	 * @return: ReturnBean 
	 * @throws: 
	 * @param bankReturnBean
	 * @param channelCode
	 */
	public ReturnBean getBankChannel(BankReturnBean bankReturnBean,String channelCode);
	

	/**
	 * 
	 * @author: peifang
	 * @Title getTradeInfoByTrReference
	 * @Time: 2012-2-7下午02:13:41
	 * @Description: 根据银行返回交易序列号获取信息 
	 * @return: void 
	 * @throws: 
	 * @param bankReturnBean
	 * @param trReference
	 */
	public void getTradeInfoByTrReference(BankReturnBean bankReturnBean,String trReference);
	
	/**
	 * 
	 * @author: kevin
	 * @Title updateTradeRecord
	 * @Time: 2011-8-5上午10:30:26
	 * @Description: 接收银行返回修改交易记录表
	 * @return: boolean 
	 * @throws: 
	 * @param bankReturnBean
	 * @return
	 */
	public boolean updateTradeRecord(BankReturnBean bankReturnBean);


	/**
	 * 
	 * @author: kevin
	 * @Title getDomainInfo
	 * @Time: 2011-8-8下午05:14:46
	 * @Description: 获取发送邮件域名信息
	 * @return: void 
	 * @throws: 
	 * @param bankReturnBean
	 * @return
	 */
	public void getDomainInfo(BankReturnBean bankReturnBean);
	
	/**
	 * 
	 * @author: kevin
	 * @Title sendEmail
	 * @Time: 2011-8-9下午02:02:18
	 * @Description: 发送邮件
	 * @return: void 
	 * @throws: 
	 * @param bankReturnBean
	 */
	public void sendEmail(BankReturnBean bankReturnBean);
	
	/**
	 * 
	 * @author: kevin
	 * @Title saveMotoTradeDiffInfo
	 * @Time: 2012-5-29下午05:26:37
	 * @Description: 保存moto vpn交易信息
	 * @return: void 
	 * @throws: 
	 * @param sl
	 * @param ro
	 */
	public void saveMotoTradeDiffInfo(SalesObj sl, RateObj ro);
	
	/**
	 * 
	 * @author: kevin
	 * @Title getMotoTradeDiffInfoByTradeNo
	 * @Time: 2012-5-29下午02:29:56
	 * @Description: 根据流水订单号取得
	 * @return: RateObj 
	 * @throws: 
	 * @param tradeNo
	 * @return
	 */
	public RateObj getMotoTradeDiffInfoByTradeNo(String tradeNo);
	
	/**
	 * 
	 * @author: kevin
	 * @Title getChannelByFlag
	 * @Time: 2012-5-29下午06:20:37
	 * @Description: 根据通道的标识获取另一通道的值
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	public boolean getChannelByFlag(ParamBean paramBean,int chaType);
	
	/**
	 * 
	 * @author: kevin
	 * @Title updateTradeRecordChannelCode
	 * @Time: 2012-5-29下午06:52:55
	 * @Description: 修改交易记录表中的通道代码
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	public void updateTradeRecordChannelCode(ParamBean paramBean);
	
	/**
	 * 
	 * @author: kevin
	 * @Title updateMotoTradeDiffInfo
	 * @Time: 2012-5-30上午10:18:41
	 * @Description:  修改motovpn表
	 * @return: void 
	 * @throws: 
	 * @param sl
	 * @param isDcc
	 */
	public void updateMotoTradeDiffInfo(SalesObj sl,String isDcc);
	
	/**二次支付
	 * 根据商户号查询网关接入号是否支持二次支付
	 * @author: guozb
	 * @Title getPayFlagByGwNo
	 * @Time: 2012-10-29下午04:46:30
	 * @Description: 
	 * @return: String 
	 * @throws: 
	 * @param gwNo
	 * @return
	 */
	public int getPayFlagByGwNo  (String gwNo ) ;
	
	public List getPayTypeForSecPay  (BankReturnBean bankReturnBean) ;
	/**
	 * 二次支付
	 * 更改第一笔交易为 二次付款状态
	 * @author: guozb
	 * @Title updatePreTradeRecord
	 * @Time: 2012-10-25上午11:21:54
	 * @Description: 
	 * @return: boolean 
	 * @throws: 
	 * @param bankReturnBean
	 * @return
	 */
	public boolean updatePreTradeRecord(BankReturnBean bankReturnBean);
	
	/**
	 * 二次支付
	 * 根据流水订单号查询要二次传给银行的相关信息
	 * @author: guozb
	 * @Title getSecondParamBeanInfo
	 * @Time: 2012-10-26上午10:45:40
	 * @Description: 
	 * @return: ParamBean 
	 * @throws: 
	 * @param paramBean
	 * @param trNo
	 * @return
	 */
	public ParamBean getSecondParamBeanInfo(  BankReturnBean bankReturnBean);
	/**
	 * 二次支付保存交易记录表
	 * @author: guozb
	 * @Title saveSecondTradeRecord
	 * @Time: 2012-10-27上午10:04:55
	 * @Description: 
	 * @return: boolean 
	 * @throws: 
	 * @param oldTrNo
	 * @param newTrNo
	 * @return
	 */
	public boolean saveSecondTradeRecord(String oldTrNo,String newTrNo) ;
	/**
	 * 二次支付保存持卡人信息表
	 * @author: guozb
	 * @Title saveSecondCreditInfo
	 * @Time: 2012-10-27上午10:05:23
	 * @Description: 
	 * @return: boolean 
	 * @throws: 
	 * @param oldTrNo
	 * @param newTrNo
	 * @return
	 */
	public boolean saveSecondCreditInfo(String oldTrNo,String newTrNo) ;
	/**
	 * 二次支付 更新交易表中的bankcode，channecode
	 * @author: guozb
	 * @Title UpdateSecondTrade
	 * @Time: 2012-10-27上午10:29:16
	 * @Description: 
	 * @return: boolean 
	 * @throws: 
	 * @param paramBean
	 * @param newTrNo
	 * @return
	 */
	public boolean UpdateSecondTrade(ParamBean paramBean);

	
	/**
	 * 
	 * @author: kevin
	 * @Title saveMotoTradeDiffInfo
	 * @Time: 2012-5-29下午05:26:37
	 * @Description: 保存moto vpn交易信息
	 * @return: void 
	 * @throws: 
	 * @param sl
	 * @param ro
	 */
	public void saveMoto3DTradeDiffInfo(Moto3DBean sl, RateObj ro);


	/**
	 * 
	 * @author: kevin
	 * @Title updateMotoTradeDiffInfo
	 * @Time: 2012-5-30上午10:18:41
	 * @Description:  修改motovpn表
	 * @return: void 
	 * @throws: 
	 * @param sl
	 * @param isDcc
	 */
	public void updateMoto3DTradeDiffInfo(Moto3DBean sl, String isDcc);
	
	/**
	 * //根据通 网关号+道代码 去查找是否绑定了二次支付(如果是DCC,交易走的是EDC，则要找到交易通道标识，再去找是否绑定了二次支付)  guozb 13-05-03
	 * @author: guozb
	 * @Title getPayTypeFlag
	 * @Time: 2013-5-3上午10:06:48
	 * @Description: 
	 * @return: int 
	 * @throws: 
	 * @param bankReturnBean
	 * @return
	 */
	public int getPayTypeFlag( BankReturnBean bankReturnBean,int type);

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: saveMailAndIpForstcard
	 * @Description: 银行返回代码为stolen card,拉黑持卡人邮箱和IP
	 * @Time: 2013-7-1 上午11:05:27
	 * @Return: boolean
	 * @Throws:
	 */
	public boolean saveMailAndIpForstcard(BankReturnBean bankReturnBean);


	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: queryShopInfo
	 * @Description: 查询WebMoney shop 信息
	 * @Time: 2013-10-14 下午03:53:20
	 * @Return: WebMoneyShopBean
	 * @Throws:
	 */
	public WebMoneyShopBean queryShopInfo(String website);

	/**
	 * 
	 * @Auth: wujie
	 * @Title: getBankServerUrl
	 * @Description: 查询银行的支付地址信息
	 * @Return: Map
	 * @Throws:
	 */
	public Map getBankServerUrl(String bankCode);
	
	/**
     * @Title: getParamBeanInfo
     * @Description: 根据订单号获取交易信息
     * <AUTHOR>
     * @date 2020年4月30日 下午6:25:35
     * @param trNo
     * @return
     */
	public ParamBean getParamBeanInfo(String trNo);
	
	/**
	 * @Title: updateTrade3DSInfo
	 * @Description: 更新3DS相关信息
	 * <AUTHOR>
	 * @date 2021年1月12日 上午9:04:56
	 * @param trNo
	 * @param replyMap
	 */
	public boolean updateTrade3DSInfo(String trNo, Map<String, String> replyMap);
	
	/**
	 * @Title: checkMerBindCybers3DS
	 * @Description: 是否绑定了3ds验证服务
	 * <AUTHOR>
	 * @date 2021年2月5日 下午4:09:48
	 * @param paramBean
	 * @return
	 */
	public boolean checkMerBindCybers3DS(ParamBean paramBean);

    public boolean check3DSMerchantTrade(ParamBean paramBean);
    
    public boolean updateTrade3DSStep(String trNo, int tr3dsProvider, int step, String authData, String returnInfo);
    
    public boolean checkTrade3DSStep(String trNo, int step);
    
    /**
     * 保存消息推送信息
     */
    public void saveTradeNotification(String tradeNo, String statusCode, String jsonData,
            String errorCode, String errorInfo, String notificationUrl);
    
    /**
     * 更新ACS信息
     */
    public boolean updateTrade3DSAcsInfo(String trNo, String stepUpUrl, String jwt);
    
}
