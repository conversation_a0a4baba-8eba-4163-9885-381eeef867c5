package com.lot.dao;

import com.lot.bean.CreditInfoLogBean;
import com.lot.bean.LoadPageInfo;

public interface CreditInfoLogDao {

	/**
	 * 关闭支付信息收集页面，插入该交易记录
	 * @Title 
	 * @description 
	 * <AUTHOR> (赵明明)
	 * @time 2012上午10:29:57
	 * @param sucRate
	 */
	public void insertInfo(CreditInfoLogBean crlBean);

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: loadPageInfo
	 * @Description: 保存加载支付页面的信息
	 * @Time: 2013-9-10 下午03:47:52
	 * @Return: void
	 * @Throws:
	 */
	public void loadPageInfo(LoadPageInfo pageInfo);
}
