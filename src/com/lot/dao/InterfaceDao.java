package com.lot.dao;

import java.sql.Connection;
import java.util.List;
import java.util.Map;

import com.lot.bank.bean.BankReturnBean;
import com.lot.bank.bean.TradeInfoBean;
import com.lot.bean.DccChaInfolBean;
import com.lot.bean.ExtraElementBean;
import com.lot.bean.InterfaceParamName;
import com.lot.bean.MaxmindOutputs;
import com.lot.bean.ParamBean;
import com.lot.bean.ReturnBean;
import com.lot.bean.TeleSignBean;
import com.lot.bean.TerminalBean;
import com.lot.bean.UnTraderecordBean;


/**
 * 
 * <p>Title: </p>
 * <p>Description: 接口数据操作类</p>
 * <p>Copyright: Copyright (c) 2011 版权</p>
 * <p>Company: </p>
 * <AUTHOR>
 * @version V1.0 
 * @date 2011-6-27下午03:22:51
 */
public interface InterfaceDao{
	
	/**
	 * 
	 * @author: kevin
	 * @Title getExtraParam
	 * @Time: 2011-7-14下午12:09:40
	 * @Description: 根据网关接入号绑定的附加角色 获取附加信息 
	 * @return: List<ExtraElementBean> 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public List<ExtraElementBean> getExtraParam(ParamBean paramBean);
	
	/**
	 * 
	 * @author: kevin
	 * @Title saveInterfaceParam
	 * @Time: 2011-7-4下午03:34:32
	 * @Description:保存接口参数 
	 * @return: boolean 
	 * @throws: 
	 * @param returnBean
	 * @param paramBean
	 * @return
	 */
	public ReturnBean saveInterfaceParam(ReturnBean returnBean,ParamBean paramBean);
	
	/**
	 * @Title: saveInterfaceParam2
	 * @Description: 失败交易二次支付时，重新生成一笔交易
	 * <AUTHOR>
	 * @date 2016-12-19 下午04:36:27
	 * @param returnBean
	 * @param paramBean
	 * @return
	 */
	public ReturnBean saveInterfaceParam2(ReturnBean returnBean,ParamBean paramBean);
	/**
	 * 
	 * @author: kevin
	 * @Title processError
	 * @Time: 2011-7-5下午03:13:11
	 * @Description: 处理错误信息写到数据库:异常交易记录表或测试表
	 * @return: boolean 
	 * @throws: 
	 * @param paramBean
	 * @param returnBean
	 * @return
	 */
	public boolean processError(ParamBean paramBean,ReturnBean returnBean);
	
	/**
	 * 
	 * @author: kevin
	 * @Title processErrorByConfirm
	 * @Time: 2011-12-10上午11:38:14
	 * @Description: 延时通道风控2挡掉的处理:待确定
	 * @return: boolean 
	 * @throws: 
	 * @param paramBean
	 * @param returnBean
	 * @return
	 */
	public boolean processErrorByConfirm(ParamBean paramBean,ReturnBean returnBean);
	
	/**
	 * 
	 * @author: kevin
	 * @Title orderNoExist
	 * @Time: 2011-7-4下午04:01:34
	 * @Description: 判断商户订单号是否存在
	 * @return: boolean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public boolean orderNoExist(ParamBean paramBean);
	public boolean isWhiteElement(ParamBean paramBean);
	
	/**
	 * 
	 * @author: kevin
	 * @Title tradeNoIsExist
	 * @Time: 2011-7-27下午03:30:24
	 * @Description: 判断流水订单号是否存在
	 * @return: boolean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public boolean tradeNoIsExist(ParamBean paramBean);
	/**
	 * 
	 * @author: kevin
	 * @Title getTradeInfo
	 * @Time: 2011-7-4下午04:15:43
	 * @Description: 获取商户相关交易信息
	 * @return: ReturnBean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public ReturnBean getTradeInfo(ParamBean paramBean );
	
	/**
	 * 
	 * @author: kevin
	 * @Title getRealCurrency
	 * @Time: 2011-7-15下午06:10:51
	 * @Description: 获取币种汇率
	 * @return: double 
	 * @throws: 
	 * @param comeCurrency
	 * @param channelCurrency
	 * @return
	 */
	public Double getRealCurrency(String comeCurrency,String channelCurrency,ParamBean  paramBean);
	
	
	public Double getRealSettRate(String comeCurrency,String settCurrency,ParamBean  paramBean);
	
	 public Double getRealSettRate2(String comeCurrency, String settCurrency,
				ParamBean paramBean);
	/**
	 * 
	 * @author: kevin
	 * @Title getMerGateWayInfo
	 * @Time: 2011-7-5下午03:55:02
	 * @Description: 获取商户号网关接入号信息
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	public void getMerGateWayInfo(ParamBean paramBean);

	/**
	 * 
	 * @author: kevin
	 * @Title getCurrencyInfo
	 * @Time: 2011-7-8下午02:28:25
	 * @Description: 获取币种 list
	 * @return: List<String> 
	 * @throws: 
	 * @return
	 */
	public List<String> getCurrencyInfo();
	
	/**
	 * 
	 * @author: kevin
	 * @Title getCardType
	 * @Time: 2011-7-15下午02:15:18
	 * @Description: 获取卡种
	 * @return: void 
	 * @throws:
	 */
	public void getCardType();

	/**
	 * 
	 * @author: kevin
	 * @Title saveTradeRecord
	 * @Time: 2011-7-18下午04:23:51
	 * @Description: 保存信息到交易信息表
	 * @return: ReturnBean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public ReturnBean saveTradeRecord(ParamBean paramBean);

	/**
	 * 
	 * @author: kevin
	 * @Title saveCreditInfo
	 * @Time: 2011-7-4下午03:25:01
	 * @Description: 保存持卡人交易信息
	 * @return: boolean 
	 * @throws: 
	 * @param conn
	 * @param paramBean
	 * @return
	 */
	public boolean saveCreditInfo(Connection conn,ParamBean paramBean);
	
	/**
	 * 
	 * @author: kevin
	 * @Title getDomainInfo
	 * @Time: 2011-7-26下午03:03:42
	 * @Description: 获取商户绑定域名信息
	 * @return: ReturnBean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public ReturnBean getDomainInfo(ParamBean paramBean);
	
	/**
	 * 获取MAXMIND信息
	 * @author: Jadehu
	 * @Title getMaxmindInfo
	 * @Time: 2012-2-25下午12:03:05
	 * @Description: 
	 * @return: MaxmindOutputs 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public MaxmindOutputs getMaxmindInfo(String tradeNo);

	/**
	 * 
	 * @author: kevin
	 * @Title isTestCard
	 * @Time: 2011-9-26下午05:38:41
	 * @Description: 判断是否测试卡
	 * @return: boolean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public boolean isTestCard(ParamBean paramBean);
	
	/**
	 * 
	 * @author: peifang
	 * @Title updateTrReference
	 * @Time: 2012-2-7上午11:43:31
	 * @Description: 发送银行前更新交易序列号(提交到银行的唯一值 )如：YESPAYMENTS为12位 CHINAPAY为16位 
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	public boolean updateTrReference(ParamBean paramBean);
	
	public boolean updateOrgbankcode(ParamBean paramBean,String bankcode,BankReturnBean bankReturnBean);
	public boolean updateTrReferenceandinvoce(ParamBean paramBean,String invoceno);
	
	public boolean referencnoisexist(ParamBean paramBean);
	
	public boolean updateTrRefAndQueryNo(ParamBean paramBean);
	
	/**
	 * 
	 * @author: kevin
	 * @Title serverIpIsExist
	 * @Time: 2012-3-26下午05:46:56
	 * @Description: 判断服务器IP是否在商户设置里
	 * @return: boolean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public boolean serverIpIsExist(ParamBean paramBean);
	/**
	 * 保存持卡人的相关信息到CCPS_CREDITINFO_RECURRING表中
	 * @author: max
	 * @Title saveCreditCardRecurringInfo
	 * @Time: Jun 5, 20123:18:18 PM
	 * @Description: 
	 * @return: boolean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public ReturnBean saveCreditCardRecurringInfo(ParamBean paramBean);
	/**
	 * 获取持卡人唯一标识对应的记录实体
	 * @author: max
	 * @Title getCreditInfoRecurringBean
	 * @Time: Jun 5, 20123:31:29 PM
	 * @Description: 
	 * @return: CreditInfoRecurringBean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public ReturnBean getCreditInfoRecurringBean(ParamBean paramBean);

	/**
	 * @author: peifang
	 * @Title updateTrReferenceAndTrBankorderno
	 * @Time: 2012-6-14上午09:52:16
	 * @Description: 与银行多次交互时保存银行交易码及发送银行交易序列号(提交到银行的唯一值 )
	 * @return: boolean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	boolean updateTrReferenceAndTrBankorderno(ParamBean paramBean);
	
	/**
	 * 
	 * @author: kevin
	 * @Title getPaymentMethodInfo
	 * @Time: 2012-6-18下午04:50:32
	 * @Description: 根据支付方式名称获取支付方式信息
	 * @return: ReturnBean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public ReturnBean getPaymentMethodInfo(ParamBean paramBean);
	
	 /**
	  * 更改网关接入时间
	  * @author: guozb
	  * @Title updateUtrPayTime
	  * @Time: 2012-12-5下午02:09:22
	  * @Description: 
	  * @return: boolean 
	  * @throws: 
	  * @param paramBean
	  * @return
	  */
	public boolean updateUtrPayTime(ParamBean paramBean)  ;
	
	/**
	 * 修改持卡人信息
	 * @author: guozb
	 * @Title updateCreaditInfo
	 * @Time: 2012-12-14下午03:01:59
	 * @Description: 
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	public boolean updateCreaditInfo(ParamBean paramBean);
	
	/**
	 * 根据流水订单查询持卡人信息
	 * @author: guozb
	 * @Title queryCreaditInfoByTrNo
	 * @Time: 2012-12-17下午04:48:58
	 * @Description: 
	 * @return: boolean 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	public boolean queryCreaditInfoByTrNo(ParamBean paramBean);

	public String queryCreaditCardTempByTrNo(String trno);
	/**
	 * 获取系统参数
	 * @Title 
	 * @description 
	 * <AUTHOR> 
	 * @time 2013上午10:31:31
	 * @return
	 */
	public void getSysset();

	/**
	 * 校验12小时内是否有此交易
	 * @Title 
	 * @description 
	 * <AUTHOR> 
	 * @time 2013上午11:48:12
	 * @param paramBean
	 * @return
	 */
	public int verifyRepay(ParamBean paramBean);

	/**
	 * 保存电话校验信息
	 * @Title 
	 * @description 
	 * <AUTHOR> 
	 * @time 2013下午03:13:39
	 * @param telesign
	 * @return
	 */
	public int saveTeleSign(TeleSignBean telesign);

	
	/**
	 * 查询该订单的验证码
	 * @Title 
	 * @description 
	 * <AUTHOR> 
	 * @time 2013上午09:29:06
	 * @param tradeNo
	 * @return
	 */
	public List<TeleSignBean> queryVerCode(TeleSignBean telesign);

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: queryUnTraderecord
	 * @Description: 通过流水订单号查询挡掉交易表
	 * @Time: 2013-10-11 下午03:36:40
	 * @Return: List
	 * @Throws:
	 */
	public List<UnTraderecordBean> queryUnTraderecord(String tradeNo);

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: querTraderecord
	 * @Description: 通过流水订单号查询交易表
	 * @Time: 2013-10-12 下午04:12:41
	 * @Return: TradeInfoBean
	 * @Throws:
	 */
	public TradeInfoBean querTraderecord(String tradeNo);

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: getTradeOneminite
	 * @Description: 获取一分钟内该网关接入号的交易数量（正式交易和挡掉交易）
	 * @Time: 2013-10-21 下午02:23:54
	 * @Return: int
	 * @Throws:
	 */
	public int getTradeOneminite(ParamBean paramBean);

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: getTradeOneminite
	 * @Description: 获取一分钟内该网关接入号的交易数量(测试交易)
	 * @Time: 2013-10-21 下午02:23:54
	 * @Return: int
	 * @Throws:
	 */
	public int getTestTradeOneminite(ParamBean paramBean);

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: saveTerminal
	 * @Description: 保存终端信息
	 * @Time: 2014-1-22 上午10:59:13
	 * @Return: int
	 * @Throws:
	 */
	public int saveTerminal(TerminalBean terminal);
	
	 /**
	   * 获取DCC浮动通道信息
	   * @param chaCode
	   * @return
	   */
	  public DccChaInfolBean getDccChaInfo(int chaCode);
	  /**
	   * DCC浮动修改交易
	   * @param rate
	   * @param realAmount
	   * @param dccFlag
	   * @param trNo
	   */
	  public void updateDccChaTrade(double rate,double realAmount, int dccFlag,String trNo);
	  /**
	   * dcc浮动修改本币币种
	   * @author: guozb
	   * @Title updateDccChaCridite
	   * @Time: 2014-3-15上午11:54:05
	   * @Description: 
	   * @return: void 
	   * @throws: 
	   * @param dccCurrency
	   * @param trNo
	   */
	  public void updateDccChaCridite(String dccCurrency, String trNo);

	
	/**
	 * 获取到系统中设置的参数名称
	 * 
	 * @author: Wangqian
	 * @Title getInterfaceParamName
	 * @Time: 2014-6-9下午04:41:00
	 * @Description:
	 * @return: InterfaceParamName
	 * @throws:
	 * @param moduleId
	 * @return
	 */
	public InterfaceParamName getInterfaceParamName(String moduleId);
	
	/**
	 * 
	 * @author: zhangjiyue
	 * @Title: getTelValidateCard
	 * @Time: 2014-6-30 上午10:45:45
	 * @Description: 根据网关号获取需要电话验证的卡种
	 * @param gatewayNo
	 * @return 卡种id拼成的字符串, 用","隔开
	 */
	public String getTelValidateCard(String gatewayNo);

	/**
	 * 
	 * @author: zhangjiyue
	 * @Title: getSysParamValue
	 * @Time: 2014-7-18 下午04:29:03
	 * @Description: 获取系统参数值
	 * @param sysParamName 系统参数名
	 * @return
	 */
	public String getSysParamValue(String sysParamName);
	/**
	 * 
	 * @author: heyyi
	 * @Title: getSysParamValueByCode
	 * @Time: 2014-9-23 下午04:29:03
	 * @Description: 获取系统参数值
	 * @param sysParamCode 系统参数代码
	 * @return
	 * @throws Exception 
	 */
	String getBusinessParamValue(String paramCode) throws Exception;
	
	public String queryVerCode1(String telesign);
	/**
	 * 获取浮动汇率值
	 * @author: heyyi
	 * @Time: 2014-10-18 10:35:12
	 * @param gateWayNo
	 * @param currencyType
	 * @param currency
	 * @param extrangeType
	 * @param amount
	 * @return
	 * @throws Exception
	 */
	Double getFloatRate(String gateWayNo, String currency, String realCurrncy,
			int extrangeType, double amount, double realAmout) throws Exception;
	public ParamBean getInfo(String tradeNo);
	
	
	/**
	 * @Title: updateBillAddress
	 * @Description: 更新账单地址到交易表
	 * <AUTHOR>
	 * @date 2016-3-31 下午04:51:31
	 * @param trNo
	 * @param billAddress
	 * @return
	 */
	public boolean updateBillAddress(String trNo, String billAddress) throws Exception;
	
	/**
	 * @Title: checkPayipList
	 * @Description: 验证首信易支付IP是否为黑名单/白名单
	 * <AUTHOR>
	 * @date 2019年8月5日 上午9:10:10
	 * @param type 1-黑名单 2-白名单
     * @param ipAddress
	 * @return
	 */
	public boolean checkPayipList(int type, String ipAddress);
	
	/**
     * @Title: checkPayipBlackList
     * @Description: 获取首信易支付IP黑名单/白名单集合
     * <AUTHOR>
     * @date 2019年8月5日 上午9:10:10
     * @param type 1-黑名单 2-白名单
     * @return
     */
	public Map<Integer, String> getPayipList(int type);
	
	/**
	 * @Title: saveDbLog
	 * @Description: 保存异常日志信息
	 * <AUTHOR>
	 * @date 2019年8月5日 上午10:11:09
	 * @param proName
	 * @param logInfo
	 * @param trNo
	 * @return
	 */
	public String saveDbLog(String proName, String logInfo, String trNo);
	
	/**
	 * @Title: savePayBlacklistIp
	 * @Description: 保存首信易黑名单IP
	 * <AUTHOR>
	 * @date 2019年8月16日 下午12:15:14
	 * @param type 1-黑名单 2-白名单
	 * @param ipAddress
	 * @return
	 */
	public boolean savePayBlacklistIp(int type, String ipAddress);
	
	/**
	 * @Title: deletePayBlacklistIp
	 * @Description: 删除首信易黑名单IP
	 * <AUTHOR>
	 * @date 2019年8月16日 下午12:15:32
	 * @param type 1-黑名单 2-白名单
	 * @param ipAddress
	 * @return
	 */
	public boolean deletePayBlacklistIp(int type, String ipAddress);
	
	/**
	 * @Title: savePayIpChange
	 * @Description: 保存首信易IP转换信息
	 * <AUTHOR>
	 * @date 2019年8月21日 下午12:08:03
	 * @param trNo
	 * @param ipAddressBg 原始IP
     * @param ipAddressAf 转换后IP
	 * @return
	 */
    public boolean savePayIpChange(String trNo, String ipAddressBg, String ipAddressAf);
	
    
    /**
     * @Title: updateTrBankDatetime
     * @Description: 更新提交银行时间
     * <AUTHOR>
     * @date 2019年9月6日 下午2:31:07
     * @param trNo
     * @param bankdatetime
     * @return
     */
    public boolean updateTrBankDatetime(String trNo, String bankdatetime);
    
    /**
     * @Title: updateTrBankDatetime
     * @Description: 更新银行返回的交易时间
     * <AUTHOR>
     * @date 2019年9月6日 下午2:31:07
     * @param trNo
     * @param bankdatetime
     * @return
     */
    public boolean updateTrBankTradeDatetime(String trNo, String bankdatetime);

    /**
     * @Title: updateTr3DsStatus
     * @Description: 更新3D交易标识
     * <AUTHOR>
     * @date 2021年7月1日 上午10:32:28
     * @param trNo
     * @param tr3dsFlag
     * @return
     */
    public boolean updateTr3DsStatus(String trNo, String tr3dsFlag);
    
    /**
     * @Title: updateTr3DsStatus
     * @Description: 更新3D交易标识
     * <AUTHOR>
     * @date 2021年7月1日 上午10:32:28
     * @param trNo
     * @param tr3dsFlag
     * @return
     */
    public boolean updateTr3DsStatus(String trNo, String tr3dsFlag, String tr3dsStatus, String tr3dsInfo, int dmFalg);
}
