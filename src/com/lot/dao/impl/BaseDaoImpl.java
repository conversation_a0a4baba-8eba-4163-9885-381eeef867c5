package com.lot.dao.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import com.lot.util.SpringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.core.RowMapper;

import com.lot.bean.ConstantsBean;
import com.lot.bean.SysBusinessParam;
import com.lot.util.InterfaceUtil;

/**
 * 
 * <p>
 * Title:
 * </p>
 * <p>
 * Description: 数据库操作类
 * </p>
 * <p>
 * Copyright: Copyright (c) 2011 版权
 * </p>
 * <p>
 * Company:
 * </p>
 * 
 * <AUTHOR>
 * @version V1.0
 * @date 2011-6-27下午06:53:33
 */
public class BaseDaoImpl{

	static Logger log = Logger.getLogger(BaseDaoImpl.class);
	
	// 数据库驱动
	private static final String DRIVERNAME = "org.logicalcobwebs.proxool.ProxoolDriver";

	// 数据库别名 采用的是proxool连接池
	private static final String DBALIAS = "proxool.DBPool";

	private Connection conn = null;
	private PreparedStatement ps = null;
	private ResultSet rs = null;

	private static void printSql(String sql, Object[] pars) {
//		System.out.println("--SQL:" + sql + ";");
//		if (pars != null) {
//			for (int i = 0; i < pars.length; i++) {
//				System.out.println("--PARAM" + i + ":" + pars[i]);
//			}
//		}
	}

	/**
	 * 
	 * @author: kevin
	 * @Title getConn
	 * @Time: 2011-6-27下午06:53:52
	 * @Description:创建数据库连接
	 * @return: Connection
	 * @throws:
	 * @return
	 */
	public Connection getConn() {
        try {
            if (conn != null) {
                return conn;
            }
            Class.forName(DRIVERNAME);
            /** 更改为从数据源获取连接 */
            DataSource ds = SpringUtils.getBean("sbsDataSource", DataSource.class);
            conn = ds.getConnection();
            // conn = DriverManager.getConnection(DBALIAS);
        } catch (Exception ex) {
            log.error(InterfaceUtil.getExceptionInfo(ex));
        }
        return conn;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title addPars
	 * @Time: 2011-6-27下午06:54:04
	 * @Description: 根据sql语句、参数组装
	 * @return: void
	 * @throws:
	 * @param sql
	 * @param pars
	 */
	private void addPars(String sql, Object[] pars) {
		getConn();
		try {
			ps = conn.prepareStatement(sql);
			if (pars != null) {
				for (int i = 0; i < pars.length; i++) {
					ps.setObject((i + 1), pars[i] == null ? "" : pars[i]);
				}
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		}
	}

	/**
	 * 
	 * @author: kevin
	 * @Title closeAll
	 * @Time: 2011-6-27下午06:54:48
	 * @Description: 释放资源
	 * @return: void
	 * @throws:
	 */
	public void closeAll() {
		try {
			if (rs != null) {
				rs.close();
			}
			if (ps != null) {
				ps.close();
			}
			if (conn != null) {
				conn.close();
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			conn = null;
			rs = null;
			ps = null;
		}
	}

	/**
	 * 
	 * @author: kevin
	 * @Title executeUpdate
	 * @Time: 2011-6-27下午06:55:04
	 * @Description: 执行更新语句（增、删、改）
	 * @return: int
	 * @throws:
	 * @param sql
	 * @param pars
	 * @return
	 */
	public int executeUpdate(String sql, Object[] pars) {
		printSql(sql, pars);
		int result = 0;
		try {
			addPars(sql, pars);
			result = ps.executeUpdate();
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			this.closeAll();
		}
		return result;
	}
	
	/**
	 * 
	 * @author: kevin
	 * @Title executeUpdate
	 * @Time: 2011-7-13上午10:52:56
	 * @Description: 执行更新语句（增、删、改）
	 * @return: int 
	 * @throws: 
	 * @param conn
	 * @param sql
	 * @param pars
	 * @return
	 */
	public int executeUpdate(Connection conn,String sql, Object[] pars) {
		printSql(sql, pars);
		int result = 0;
		try {
			ps = conn.prepareStatement(sql);
			if (pars != null) {
				for (int i = 0; i < pars.length; i++) {
					ps.setObject((i + 1), pars[i] == null ? "" : pars[i]);
				}
			}
			
			result = ps.executeUpdate();
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			try {
				if (ps != null) {
					ps.close();
				}
			} catch (Exception ex) {
				log.error(InterfaceUtil.getExceptionInfo(ex));
			} finally {
				ps = null;
			}
		}
		return result;
	}

	/**
	 * 查询方法,需要遍历结果集
	 * 
	 * @param sql
	 *            SQL语句
	 * @param pars
	 *            占位参数
	 * @return ResultSet结果集
	 * @exception Exception
	 * 
	 * */
	public ResultSet executeQuery(String sql, Object[] pars)
			throws Exception {
		printSql(sql, pars);
		addPars(sql, pars);
		rs = ps.executeQuery();
		return rs;
	}
	
	
	public ResultSet executeQuery(Connection conn,String sql, Object[] pars)
	throws Exception {
		try {
			printSql(sql, pars);
			ps = conn.prepareStatement(sql);
			if (pars != null) {
				for (int i = 0; i < pars.length; i++) {
					ps.setObject((i + 1), pars[i] == null ? "" : pars[i]);
				}
			}
			rs = ps.executeQuery();
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		}

			return rs;
}
	/**
	 * 
	 * @author: kevin
	 * @Title executeQueryCount
	 * @Time: 2011-7-11上午11:22:00
	 * @Description: 查询方法,返回记录条数
	 * @return: String 
	 * @throws: 
	 * @param sql
	 * @param pars
	 * @return
	 */
	public String executeQueryByFirst(String sql, Object[] pars) {
		printSql(sql, pars);
		String str = "";
		try {
			addPars(sql, pars);
			rs = ps.executeQuery();
			while(rs.next()){
				str = rs.getString(1);
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			this.closeAll();
		}
		return str;
	}
	
	public String executeQueryByFirst1(String sql, Object[] pars) {
		printSql(sql, pars);
		String str = "";
		try {
			addPars(sql, pars);
			rs = ps.executeQuery();
			while(rs.next()){
				str = rs.getString(1);
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			//this.closeAll();
		}
		return str;
	}
	
	/**
	 * 批量修改数据
	 */
	public boolean batchUpdate(String sql, List<Object[]> pars){
		boolean flag = false;
		int[] rt ;
		Connection con = getConn();
		try {
			ps = con.prepareStatement(sql);
			con.setAutoCommit(false);
			for(Object[] obj: pars){
				for (int i = 0; i < obj.length; i++) {
					ps.setObject((i + 1), obj[i] == null ? "" : obj[i]);					
				}
				ps.addBatch();
				
			}
			rt = ps.executeBatch();
			con.commit();
			con.setAutoCommit(true);
			flag = true;
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			log.error(InterfaceUtil.getExceptionInfo(e));
		} finally{
			this.closeAll();
			if(con != null){
				try {
					con.close();
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					log.error(InterfaceUtil.getExceptionInfo(e));
				}
				con = null;
			}
		}
		return flag ;
		
	}
	
	/**
	 * @Title: saveSms
	 * @Description: 保存待发送信息
	 * @author: liuyq
	 * @date: 2015-7-10 上午11:55:17
	 * @param smsType 短信类型 1-预警短信
	 * @param from
	 * @param phones
	 * @param content
	 * @return
	 * @throws SystemException
	 */
	public String saveSms(int smsType,String from,String phones,String content) throws Exception{

		if(StringUtils.isBlank(phones)){
			//接收手机号为空
			log.info("error=S0001");
			log.info("errorMsg=接收手机号不能为空");
			return "S0001";
		}else{
			if("0".equals(phones) || phones.length() < 7){
				log.info("error=S0004");
				log.info("errorMsg=接收手机号不正确");
				return "S0004";
			}
			String[] str = phones.split(";");
			if(str.length>50){
				//接收手机号最多发送50个号码
				log.info("error=S0002");
				log.info("errorMsg=接收手机号最多只能发送50个号码");
				return "S0002";
			}
		}
		if(StringUtils.isBlank(content)){
			//发送内容为空
			log.info("error=S0003");
			log.info("errorMsg=发送内容不能为空");
			return "S0003";
		}else{
			
			if(content.length()>300){
				content = content.substring(0,295)+"...";
				/*//发送内容不能超过300个字符
				logger.info("error=S0004");
				logger.info("errorMsg=发送内容不能超过300个字符");
				return "S0004";*/
			}
		}
		if(StringUtils.isBlank(from)){
			from = ConstantsBean.SMS_FROM;
		}
		
		Map<String, String> parammap = this.getSysBusParam();
		
		StringBuffer sql = new StringBuffer();
		sql.append("INSERT INTO CCPS_SENDSMS(SMS_ID,SMS_FROM,SMS_SENDTO,SMS_CONTENT,SMS_TYPE,")
		.append("SMS_STATUS,SMS_MAXTIMES,SMS_TIMES,SMS_CREATETIME,SMS_SENDTIME,SMS_EXPIREDTIME,")
		.append("SMS_USERID,SMS_USERNAME,SMS_PASSWORD,SMS_ISVOICE,SMS_POSTFIXNUM)")
		.append(" VALUES (CCPS_SENDSMS_SEQ.Nextval,?,?,?,?,0,?,0,sysdate,")
		.append(" sysdate,sysdate+1,?,?,?,1,'0')");
		
		List<Object> paramList = new ArrayList<Object>();
		paramList.add(from);
		paramList.add(phones);
		paramList.add(content);
		paramList.add(smsType);//短信类型
		paramList.add(ConstantsBean.SMS_MAXTIMES);//最大发送次数如果为0 ,  则表示发送直到成功
		paramList.add(parammap.get(ConstantsBean.SMS_USERID));
		paramList.add(parammap.get(ConstantsBean.SMS_USERNAME));
		paramList.add(parammap.get(ConstantsBean.SMS_PASSWORD));
		
		log.info("sql="+sql.toString()+" parms="+paramList.toString());
		
		this.executeUpdate(sql.toString(), paramList.toArray());
		
		return null;
	}

	/**
	 * @Title: getSysBusParam
	 * @Description: 获取业务参数
	 * @author: liuyq	
	 * @date: 2015-6-10 上午10:07:54
	 * @return
	 * @throws Exception
	 */
	public Map<String, String> getSysBusParam() throws Exception{
		Map<String, String> map = new HashMap<String, String>();
		ResultSet rs = null;
		try{
			String sql = "select * from sys_business_param";
			
			rs = this.executeQuery(sql,new Object[] { });
			while(rs.next()) {
				String paraCode = rs.getString("SB_PARA_CODE");
				String paraValue = rs.getString("SB_PARA_VALUE");
				map.put(paraCode, paraValue);
			}
		}catch (Exception e) {
			log.error("业务参数查询出现异常：" + InterfaceUtil.getExceptionInfo(e));
			throw new Exception();
		} finally {
			this.closeAll();
		}
		return map; 
	}
	
	/**
	 * @Title: SysBusRowMapper
	 * @Description: 查询业务参数信息
	 * @author: liuyq
	 * @date: 2015-6-10 上午10:00:00
	 * @param merNo
	 * @param gatewayNo
	 * @return
	 * @throws SystemException
	 */
	static class SysBusRowMapper implements RowMapper {
		@Override
		public Object mapRow(ResultSet rs, int index) throws SQLException {
			SysBusinessParam sysBus = new SysBusinessParam();
			sysBus.setSbId(rs.getLong("sb_id"));
			sysBus.setSbParaType(rs.getInt("sb_para_type"));
			sysBus.setSbParaCode(rs.getString("sb_para_code"));
			sysBus.setSbParaName(rs.getString("sb_para_name"));
			sysBus.setSbParaValue(rs.getString("sb_para_value"));
			return sysBus;
		}
	}
	
}