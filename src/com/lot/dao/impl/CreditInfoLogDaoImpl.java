package com.lot.dao.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.lot.bean.CreditInfoLogBean;
import com.lot.bean.LoadPageInfo;
import com.lot.dao.CreditInfoLogDao;
import com.lot.sql.InterfaceSql;
import com.lot.sql.RiskControlSql;

public class CreditInfoLogDaoImpl extends BaseDaoImpl implements CreditInfoLogDao {

	Logger logger = Logger.getLogger(CreditInfoLogDaoImpl.class);
	
	
	@Override
	public void insertInfo(CreditInfoLogBean crlBean) {
		// TODO Auto-generated method stub
		StringBuffer sql = RiskControlSql.SQL_INSERT_CREDITINFOLOG;
		
		int m = 0;
		List<Object> pars = new ArrayList<Object>();
		try{
			pars.add(crlBean.getTradeNo());
			pars.add(crlBean.getMerNo());
			pars.add(crlBean.getGwNo());
			pars.add(crlBean.getCause());
			pars.add(crlBean.getRemark());
			pars.add(crlBean.getOs());
			pars.add(crlBean.getBrower());
			pars.add(crlBean.getOsLong());
			pars.add(crlBean.getTimezone());
			pars.add(crlBean.getResolution());
			pars.add(crlBean.getCkrip());
			if(crlBean.getEnterPagetime() != null && !"".equals(crlBean.getEnterPagetime())){			
				pars.add( java.sql.Timestamp.valueOf(crlBean.getEnterPagetime()));
			}else{
				pars.add(null);
			}
			pars.add(crlBean.getComloadpage());
			pars.add(crlBean.getWhetherEntry());
			m = super.executeUpdate(sql.toString(), pars.toArray());
			if(m<1){
				logger.info("插入 CCPS_CREDITINFO_LOG 数据失败");
			}
		}catch (Exception e) {
			// TODO: handle exception
			logger.info("插入 CCPS_CREDITINFO_LOG 表出现异常");
		}		
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Description: 保存加载支付页面的信息
	 * @Time: 2013-9-10 下午03:48:20
	 * @Throws:
	 */
	@Override
	public void loadPageInfo(LoadPageInfo pageInfo) {
		// TODO Auto-generated method stub
		StringBuffer sql = InterfaceSql.SQL_SAVE_PAGEINFO;		
		int m = 0;
		List<Object> pars = new ArrayList<Object>();
		try{
			pars.add(pageInfo.getTradeNo());
			pars.add(pageInfo.getStarttime());
			pars.add(pageInfo.getEndtime());
			m = super.executeUpdate(sql.toString(), pars.toArray());
			if(m < 1){
				logger.info("保存加载支付页面信息失败");
			}				
		}catch (Exception e) {
			// TODO: handle exception
			logger.info("保存加载支付页面信息异常");
		}
	}

}
