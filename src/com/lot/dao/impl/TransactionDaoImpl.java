package com.lot.dao.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;

import com.lot.bean.Gateway;
import com.lot.bean.RefundInBean;
import com.lot.bean.RefundObj;
import com.lot.bean.RefundOutBean;
import com.lot.bean.RefundTradeInfo;
import com.lot.bean.Traderecord;
import com.lot.bean.UnNormalProcessBean;
import com.lot.dao.TransactionDao;
import com.lot.error.RefundInfo;
import com.lot.sql.InterfaceSql;
import com.lot.util.BigDPayment;
import com.lot.util.EncryptUtil;
import com.lot.util.InterfaceUtil;
import com.lot.util.ParamCheck;

/**
 * 
 * <p>
 * 
 * @Title: </p>
 *         <p>
 * @Description:交易接口处理 </p>
 *                     <p>
 * @Copyright: Copyright (c) 2010 版权
 *             </p>
 *             <p>
 * @Company: </P>
 * 
 * @Auth zhaomingming
 * @version V1.0
 * @date 2013-8-26 下午06:04:49
 */
public class TransactionDaoImpl extends BaseDaoImpl implements TransactionDao {

	private static Logger logger = Logger.getLogger(TransactionDaoImpl.class);

	private static DecimalFormat df = new DecimalFormat("#.00");

	/**
	 * 申请退款，直接退款到银行
	 * 
	 * @author: Wangqian
	 * @Title applyrefundInterface
	 * @Time: 2014-6-18下午04:03:46
	 * @Description:
	 * @return: RefundOutBean
	 * @throws:
	 * @param refundIn
	 * @param remoteIp
	 * @return
	 * @throws SystemException
	 */
	@Override
	public RefundOutBean realTimeRefundBean(RefundInBean refundIn,
			String remoteIp) {

		RefundOutBean outBean = new RefundOutBean();

		// 传入参数为空
		if (refundIn == null) {
			outBean = returnInfo(refundIn, "01");
			return outBean;
		}

		// 打印传入的参数
		StringBuffer strbuf = new StringBuffer();
		strbuf.append(", merNo:" + refundIn.getMerNo());
		strbuf.append(", gatawayNo:" + refundIn.getGatawayNo());
		strbuf.append(", tradeNo:" + refundIn.getTradeNo());
		strbuf.append(", refundType:" + refundIn.getRefundType());
		strbuf.append(", tradeAmount:" + refundIn.getTradeAmount());
		strbuf.append(", refundAmount:" + refundIn.getRefundAmount());
		strbuf.append(", currency:" + refundIn.getCurrency());
		strbuf.append(", refundReason:" + refundIn.getRefundReason());
		strbuf.append(", remark:" + refundIn.getRemark());
		strbuf.append(", remoteIp:" + remoteIp);

		logger.info(strbuf.toString());
		// 退款参数校验
		outBean = checkParam(refundIn);

		// 退款参数错误
		if (!StringUtils.isBlank(outBean.getCode())) {
			outBean = returnInfo(refundIn, outBean.getCode());
			return outBean;
		}

		// 校验商户信息
		outBean = checkMerchant(refundIn);

		if (!StringUtils.isBlank(outBean.getCode())) { // 商户校验未通过
			outBean = returnInfo(refundIn, outBean.getCode());
			return outBean;
		}

		// 访问ip校验
		if (!ipCheckIsExist(remoteIp, refundIn)) {
			outBean = returnInfo(refundIn, "03");
			outBean.setDescription(outBean.getDescription() + ":" + remoteIp);
			return outBean;
		}

		Traderecord trade = getTraderecord(refundIn);

		// 未查询到该笔交易
		if (trade == null) {
			outBean = returnInfo(refundIn, "98");
			return outBean;
		}

		// 设置退款批次号
		refundIn.setBatchNo(getBatchNo());

		// 交易校验
		outBean = checkTradeInfo(refundIn, trade);
		outBean = returnInfo(refundIn, outBean.getCode());

		return outBean;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: returnInfo
	 * @Description: 返回信息
	 * @Time: 2013-8-29 上午11:40:04
	 * @Return: RefundOutBean
	 * @Throws:
	 */

	private RefundOutBean returnInfo(RefundInBean refundIn, String code) {
		RefundOutBean outBean = new RefundOutBean();
		outBean = RefundInfo.refunderror(code);
		if (refundIn != null) {
			outBean.setBatchNo(refundIn.getBatchNo());
			outBean.setMerNo(refundIn.getMerNo());
			outBean.setGatewayNo(refundIn.getGatawayNo());
			outBean.setSignInfo(refundIn.getSignInfo());
			outBean.setTradeNo(refundIn.getTradeNo());
			outBean.setRefundReason(refundIn.getRefundReason());
			outBean.setRemark(refundIn.getRemark());
		}
		logger.info("return code:" + code);
		return outBean;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: saveRefundment
	 * @Description: 保存退款申请信息
	 * @Time: 2013-8-28 下午02:39:56
	 * @Return: boolean
	 * @Throws:
	 */
	private boolean saveRefundment(RefundInBean in, Traderecord td) {

		StringBuffer sqlInfo = InterfaceSql.SQL_SAVE_REFUND;
		List<Object> param = new ArrayList<Object>();
		param.add(in.getBatchNo());
		param.add(in.getTradeNo());
		param.add(4);
		param.add(1);
		param.add(td.getTrCurrency());
		param.add(df.format(in.getRefundAmount()));
		param.add(ParamCheck.getSubString(in.getRefundReason(), 100));
		param.add(ParamCheck.getSubString(in.getRemark(), 100));
		param.add(0);
		param.add("system");
		if (td.getTrTsStatus() == 0) {
			param.add(0);
			param.add(0);
		} else {
			param.add(1);
			param.add(1);
		}
		int count = super.executeUpdate(sqlInfo.toString(), param.toArray());
		return count > 0 ? true : false;
	}

	/**
	 * 参数校验
	 * 
	 * @author: Wangqian
	 * @Title checkParam
	 * @Time: 2014-7-15上午10:46:12
	 * @Description:
	 * @return: RefundOutBean
	 * @throws:
	 * @param in
	 * @return
	 */
	private RefundOutBean checkParam(RefundInBean in) {
		RefundOutBean result = new RefundOutBean();
		// 校验参数是否完整
		if (StringUtils.isBlank(in.getTradeNo())
				|| StringUtils.isBlank(in.getCurrency())
				|| StringUtils.isBlank(in.getRefundReason())) {
			result = RefundInfo.refunderror("04");
		} else if (!StringUtils.isNumeric(in.getTradeNo())
				|| in.getTradeNo().length() > 30
				|| in.getTradeNo().length() < 20) { // 校验流水订单号，为数字并且长度小于30
			result = RefundInfo.refunderror("08");
		} else if (in.getRefundType() != 1 && in.getRefundType() != 2) { // 校验退款类型
			result = RefundInfo.refunderror("10");
		} else if (!amountCheck(in.getTradeAmount())) { // 交易金额格式错误
			result = RefundInfo.refunderror("22");
		} else if (!amountCheck(in.getRefundAmount())) { // 退款金额格式错误
			result = RefundInfo.refunderror("18");
		} else if (in.getRefundAmount() > in.getTradeAmount()) { // 校验退款金额是否大于交易金额
			result = RefundInfo.refunderror("09");
		} else if (!checkRefundType(in.getTradeAmount(), in.getRefundAmount(),
				in.getRefundType())) { // 退款金额和退款类型不符
			result = RefundInfo.refunderror("19");
		} else if (!StringUtils.isBlank(in.getRefundReason())
				&& in.getRefundReason().length() > 100) { // 校验退款类型
			result = RefundInfo.refunderror("23");
		}
		logger.info("param check return code :" + result.getCode());
		return result;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: checkRefundType
	 * @Description:
	 * @Time: 2013-8-28 上午09:38:05
	 * @Return: boolean
	 * @Throws:
	 */
	private boolean checkRefundType(double tradeAmount, double refundAmount,
			int refundtype) {
		if ((tradeAmount == refundAmount && refundtype == 2)
				|| (tradeAmount != refundAmount && refundtype == 1)) {
			return false;
		} else {
			return true;
		}
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: amountCheck
	 * @Description: 金额格式判断,最多两位小数
	 * @Time: 2013-8-29 上午09:03:02
	 * @Return: boolean
	 * @Throws:
	 */
	public boolean amountCheck(double amount) {
		if (amount <= 0) {
			return false;
		}
		String amountstr = String.valueOf(amount);
		boolean amountflag = true;
		int length = amountstr.length() - amountstr.indexOf(".");
		if (length > 3) {
			amountflag = false;
		}
		return amountflag;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: checkGw
	 * @Description: 判断网关信息
	 * @Time: 2013-8-27 上午11:20:00
	 * @Return: RefundOutBean
	 * @Throws:
	 */
	private RefundOutBean checkMerchant(RefundInBean refundIn) {
		RefundOutBean out = new RefundOutBean();

		// 商户号错误
		if (refundIn.getMerNo() <= 0) {
			out = RefundInfo.refunderror("05");
			return out;
		}

		// 网关接入号错误
		if (refundIn.getGatawayNo() <= 0) {
			out = RefundInfo.refunderror("06");
			return out;
		}

		// 签名信息错误
		if (StringUtils.isBlank(refundIn.getSignInfo())) {
			out = RefundInfo.refunderror("12");
			return out;
		}

		// 查询网关接入号信息
		StringBuffer sql = InterfaceSql.SQL_GET_GATEWAY;

		// 结果集
		ResultSet rs = null;
		Gateway gateway = null;

		try {
			rs = super.executeQuery(sql.toString(), new Object[] { refundIn
					.getGatawayNo() });
			while (rs.next()) {
				gateway = new Gateway();
				gateway.setMerNo(rs.getLong("gw_mer_no"));
				gateway.setGwNo(rs.getLong("gw_no"));
				gateway.setGwStatus(rs.getLong("gw_status"));
				gateway.setMd5key(rs.getString("gw_md5key"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}

		if (gateway == null) {
			out = RefundInfo.refunderror("06");
		} else {
			if (refundIn.getMerNo() != gateway.getMerNo()) { // 商户号错误
				out = RefundInfo.refunderror("05");
			} else {
				// 获取签名信息
				String sign = signCheck(refundIn, gateway);
				if (!sign.equalsIgnoreCase(refundIn.getSignInfo())) {
					out = RefundInfo.refunderror("12");
				}
			}
		}

		logger.info("merchant check return code:" + out.getCode());
		return out;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: signCheck
	 * @Description:
	 * @Time: 2013-8-27 上午10:43:15
	 * @Return: boolean
	 * @Throws:
	 */
	private String signCheck(RefundInBean in, Gateway gw) {
		StringBuffer str = new StringBuffer();
		str.append(in.getMerNo());
		str.append(in.getGatawayNo());
		str.append(in.getTradeNo());
		str.append(in.getRefundType());
		str.append(gw.getMd5key());
		EncryptUtil encry = new EncryptUtil();
		String info = encry.getSHA256Encrypt(str.toString());
		return info;
	}

	/**
	 * 查询交易订单
	 * 
	 * @author: Wangqian
	 * @Title getTraderecord
	 * @Time: 2014-7-15下午12:22:19
	 * @Description:
	 * @return: Traderecord
	 * @throws:
	 * @param refundIn
	 * @return
	 */
	private Traderecord getTraderecord(RefundInBean refundIn) {

		// 查询交易信息
		StringBuffer tradeSql = InterfaceSql.SQL_GET_TRADE_REFUND;

		// 封装参数
		List<Object> list = new ArrayList<Object>();
		list.add(refundIn.getTradeNo().trim());
		list.add(refundIn.getMerNo());
		list.add(refundIn.getGatawayNo());

		// 结果集
		ResultSet rs = null;
		Traderecord trade = null;

		try {
			rs = super.executeQuery(tradeSql.toString(), list.toArray());
			while (rs.next()) {
				// 查询结果
				trade = new Traderecord();
				trade.setTrNo(rs.getString("tr_no"));
				trade.setTrMerNo(rs.getLong("tr_mer_no"));
				trade.setTrGwNo(rs.getLong("tr_gw_no"));
				trade.setTrDatetime(rs.getTimestamp("tr_datetime"));
				trade.setTrAmount(rs.getDouble("tr_amount"));
				trade.setTrCurrency(rs.getString("tr_currency"));
				trade.setTrStatus(rs.getInt("tr_status"));
				trade.setTrCongeal(rs.getInt("tr_congeal"));
				trade.setTrRsStatus(rs.getInt("tr_rs_status"));
				trade.setTrTsStatus(rs.getInt("tr_ts_status"));
				trade.setTrIsBackCHA(rs.getInt("cha_part"));
				trade.setTrTsId(rs.getLong("tr_ts_id"));
				trade.setTrAsId(rs.getLong("tr_as_id"));
				trade.setTrAsStatus(rs.getInt("tr_as_status"));
				trade.setTrChecked(rs.getInt("tr_checked"));
				trade.setTrRefundmentAmount(rs
						.getBigDecimal("TR_REFUNDMENT_AMOUNT"));
				trade.setTrCongealAmount(rs.getBigDecimal("TR_CONGEAL_AMOUNT"));
				trade.setTrProtestAmount(rs.getBigDecimal("TR_PROTEST_AMOUNT"));
				trade.setTrBankCode(rs.getString("TR_BANK_CODE"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}

		return trade;
	}

	/**
	 * 查询退款批次号
	 * 
	 * @author: Wangqian
	 * @Title getBatchNo
	 * @Time: 2014-7-15下午02:21:51
	 * @Description:
	 * @return: Long
	 * @throws:
	 * @return
	 */
	private Long getBatchNo() {
		// 查询交易信息
		StringBuffer batchNoSql = InterfaceSql.SQL_GET_BATCHNO;

		// 封装参数
		List<Object> list = new ArrayList<Object>();

		// 结果集
		ResultSet rs = null;
		Long batchNo = null;

		try {
			rs = super.executeQuery(batchNoSql.toString(), list.toArray());
			while (rs.next()) {
				// 查询结果
				batchNo = rs.getLong(1);
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
		return batchNo;
	}

	/**
	 * 查询异常交易金额
	 * 
	 * @author: Wangqian
	 * @Title getUnormalTotalAmount
	 * @Time: 2014-7-15下午02:41:48
	 * @Description:
	 * @return: UnNormalProcessBean
	 * @throws:
	 * @param tradeNo
	 * @return
	 */
	private UnNormalProcessBean getUnormalTotalAmount(String tradeNo) {

		StringBuffer sqlInfo = InterfaceSql.SQL_GET_PROCESSTOTAL;

		// 封装参数
		List<Object> list = new ArrayList<Object>();
		list.add(tradeNo);

		// 结果集
		ResultSet rs = null;
		UnNormalProcessBean unnormal = null;

		try {
			rs = super.executeQuery(sqlInfo.toString(), list.toArray());
			while (rs.next()) {
				// 查询结果
				unnormal = new UnNormalProcessBean();
				unnormal.setTotalAmount(rs.getDouble("amount"));
				unnormal.setTotalCurrency(rs.getString("UP_CURRENCY"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}

		return unnormal;
	}

	/**
	 * 根据交易划款批次号查询划款复核状态
	 * 
	 * @Auth: zhaomingming
	 * @Title: getTsStatus
	 * @Description:
	 * @Time: 2013-8-28 上午10:18:28
	 * @Return: String
	 * @Throws:
	 */
	private int getTsStatus(long tsId) {
		int status = 0;
		StringBuffer infoSql = InterfaceSql.SQL_GET_TRADESETTLEMENT;

		// 封装参数
		List<Object> list = new ArrayList<Object>();
		list.add(tsId);

		// 结果集
		ResultSet rs = null;

		try {
			rs = super.executeQuery(infoSql.toString(), list.toArray());
			while (rs.next()) {
				// 查询结果
				status = rs.getInt("TS_STATUS");
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}

		logger.info("trTsStatus:" + status);
		return status;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: getTradePatchSettle
	 * @Description:
	 * @Time: 2013-8-28 上午11:07:49
	 * @Return: UnNormalProcessBean
	 * @Throws:
	 */
	private List<UnNormalProcessBean> getTradePatchSettle(String trNo) {

		StringBuffer sqlInfo = InterfaceSql.SQL_PROCESS_SETTLE;
		List<UnNormalProcessBean> beanList = new ArrayList<UnNormalProcessBean>();

		// 封装参数
		List<Object> list = new ArrayList<Object>();
		list.add(trNo);

		// 结果集
		ResultSet rs = null;
		UnNormalProcessBean unnormal = null;

		try {
			rs = super.executeQuery(sqlInfo.toString(), list.toArray());
			while (rs.next()) {
				// 查询结果
				unnormal = new UnNormalProcessBean();
				unnormal.setUpAgentStatus(rs.getInt("UP_AGENT_STATUS"));
				unnormal.setUpAsId(rs.getLong("UP_AS_ID"));
				unnormal.setUpSettlementStatus(rs
						.getInt("UP_SETTLEMENT_STATUS"));
				unnormal.setUpTsId(rs.getLong("UP_TS_ID"));
				beanList.add(unnormal);
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}

		return beanList;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: checkSettle
	 * @Description:
	 * @Time: 2013-8-28 上午11:22:15
	 * @Return: boolean
	 * @Throws:
	 */
	private boolean checkSettle(List<UnNormalProcessBean> patchList) {
		boolean unflag = true;
		for (UnNormalProcessBean un : patchList) {
			if (un.getUpSettlementStatus() == 1) {
				int unstatus = getTsStatus(un.getUpTsId());
				if (unstatus == 0 || unstatus == 1) {
					logger.info("uptsid:" + un.getUpTsId() + ", unstatus:"
							+ unstatus);
					unflag = false;
					break;
				}
			}
		}
		return unflag;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: getAgentStatus
	 * @Description:
	 * @Time: 2013-8-28 上午11:50:22
	 * @Return: int
	 * @Throws:
	 */
	private int getAgentStatus(long asId) {
		int status = 0;
		StringBuffer infoSql = InterfaceSql.SQL_GET_AGENTSETTLEMENT;

		// 封装参数
		List<Object> list = new ArrayList<Object>();
		list.add(asId);

		// 结果集
		ResultSet rs = null;

		try {
			rs = super.executeQuery(infoSql.toString(), list.toArray());
			while (rs.next()) {
				// 查询结果
				status = rs.getInt("TS_STATUS");
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}

		logger.info("trTsStatus:" + status);
		return status;

	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: checkSettle
	 * @Description:
	 * @Time: 2013-8-28 上午11:22:15
	 * @Return: boolean
	 * @Throws:
	 */
	private boolean checkAgent(List<UnNormalProcessBean> patchList) {
		boolean unflag = true;
		for (Object obj : patchList) {
			UnNormalProcessBean un = (UnNormalProcessBean) obj;
			if (un.getUpAgentStatus() == 1) {
				int unstatus = getAgentStatus(un.getUpAsId());
				if (unstatus == 0 || unstatus == 1) {
					logger.info("upasid:" + un.getUpAsId() + ", unstatus:"
							+ unstatus);
					unflag = false;
					break;
				}
			}
		}
		return unflag;
	}

	/**
	 * 查询需要退款的信息
	 * 
	 * @author: Wangqian
	 * @Title queryRefundTradeInfo
	 * @Time: 2014-7-15下午04:15:28
	 * @Description:
	 * @return: RefundTradeInfo
	 * @throws:
	 * @param upId
	 * @param bankCode
	 * @return
	 */
	private RefundTradeInfo queryRefundTradeInfo(String upId, String bankCode) {

		List<Object> param = new ArrayList<Object>();

		StringBuffer infoSqlStr = new StringBuffer(
				"select * from ccps_traderecord t LEFT JOIN ccps_bank b ON  t.tr_bank_code=b.bank_code LEFT JOIN ccps_unnormal_process up ON t.tr_no=up.up_tr_no LEFT JOIN ccps_channel c ON t.tr_cha_code = c.cha_code LEFT join ccps_currency c on c.curr_name=t.tr_bankcurrency ");
		infoSqlStr
				.append(" LEFT join (select distinct sf.scb_tr_no,sf.scb_xid,sf.scb_securitylevel,sf.scb_cav,sf.scb_is3d from ccps_scb_function sf where sf.scb_type in(1,2) ) f on t.tr_no=f.scb_tr_no  WHERE 1 = 1");

		// 退款银行
		// 判断是scb还是Ebanx，scb需要支持多通道scb，scb_gb
		if (bankCode.startsWith("SCB")) {
			infoSqlStr.append(" AND upper(b.bank_code) in ('SCB','SCB_GB')");
		} else {
			infoSqlStr.append(" AND upper(b.bank_code) = 'EBANX'");
		}

		// 退款批次号
		infoSqlStr.append(" AND up.up_id = ?");
		param.add(upId);

		// 结果集
		ResultSet rs = null;
		RefundTradeInfo bean = null;

		try {
			rs = super.executeQuery(infoSqlStr.toString(), param.toArray());
			while (rs.next()) {
				// 查询结果
				bean = new RefundTradeInfo();
				bean.setReMerNo(rs.getString("TR_MER_NO"));
				bean.setReTrNo(rs.getString("TR_NO"));
				bean.setReGwNo(rs.getString("TR_GW_NO"));
				bean.setMerOrderNo(rs.getString("TR_MER_ORDERNO"));
				bean.setBankAmount(rs.getBigDecimal("TR_BANKAMOUT"));
				bean.setTrAmount(rs.getBigDecimal("TR_AMOUNT"));
				bean.setPayBank(rs.getString("BANK_NAME"));
				bean.setTrTime(rs.getTimestamp("TR_DATETIME"));
				bean.setAmountCurrency(rs.getString("TR_CURRENCY"));
				bean.setBankCurrency(rs.getString("tr_bankcurrency"));
				bean.setCurrencyCodeT(rs.getString("curr_code"));
				bean.setUpId(rs.getLong("UP_ID"));
				bean.setUpOprTime(rs.getTimestamp("UP_OPRTIME"));
				bean.setRefundAmountTR(rs.getBigDecimal("UP_AMOUNT"));

				bean.setRate(rs.getBigDecimal("TR_RATE_VALUE"));
				double rp = BigDPayment.round((bean.getRefundAmountTR()
						.doubleValue() * bean.getRate().doubleValue()), 2);
				bean.setRefundAmountRP(new BigDecimal(String.valueOf(rp)));

				bean.setRefundReason(rs.getString("UP_REASON"));
				bean.setBankMerNo(rs.getString("CHA_MERNO"));

				bean.setTerminalNo(rs.getString("TR_TERMINALNO"));
				bean.setAccessNo(rs.getString("CHA_SECURE_SECRET"));
				bean.setGatewayClientURL(rs.getString("BANK_PAY_URL"));
				bean.setUpcAccesscode(rs.getString("CHA_VPC_ACCESSCODE"));
				bean.setChaCode(rs.getString("cha_code"));

				// 授权号，银行订单号
				bean.setAuthorNo(rs.getString("TR_AUTHORIZELD"));
				bean.setBankOrderNo(rs.getString("TR_BANKORDERNO"));

				// 渣打
				bean.setScbis3d(rs.getInt("CHA_ISDCC"));
				bean.setScbsecurityLevel(rs.getString("SCB_SECURITYLEVEL"));
				bean.setScbxid(rs.getString("SCB_XID"));
				bean.setScbcav(rs.getString("SCB_CAV"));
				bean.setChaVpcUser(rs.getString("CHA_VPC_USER"));
				bean.setChaVpcPassword(rs.getString("CHA_VPC_PASSWORD"));
				bean.setChaObligate1(rs.getString("CHA_OBLIGATE1"));
				bean.setChaObligate2(rs.getString("CHA_OBLIGATE2"));

			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}

		return bean;
	}

	/**
	 * 查询scb通道信息
	 * 
	 * @author: Wangqian
	 * @Title queryScbChannelInfo
	 * @Time: 2014-7-15下午04:55:23
	 * @Description:
	 * @return: RefundTradeInfo
	 * @throws:
	 * @param refundDetail
	 * @return
	 */
	private RefundTradeInfo queryScbChannelInfo(RefundTradeInfo refundDetail) {
		String scbInfoSql = "select sc_mer_type,sc_mer_acquire_citycode,sc_tid,sc_mid,sc_mer_name,sc_mer_city,sc_mer_country from ccps_scbchannel_info sci where  sci.sc_cha_code =? and sci.sc_cha_currency=?";
		List<Object> scbInfoParam = new ArrayList<Object>();
		scbInfoParam.add(refundDetail.getChaCode());
		scbInfoParam.add(refundDetail.getBankCurrency());

		// 结果集
		ResultSet rs = null;
		RefundTradeInfo bean = null;

		try {
			rs = super.executeQuery(scbInfoSql.toString(), scbInfoParam
					.toArray());
			while (rs.next()) {
				// 查询结果
				bean = new RefundTradeInfo();
				bean.setScMerType(rs.getString("SC_MER_TYPE")); // f18 ---
				// 替换到scb通道信息表
				bean.setScMerAcquireCitycode(rs
						.getString("SC_MER_ACQUIRE_CITYCODE")); // f32
				bean.setScTid(rs.getString("SC_TID")); // f41
				bean.setScMid(rs.getString("SC_MID"));// f42
				bean.setScMerName(rs.getString("SC_MER_NAME"));// 43_name a
				bean.setScMerCity(rs.getString("SC_MER_CITY"));// 43_ a
				bean.setScMerCountry(rs.getString("SC_MER_COUNTRY"));// 43_ a
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}

		return bean;
	}

	/**
	 * 根据传入sql，查询序列
	 * 
	 * @author: Wangqian
	 * @Title getNumber
	 * @Time: 2014-7-15下午05:05:08
	 * @Description:
	 * @return: Long
	 * @throws:
	 * @param sql
	 * @return
	 */
	private Long getNumber(String sql) {

		// 封装参数
		List<Object> list = new ArrayList<Object>();

		// 结果集
		ResultSet rs = null;
		Long number = null;

		try {
			rs = super.executeQuery(sql.toString(), list.toArray());
			while (rs.next()) {
				// 查询结果
				number = rs.getLong("nextVal");
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
		return number;
	}

	/**
	 * 保存退款详情记录
	 * 
	 * @author: Wangqian
	 * @Title saveRefundDetail
	 * @Time: 2014-6-19下午06:21:03
	 * @Description:
	 * @return: void
	 * @throws:
	 * @param refundDetail
	 * @return
	 */
	private void saveRefundDetail(RefundTradeInfo refundDetail) {

		StringBuffer sqlInfo = InterfaceSql.SLQ_SCB_INSERT_DETAIL;
		List<Object> param = new ArrayList<Object>();

		param.add(refundDetail.getReMerNo());
		param.add(refundDetail.getReGwNo());
		param.add(refundDetail.getMerOrderNo());
		param.add(refundDetail.getReTrNo());
		param.add(refundDetail.getBankCurrency());
		param.add(refundDetail.getBankAmount());
		param.add(refundDetail.getAmountCurrency());
		param.add(refundDetail.getTrAmount());
		param.add(refundDetail.getPayBank());
		param.add(refundDetail.getTrTime());
		param.add(refundDetail.getRefundAmountTR());
		param.add(refundDetail.getRefundAmountRP());
		param.add(refundDetail.getRate());
		param.add(refundDetail.getRefundStatus());
		param.add(refundDetail.getBankInfo());
		param.add(refundDetail.getRefundReason());
		param.add(refundDetail.getBankOrderNo());
		param.add(refundDetail.getRemark());
		param.add(refundDetail.getUpId());

		super.executeUpdate(sqlInfo.toString(), param.toArray());
	}

	/**
	 * 查询勾兑IP是否设置
	 * 
	 * @author: Wangqian
	 * @Title ipCheckIsExist
	 * @Time: 2014-7-15下午05:20:15
	 * @Description:
	 * @return: boolean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	private boolean ipCheckIsExist(String remoteIp, RefundInBean refundIn) {
		// sql语句参数
		List<String> pars = new ArrayList<String>();
		pars.add(remoteIp);
		pars.add(String.valueOf(refundIn.getMerNo()));
		pars.add(String.valueOf(refundIn.getGatawayNo()));
		pars.add(String.valueOf(refundIn.getMerNo()));

		int count = Integer.valueOf(super.executeQueryByFirst(
				InterfaceSql.SQL_CHECK_IP.toString(), pars.toArray()));
		return count > 0;
	}

	/**
	 * 获取到订单信息，以及订单校验
	 * 
	 * @author: Wangqian
	 * @Title checkTradeInfo
	 * @Time: 2014-6-19上午09:10:39
	 * @Description:
	 * @return: RefundOutBean
	 * @throws:
	 * @param in
	 * @param tradeList
	 * @return
	 */
	private RefundOutBean checkTradeInfo(RefundInBean in, Traderecord td) {
		RefundOutBean out = new RefundOutBean();
		try {
			boolean isSave = false;

			if (in.getTradeNo().equals(td.getTrNo())) {
				// 判断通道不支持
				if (td.getTrBankCode().startsWith("SCB")) {
					// 不做处理
				} else if ("Ebanx".equalsIgnoreCase(td.getTrBankCode())) {
					// 不做处理
				} else {
					out = RefundInfo.refunderror("91");
					return out;
				}

				// 交易金额不正确
				if (in.getTradeAmount() != td.getTrAmount()) {
					out = RefundInfo.refunderror("15");
					return out;
				}

				// 退款金额大于交易金额
				if (in.getRefundAmount() > td.getTrAmount()) {
					out = RefundInfo.refunderror("09");
					return out;
				}

				// 交易币种不正确
				if (!in.getCurrency().equalsIgnoreCase(td.getTrCurrency())) {
					out = RefundInfo.refunderror("16");
					return out;
				}

				// 成功状态的订单才可以申请退款
				if (td.getTrStatus() != 1) {
					out = RefundInfo.refunderror("13");
					return out;
				}

				// 已勾兑订单才可以申请退款
				if (td.getTrChecked() != 1) {
					out = RefundInfo.refunderror("24");
					return out;
				}

				// 订单已冻结，不可退款
				if (td.getTrCongeal() != 0) {
					out = RefundInfo.refunderror("14");
					return out;
				}

				// 已进行保证金制表，不能退款
				if (td.getTrRsStatus() == 1 || td.getTrRsStatus() == 2) {
					out = RefundInfo.refunderror("17");
					return out;
				}

				// 退款金额大于可退金额
				double canamount = td.getTrAmount()
						- (td.getTrRefundmentAmount().doubleValue()
								+ td.getTrProtestAmount().doubleValue() + td
								.getTrCongealAmount().doubleValue());
				double canback = Double.valueOf(df.format(canamount));
				if (in.getRefundAmount() > canback) {
					out = RefundInfo.refunderror("25");
					return out;
				}

				// 退款金额大于可退金额
				UnNormalProcessBean unormal = getUnormalTotalAmount(td
						.getTrNo());
				if (unormal != null && unormal.getTotalCurrency() != null) {
					double canbackamount = td.getTrAmount()
							- (td.getTrRefundmentAmount().doubleValue()
									+ td.getTrProtestAmount().doubleValue()
									+ td.getTrCongealAmount().doubleValue() + unormal
									.getTotalAmount());
					canbackamount = Double.valueOf(df.format(canbackamount));
					if (in.getRefundAmount() > canbackamount) {
						out = RefundInfo.refunderror("25");
						return out;
					}
				}

				// 提交银行返回信息
				String bankCode = "";

				// 交易划款处理状态为待处理、已审核待复核状态时不能提交退款
				if (td.getTrTsStatus() != 0) {
					if (td.getTrTsStatus() == 1) {
						long tsId = td.getTrTsId();
						logger.info("tsId:" + tsId);
						int tsStatus = getTsStatus(tsId);
						if (tsStatus == 0 || tsStatus == 1) {
							logger.info("trade settle location 1");
							out = RefundInfo.refunderror("21");
							return out;
						} else {
							List<UnNormalProcessBean> patchList = getTradePatchSettle(td
									.getTrNo());
							boolean psflag = checkSettle(patchList);
							if (!psflag) {
								logger.info("trade settle location 2");
								out = RefundInfo.refunderror("21");
								return out;
							}
							// 保存退款信息
							isSave = saveRefundment(in, td);
							if (isSave) {
								// 调用退款方法
								bankCode = getTradeInfo(in.getBatchNo()
										.toString(), td.getTrBankCode());
								if ("00".equals(bankCode)
										|| "CO".equals(bankCode)) {
									out = RefundInfo.refunderror("00");
								} else {
									out = RefundInfo.refunderror(bankCode);

									// 异常表改为失败
									String sql = "update ccps_unnormal_process up set up.up_checkaccount = 'system',up.up_checktime = systimestamp,up.up_status = -1 where up.up_id = ?";
									List<Object> upParam = new ArrayList<Object>();
									upParam.add(in.getBatchNo());
									super.executeUpdate(sql.toString(), upParam
											.toArray());
								}
								// 判断是否是银行返回,没有返回信息，就是银行code
								if (StringUtils.isBlank(out.getDescription())) {
									out = RefundInfo.refunderror("93");
								}

								return out;
							} else { // 保存失败
								out = RefundInfo.refunderror("99");
								return out;
							}
						}
					} else {
						logger.info("trTsState:" + td.getTrTsStatus());
						List<UnNormalProcessBean> patchList = getTradePatchSettle(td
								.getTrNo());
						boolean pstflag = checkSettle(patchList);
						if (!pstflag) {
							logger.info("trade settle location 3");
							out = RefundInfo.refunderror("21");
							return out;
						}
						// 保存退款信息
						isSave = saveRefundment(in, td);
						if (isSave) {
							// 调用退款方法
							bankCode = getTradeInfo(in.getBatchNo().toString(),
									td.getTrBankCode());
							if ("00".equals(bankCode) || "CO".equals(bankCode)) {
								out = RefundInfo.refunderror("00");
							} else {
								out = RefundInfo.refunderror(bankCode);

								// 异常表改为失败
								String sql = "update ccps_unnormal_process up set up.up_checkaccount = 'system',up.up_checktime = systimestamp,up.up_status = -1 where up.up_id = ?";
								List<Object> upParam = new ArrayList<Object>();
								upParam.add(in.getBatchNo());
								super.executeUpdate(sql.toString(), upParam
										.toArray());
							}
							// 判断是否是银行返回,没有返回信息，就是银行code
							if (StringUtils.isBlank(out.getDescription())) {
								out = RefundInfo.refunderror("93");
							}

							return out;
						} else { // 保存失败
							out = RefundInfo.refunderror("99");
							return out;
						}
					}
				}

				// 代理商划款处理状态为待处理、已审核待复核状态时不能提交退款
				if (td.getTrAsStatus() != 0) {
					if (td.getTrAsStatus() == 1) {
						long asId = td.getTrAsId();
						logger.info("asId:" + asId);
						int asStatus = getAgentStatus(asId);
						if (asStatus == 0 || asStatus == 1) {
							logger.info("trade agent location 1");
							out = RefundInfo.refunderror("21");
							return out;
						} else {
							List<UnNormalProcessBean> patchList = getTradePatchSettle(td
									.getTrNo());
							boolean psflag = checkAgent(patchList);
							if (!psflag) {
								logger.info("trade agent location 2");
								out = RefundInfo.refunderror("21");
								return out;
							}
							// 保存退款信息
							isSave = saveRefundment(in, td);
							if (isSave) {
								// 调用退款方法
								bankCode = getTradeInfo(in.getBatchNo()
										.toString(), td.getTrBankCode());
								if ("00".equals(bankCode)
										|| "CO".equals(bankCode)) {
									out = RefundInfo.refunderror("00");
								} else {
									out = RefundInfo.refunderror(bankCode);

									// 异常表改为失败
									String sql = "update ccps_unnormal_process up set up.up_checkaccount = 'system',up.up_checktime = systimestamp,up.up_status = -1 where up.up_id = ?";
									List<Object> upParam = new ArrayList<Object>();
									upParam.add(in.getBatchNo());
									super.executeUpdate(sql.toString(), upParam
											.toArray());
								}
								// 判断是否是银行返回,没有返回信息，就是银行code
								if (StringUtils.isBlank(out.getDescription())) {
									out = RefundInfo.refunderror("93");
								}

								return out;
							} else { // 保存失败
								out = RefundInfo.refunderror("99");
								return out;
							}
						}
					} else {
						logger.info("trAsState:" + td.getTrAsStatus());
						List<UnNormalProcessBean> patchList = getTradePatchSettle(td
								.getTrNo());
						boolean pstflag = checkAgent(patchList);
						if (!pstflag) {
							logger.info("trade agent location 3");
							out = RefundInfo.refunderror("21");
							return out;
						}
						// 保存退款信息
						isSave = saveRefundment(in, td);
						if (isSave) {
							// 调用退款方法
							bankCode = getTradeInfo(in.getBatchNo().toString(),
									td.getTrBankCode());
							if ("00".equals(bankCode) || "CO".equals(bankCode)) {
								out = RefundInfo.refunderror("00");
							} else {
								out = RefundInfo.refunderror(bankCode);

								// 异常表改为失败
								String sql = "update ccps_unnormal_process up set up.up_checkaccount = 'system',up.up_checktime = systimestamp,up.up_status = -1 where up.up_id = ?";
								List<Object> upParam = new ArrayList<Object>();
								upParam.add(in.getBatchNo());
								super.executeUpdate(sql.toString(), upParam
										.toArray());
							}
							// 判断是否是银行返回,没有返回信息，就是银行code
							if (StringUtils.isBlank(out.getDescription())) {
								out = RefundInfo.refunderror("93");
							}

							return out;
						} else { // 保存失败
							out = RefundInfo.refunderror("99");
							return out;
						}
					}
				} // end if(td.getTrAsStatus() != 0)

				// 该通道不支持部分退款
				if (in.getRefundType() == 2
						&& in.getRefundAmount() != td.getTrAmount()) {
					if (td.getTrIsBackCHA() == 0) {
						out = RefundInfo.refunderror("20");
						return out;
					} else {
						// 保存退款信息
						isSave = saveRefundment(in, td);
						if (isSave) {
							// 调用退款方法
							bankCode = getTradeInfo(in.getBatchNo().toString(),
									td.getTrBankCode());
							if ("00".equals(bankCode) || "CO".equals(bankCode)) {
								out = RefundInfo.refunderror("00");
							} else {
								out = RefundInfo.refunderror(bankCode);

								// 异常表改为失败
								String sql = "update ccps_unnormal_process up set up.up_checkaccount = 'system',up.up_checktime = systimestamp,up.up_status = -1 where up.up_id = ?";
								List<Object> upParam = new ArrayList<Object>();
								upParam.add(in.getBatchNo());
								super.executeUpdate(sql.toString(), upParam
										.toArray());
							}
							// 判断是否是银行返回,没有返回信息，就是银行code
							if (StringUtils.isBlank(out.getDescription())) {
								out = RefundInfo.refunderror("93");
							}

							return out;
						} else { // 保存失败
							out = RefundInfo.refunderror("99");
							return out;
						}
					}
				}

				// 通过所有校验，保存退款申请信息
				isSave = saveRefundment(in, td);
				if (isSave) {
					// 调用退款方法
					bankCode = getTradeInfo(in.getBatchNo().toString(), td
							.getTrBankCode());
					if ("00".equals(bankCode) || "CO".equals(bankCode)) {
						out = RefundInfo.refunderror("00");
					} else {
						out = RefundInfo.refunderror(bankCode);

						// 异常表改为失败
						String sql = "update ccps_unnormal_process up set up.up_checkaccount = 'system',up.up_checktime = systimestamp,up.up_status = -1 where up.up_id = ?";
						List<Object> upParam = new ArrayList<Object>();
						upParam.add(in.getBatchNo());
						super.executeUpdate(sql.toString(), upParam.toArray());
					}
					// 判断是否是银行返回,没有返回信息，就是银行code
					if (StringUtils.isBlank(out.getDescription())) {
						out = RefundInfo.refunderror("93");
					}

					return out;
				} else { // 保存失败
					out = RefundInfo.refunderror("99");
					return out;
				}
			}
		} catch (Exception e) {
			logger.error(InterfaceUtil.getExceptionInfo(e));
			out = RefundInfo.refunderror("99");
		} finally {
			super.closeAll();
		}

		logger.info("trade check return code:" + out.getCode());
		return out;
	}

	/**
	 * 退款处理
	 * 
	 * @author: Wangqian
	 * @Title getTradeInfo
	 * @Time: 2014-6-20下午12:01:33
	 * @Description:
	 * @return: void
	 * @throws:
	 * @param bankCode
	 * @param upId
	 */
	private String getTradeInfo(String upId, String bankCode) {

		try {

			// 获取到需要退款的对象
			RefundTradeInfo refundTradeInfo = queryRefundTradeInfo(upId,
					bankCode);

			String refundStauts = "";

			 

			// 判断是否成功，异常表更改为审核成功未处理
			if ("00".equals(refundStauts) || "CO".equals(refundStauts)) {
				String sql = "update ccps_unnormal_process up set up.up_checkaccount = 'system',up.up_checktime = systimestamp,up.up_status = 1 where up.up_id = ?";

				List<Object> upParam = new ArrayList<Object>();
				upParam.add(upId);
				super.executeUpdate(sql.toString(), upParam.toArray());
			}

			return refundStauts;

		} catch (Exception e) {
			logger.error(InterfaceUtil.getExceptionInfo(e));
			logger.error("申请退款发生异常：批次号：" + upId + " ");
			return "99";
		}
	}

	/**
	 * Ebanx 退款
	 * 
	 * @author: Wangqian
	 * @Title processEbanxRefund
	 * @Time: 2014-6-20上午10:26:16
	 * @Description:
	 * @return: String
	 * @throws:
	 * @param refundForm
	 * @param userSession
	 * @return
	 */
	public String processEbanxRefund(RefundTradeInfo refundDetail) {
		// 返回responseCode
		String responseCode = null;
		try {

			// 解析进入码
			String accesscode = refundDetail.getUpcAccesscode();
			int index = accesscode.indexOf(",");
			String authorStr = "";
			String terminal = "";
			if (index > 0) {
				authorStr = accesscode.substring(0, index);
				terminal = accesscode.substring(index + 1);
			}
			// 银行终端号
			refundDetail.setTerminalNo(terminal);

			/**
			 * 封装银行所需信息 交易退款
			 */
			RefundObj r = new RefundObj();
			r.setOperationType("request");
			r.setChaAccesscode(refundDetail.getUpcAccesscode());
			r.setInvoiceNo(refundDetail.getBankOrderNo());
			r.setAmountLoc(String.valueOf(refundDetail.getRefundAmountRP()));
			r.setRefundReason(refundDetail.getRefundReason());
			r.setOrderNo(refundDetail.getReTrNo());
			r.setGatewayClientURL("https://www.ebanx.com/pay/ws/refund");

			// 連接银行，进行退款
			r = TransactionDaoImpl.refundEbanx(r);
			System.out.println("=========================");

			// 银行返回信息
			responseCode = r.getRespCode();
			refundDetail.setBankInfo(r.getReturnInfo());
			String remark = refundDetail.getRemark();
			if (r.getRefundId() != null) {
				remark += "ID:" + r.getRefundId() + "  refundCode:"
						+ r.getMercRefundCode();
			}
			refundDetail.setRemark(remark);

			// 更改数据库
			if (r != null && r.getRespCode() != null
					&& "CO".equals(r.getRespCode())) {
				// 退款成功
				refundDetail.setRefundStatus(1);
				// 调用保存方法
				saveRefundDetail(refundDetail);
			}

		} catch (Exception e) {
			logger.error("出现异常:" + e.getMessage()
					+ "方法:processEbanxRefund(RefundForm refundForm)");
			return "92";
		}

		return responseCode;
	}

	/**
	 * @author: Wangqian
	 * @Title refundEbanx
	 * @Time: 2014-6-20上午10:42:10
	 * @Description:
	 * @return: RefundObj
	 * @throws:
	 * @param v
	 * @return
	 */
	public static RefundObj refundEbanx(RefundObj v) {

		HttpClient httpClient = new HttpClient(); // 建立客户端连接
		PostMethod postMethod = new PostMethod(v.getGatewayClientURL());

		// 对 merchant_refund_code 设置，规则：ebanx + 流水订单号后10位 + 5位随机数，
		StringBuffer refundCode = new StringBuffer();
		refundCode.append("ebanx");
		refundCode.append(v.getOrderNo().substring(12, 22));
		refundCode.append(TransactionDaoImpl.getRandomCode(5, true));

		logger.info("ebanx发送merchant_refund_code：" + refundCode);

		NameValuePair[] data = {
				new NameValuePair("integration_key", v.getChaAccesscode()),
				new NameValuePair("operation", v.getOperationType()),
				new NameValuePair("hash", v.getInvoiceNo()),
				new NameValuePair("amount", v.getAmountLoc()),
				new NameValuePair("description", v.getRefundReason()),
				new NameValuePair("refund_id", ""),
				new NameValuePair("merchant_refund_code", refundCode.toString()) }; // 构造参数

		logger.info("ebanx发送数据：" + data.toString());

		postMethod.setRequestBody(data); // 设置发送参数
		try {
			httpClient.executeMethod(postMethod); // 处理发送

			// 验证是否发送成功
			JSONObject cj = new JSONObject(postMethod.getResponseBodyAsString());
			String status = cj.getString("status");
			if ("SUCCESS".equalsIgnoreCase(status)) {
				JSONObject paymentJSON = cj.getJSONObject("payment");
				// 银行返回交易状态 (OP:待处理 CO:成功 CA:失败)
				v.setRespCode(paymentJSON.getString("status"));
				v.setNewRefNo(paymentJSON.getString("merchant_payment_code")); // 新系统参考号
				v.setAmount_Bal(paymentJSON.getString("amount_br")); // 可退金额
				v.setAmountFor(paymentJSON.getString("amount_ext"));
				v.setHASH(paymentJSON.getString("hash"));

				JSONObject refunds = cj.getJSONObject("refund");

				// refund_id
				v.setRefundId(refunds.getString("id"));
				// merchant_refund_code
				v.setMercRefundCode(refunds.getString("merchant_refund_code"));
			} else {
				// 退款失败
				v.setRespCode(cj.getString("status_code"));
				v.setReturnInfo(cj.getString("status_message"));

			}

		} catch (HttpException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} catch (JSONException e) {
			e.printStackTrace();
		}
		return v;
	}

	/**
	 * 随机数生成
	 * 
	 * @author: Wangqian
	 * @Title getRandomCode
	 * @Time: 2014-6-20上午10:58:23
	 * @Description:
	 * @return: String
	 * @throws:
	 * @param len
	 * @param isLetterDigit
	 * @return
	 */
	public static String getRandomCode(int len, boolean isLetterDigit) {
		int number;
		char code = 0;
		String checkCode = "";
		Random random = new Random();
		boolean isLetter = false;
		boolean isDigit = false;
		for (int i = 0; i < len; i++) {
			number = random.nextInt(1000);
			if (number % 2 == 0) {
				isDigit = true;
				code = (char) ('0' + (char) (number % 10));
			} else if (number % 3 == 0) {
				isLetter = true;
				code = (char) ('A' + (char) (number % 26));
			} else {
				isLetter = true;
				code = (char) ('a' + (char) (number % 26));
			}
			checkCode += code + "";
		}
		if (isLetterDigit) {
			String temp = checkCode.substring(0, checkCode.length() - 1);
			number = random.nextInt(1000);
			if (!isLetter) {
				checkCode = temp.concat(String
						.valueOf((char) ('A' + (char) (number % 26))));
			} else if (!isDigit) {
				checkCode = temp.concat(String
						.valueOf((char) ('0' + (char) (number % 10))));
			}
		}
		return checkCode;
	}

}
