package com.lot.dao.impl;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Types;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.lot.bank.bean.BankReturnBean;
import com.lot.bank.bean.TradeInfoBean;
import com.lot.bean.BankChannelBean;
import com.lot.bean.ConstantsBean;
import com.lot.bean.CreditInfoRecurringBean;
import com.lot.bean.DccChaInfolBean;
import com.lot.bean.DomainInfoBean;
import com.lot.bean.ExtraElementBean;
import com.lot.bean.InterfaceParamBean;
import com.lot.bean.InterfaceParamName;
import com.lot.bean.MaxmindOutputs;
import com.lot.bean.MerAgentBean;
import com.lot.bean.MerchantAentBean;
import com.lot.bean.MerchantGateWayBean;
import com.lot.bean.ParamBean;
import com.lot.bean.PaymentMethodBean;
import com.lot.bean.ReturnBean;
import com.lot.bean.RiskControlBean;
import com.lot.bean.TeleSignBean;
import com.lot.bean.TerminalBean;
import com.lot.bean.UnTraderecordBean;
import com.lot.dao.InterfaceDao;
import com.lot.dao.RiskControlDao;
import com.lot.error.ErrorInfo;
import com.lot.sql.InterfaceSql;
import com.lot.sql.RiskControlSql;
import com.lot.util.BigDPayment;
import com.lot.util.EncryptUtil;
import com.lot.util.InterfaceUtil;
import com.lot.util.ParamCheck;
import com.lot.util.RiskControlUtil;
import com.lot.util.SecUtil;

/**
 * 
 * <p>
 * Title:
 * </p>
 * <p>
 * Description: 接口数据操作类
 * </p>
 * <p>
 * Copyright: Copyright (c) 2011 版权
 * </p>
 * <p>
 * Company:
 * </p>
 * 
 * <AUTHOR>
 * @version V1.0
 * @date 2011-6-27下午03:22:51
 */
public class InterfaceDaoImpl extends BaseDaoImpl implements InterfaceDao {

	Logger log = Logger.getLogger(InterfaceDaoImpl.class);

	/**
	 * 
	 * @author: kevin
	 * @Title getExtraParam
	 * @Time: 2011-7-14下午12:09:40
	 * @Description: 根据网关接入号绑定的附加角色 获取附加信息
	 * @return: List<ExtraElementBean>
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public List<ExtraElementBean> getExtraParam(ParamBean paramBean) {
		HttpServletRequest req = paramBean.getReq();
		// 查询语句参数
		List<String> pars = new ArrayList<String>();

		// 返回值
		List<ExtraElementBean> extraList = new ArrayList<ExtraElementBean>();
		/*
		 * ResultSet rs = null;
		 * 
		 * try { pars.add(paramBean.getInterfaceParamBean().getMerNo());
		 * pars.add(paramBean.getInterfaceParamBean().getGatewayNo()); rs =
		 * super.executeQuery(InterfaceSql.SQL_GET_EXTRA_ELEMENT .toString(),
		 * pars.toArray()); while (rs.next()) { ExtraElementBean extraElement =
		 * new ExtraElementBean();
		 * extraElement.setNameCn(rs.getString("EE_NAME_CN"));
		 * extraElement.setNameEn(rs.getString("EE_NAME_EN"));
		 * extraElement.setValue(rs.getString("EE_VALUE"));
		 * extraElement.setLength(rs.getInt("EE_LENGTH"));
		 * extraElement.setStatus(rs.getInt("GR_STATUS")); // 获取商户传入的值
		 * extraElement.setInComeValue(ParamCheck.exchangeStrParam(req
		 * .getParameter(rs.getString("EE_VALUE"))));
		 * extraList.add(extraElement); } } catch (Exception ex) {
		 * log.error(InterfaceUtil.getExceptionInfo(ex)); } finally {
		 * super.closeAll(); }
		 */
		return extraList;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title saveInterfaceParam
	 * @Time: 2011-7-4下午03:34:32
	 * @Description:保存接口参数
	 * @return: boolean
	 * @throws:
	 * @param returnBean
	 * @param paramBean
	 * @return
	 */
	@Override
	public ReturnBean saveInterfaceParam(ReturnBean returnBean, ParamBean paramBean) {
		Connection conn = null;
		try {
			conn = super.getConn();
			// 设置不自动提交
			conn.setAutoCommit(false);
			// 如果网关接入号为正常
			if (ConstantsBean.STATUS_FORMAL == paramBean.getTradeInfoBean().getMerchantGateWay().getGatewayNoStatus()) {
				if (!saveUnTradeRecord(conn, returnBean, paramBean)) {
					return ErrorInfo.getErrorInfo("S0001");
				}

				// 判断是否3方
				// if (ConstantsBean.PARTY_THREE !=
				// paramBean.getInterfaceType()) {
				if (!saveCreditInfo(conn, paramBean)) {
					return ErrorInfo.getErrorInfo("S0002");
				}
				// }
			} else if (ConstantsBean.STATUS_TEST == paramBean.getTradeInfoBean().getMerchantGateWay()
					.getGatewayNoStatus()) {
				if (!saveInformalRecord(conn, returnBean, paramBean)) {
					return ErrorInfo.getErrorInfo("S0003");
				}
			} else {
				if (!saveUnTradeRecord(conn, returnBean, paramBean)) {
					return ErrorInfo.getErrorInfo("S0001");
				}
			}

			// 保存附加交易信息
			if (!saveExtraTradeRecord(conn, paramBean)) {
				return ErrorInfo.getErrorInfo("S0004");
			}

			// 提交
			conn.commit();
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			return ErrorInfo.getErrorInfo("S0001");
		} finally {
			try {
				if (conn != null) {
					conn.close();
					conn = null;
				}
			} catch (Exception ex) {
				log.error(InterfaceUtil.getExceptionInfo(ex));
			}
		}
		return new ReturnBean();
	}

	/**
	 * 
	 * @author: kevin
	 * @Title saveUnTradeRecord
	 * @Time: 2011-7-1下午02:32:22
	 * @Description: 保存记录到异常交易记录表
	 * @return: boolean
	 * @throws:
	 * @param conn
	 * @param returnBean
	 * @param paramBean
	 * @return
	 */
	private boolean saveUnTradeRecord(Connection conn, ReturnBean returnBean, ParamBean paramBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();

		pars.add(paramBean.getTradeInfoBean().getTradeNo());
		pars.add(paramBean.getInterfaceParamBean().getMerNo());
		pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getOrderNo(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getOrderCurrency(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getOrderAmount(), 50));
		pars.add("");
		pars.add("");
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getReturnUrl(), 1000));
		String website = paramBean.getInterfaceParamBean().getWebSite();
		if (StringUtils.isNotBlank(website)) {
			website = InterfaceUtil.getSubString(
					RiskControlUtil.getRealDomainUrl(paramBean.getInterfaceParamBean().getWebSite()), 1000);
		}
		pars.add(website);
		pars.add(paramBean.getSubmitUrl());
		pars.add("");

		// pars.add(returnBean.getReturnCode().equalsIgnoreCase(Constants.SUCCESS_CODE)?"":DateUtil.getDateTimeNow());
		pars.add("");
		String errorCode = returnBean.getReturnCode().equalsIgnoreCase(ConstantsBean.SUCCESS_CODE) ? ""
				: returnBean.getReturnCode();
		if ("".equals(errorCode)) {
			errorCode = "E0000";
		}
		pars.add(errorCode);
		ReturnBean errorBean = ErrorInfo.getErrorInfo("E0000");
		pars.add("".equals(InterfaceUtil.getSubString(returnBean.getReturnInfoOut(), 100))
				? errorBean.getReturnInfoOut() : InterfaceUtil.getSubString(returnBean.getReturnInfoOut(), 100));
		pars.add("".equals(InterfaceUtil.getSubString(returnBean.getReturnInfoIn(), 100)) ? errorBean.getReturnInfoIn()
				: InterfaceUtil.getSubString(returnBean.getReturnInfoIn(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getRemark(), 1000));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getNotificationUrl(), 1000));
		log.info("in saveUnTradeRecord method, trade no:" + pars.get(0));
		int count = super.executeUpdate(conn, InterfaceSql.SQL_SAVE_UNTRADE_RECORD.toString(), pars.toArray());
		return count > 0;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title saveInformalRecord
	 * @Time: 2011-7-1下午02:34:02
	 * @Description:保存信息到非正式交易记录表（测试表）
	 * @return: boolean
	 * @throws:
	 * @param conn
	 * @param returnBean
	 * @param paramBean
	 * @return
	 */
	private boolean saveInformalRecord(Connection conn, ReturnBean returnBean, ParamBean paramBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();

		pars.add(paramBean.getTradeInfoBean().getTradeNo());
		pars.add(paramBean.getInterfaceParamBean().getMerNo());
		pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getOrderNo(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getOrderCurrency(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getOrderAmount(), 50));
		pars.add(ConstantsBean.TRADE_STATUS_PROCESS);
		pars.add("");
		pars.add("");
		pars.add("");
		pars.add("");
		pars.add("");
		pars.add("");
		pars.add("");
		// pars.add(returnBean.getReturnCode().equalsIgnoreCase(Constants.SUCCESS_CODE)?"":DateUtil.getDateTimeNow());
		pars.add("");
		pars.add(returnBean.getReturnCode().equalsIgnoreCase(ConstantsBean.SUCCESS_CODE) ? ""
				: returnBean.getReturnCode());
		pars.add(InterfaceUtil.getSubString(returnBean.getReturnInfoOut(), 100));
		pars.add(InterfaceUtil.getSubString(returnBean.getReturnInfoIn(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getReturnUrl(), 1000));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getWebSite(), 1000));
		pars.add(InterfaceUtil.getSubString(paramBean.getSubmitUrl(), 1000));
		pars.add("");
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getEmail(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getIp(), 50));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getIp(),50));
		if (StringUtils.isBlank(paramBean.getInterfaceParamBean().getIpCountry())) {
			RiskControlDao dao = new RiskControlDaoImpl();
			pars.add(dao.getIpCountryByIp(paramBean));
		} else {
			pars.add(paramBean.getInterfaceParamBean().getIpCountry());
		}
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getRemark(), 1000));

		int count = super.executeUpdate(conn, InterfaceSql.SQL_SAVE_INFORMAL_RECORD.toString(), pars.toArray());
		return count > 0;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title saveCreditInfo
	 * @Time: 2011-7-4下午03:25:01
	 * @Description: 保存持卡人交易信息
	 * @return: boolean
	 * @throws:
	 * @param conn
	 * @param paramBean
	 * @return
	 */
	@Override
	public boolean saveCreditInfo(Connection conn, ParamBean paramBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();

		// 加密卡号
		EncryptUtil encrypt = new EncryptUtil();

		// 卡号
		String cardNo = ParamCheck.str_null_to_empty(paramBean.getInterfaceParamBean().getCardNo());
		pars.add(paramBean.getTradeInfoBean().getTradeNo());
		// if(!"".equals(cardNo)){
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getEmail(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getPhone(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getFirstName(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getLastName(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getCountry(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getState(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getCity(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getZip(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getPhone(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getAddress(), 100));

		/*
		 * }else{ pars.add(""); pars.add(""); pars.add(""); pars.add("");
		 * pars.add(""); pars.add(""); pars.add(""); pars.add(""); pars.add("");
		 * pars.add("");
		 * 
		 * }
		 */
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getRealIp(), 50));
		if (StringUtils.isBlank(paramBean.getInterfaceParamBean().getIpCountry())) {
			RiskControlDao dao = new RiskControlDaoImpl();
			pars.add(dao.getIpCountryByIp(paramBean));
		} else {
			pars.add(paramBean.getInterfaceParamBean().getIpCountry());
		}
		// if(!"".equals(cardNo)){
		pars.add(paramBean.getInterfaceParamBean().getMerIp());

		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getIssuingBank(), 100));
		/*
		 * }else{ pars.add(""); pars.add(""); }
		 */
		pars.add("");
		pars.add("");
		pars.add("");
		pars.add("");
		pars.add(ConstantsBean.NO_STATUS);

		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getOs(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getBrower(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getLang(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getTimezone(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getResolution(), 50));

		pars.add("");
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getNewcookie(), 100));
		pars.add(paramBean.getInterfaceParamBean().getCookieFlag());

		if (!"".equals(cardNo)) {
			pars.add(SecUtil.encrypt(encrypt.getSHA256Encrypt(cardNo)));
			pars.add(InterfaceUtil.getCardNoPart(cardNo));
			pars.add(InterfaceUtil.getCardNoType(cardNo));
		} else {
			pars.add("");
			pars.add("");
			pars.add("");
		}
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getRemark(), 1000));
		// kevin 增加
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getPaymentMethod(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getEbanxName(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getEbanxEmail(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getEbanxType(), 100));

		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getQiwiUsername(), 100));
		// kevin 增加PPRO字段 2012-08-16
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getPayAccountnumber(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getPayBankcode(), 100));
		pars.add(InterfaceUtil
				.getSubString(new InterfaceUtil().rsaString(paramBean.getInterfaceParamBean().getCardNo()), 1000));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getQiwiCountryCode(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getInterfaceInfo(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getInterfaceVersion(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getEbanxcpf(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getIsMobile(), 10));

		// wangqian 增加收件人字段 2014-04-21
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipFirstName(),50));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipLastName(),50));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipPhone(),50));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipCountry(),50));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipState(),50));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipCity(),50));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipAddress(),100));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipZip(),50));
		// 证件号码 heyiyi 2014-8-11
		InterfaceUtil interfaceUtil = new InterfaceUtil();
		String creNum = paramBean.getInterfaceParamBean().getCreNum();
		if (StringUtils.isNotBlank(creNum)) {
			pars.add(paramBean.getInterfaceParamBean().getCreType());
			pars.add(InterfaceUtil.getCreNumPart(paramBean.getInterfaceParamBean().getCreNum()));
			pars.add(InterfaceUtil.getSubString(interfaceUtil.rsaString(paramBean.getInterfaceParamBean().getCreNum()),
					1000));
		} else {
			pars.add("");
			pars.add("");
			pars.add("");
		}
		String cardExpireMonth = paramBean.getInterfaceParamBean().getCardExpireMonth();
		String cardExpireYear = paramBean.getInterfaceParamBean().getCardExpireYear();
		if (StringUtils.isNotBlank(cardExpireMonth)) {
			pars.add(InterfaceUtil.getSubString(interfaceUtil.rsaString(cardExpireMonth + cardExpireYear), 1000));
		} else {
			pars.add("");
		}
		String unionPayType = paramBean.getInterfaceParamBean().getUnionPayType();
		if (StringUtils.isNotBlank(unionPayType)) {
			pars.add(paramBean.getInterfaceParamBean().getUnionBankCode());
			pars.add(unionPayType);
		} else {
			pars.add("");
			pars.add("");
		}
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getCsid(), 1000));// add
																								// 2015-03-16
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipFirstName(), 50));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipLastName(), 50));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipCountry(), 50));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipState(), 50));// add
																									// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipCity(), 50));// add
																									// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipAddress(), 100));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipZip(), 50));// add
																									// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getProductName(), 100));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getProductNum(), 50));// add
																									// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getProductDesc(), 100));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getExt1(), 100));// add
																								// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getExt2(), 100));// add
																								// 2016-05-31
		String sss = paramBean.getInterfaceParamBean().getCardSecurityCode();
//		int GatewayRecurringFalg=paramBean.getTradeInfoBean().getMerchantGateWay(). getGatewayRecurringFalg();
//		if(1==GatewayRecurringFalg) {
//			sss= paramBean.getInterfaceParamBean().getCardSecurityCode();
//		}
	
		cardExpireMonth = paramBean.getInterfaceParamBean().getCardExpireMonth();
		cardExpireYear = paramBean.getInterfaceParamBean().getCardExpireYear();

		pars.add(InterfaceUtil.getSubString(
				new InterfaceUtil().rsaString(cardNo + "," + sss + "," + cardExpireMonth + "" + cardExpireYear), 1000));// 2017-09-14
																														// add
		int count = 0;
		log.info("in saveCreditInfo method, trade no:" + pars.get(0));
		// 为空 则不做事务处理
		// 有值则做事务处理 ,区分二个地方的操作 1、非3方传值的保存 2、3方信用卡收集页面后的保存
		if (conn == null) {
			count = super.executeUpdate(InterfaceSql.SQL_SAVE_CREDIT_INFO.toString(), pars.toArray());
		} else {
			count = super.executeUpdate(conn, InterfaceSql.SQL_SAVE_CREDIT_INFO.toString(), pars.toArray());
		}
		if (!"".equals(cardNo)) {
			// 判断：如果测试商户不需要修改
			if (ConstantsBean.FORMAL_INTERFACE.equalsIgnoreCase(paramBean.getInterfaceStatus())) {
				// 修改来源网址
				if (count > 0) {
					//count = updateUnTradeRecordByWebSite(conn, paramBean);
				}
			}
		}
		if (ConstantsBean.PARTY_THREE != paramBean.getInterfaceType()) {

			if (!"".equals(cardNo)) {
				log.info("=================*******************************================");
				// 保存附加信息.. guozb 121206
				/*
				 * TempInfoBean tempInfoBean=new TempInfoBean();
				 * tempInfoBean.setCiAddress(InterfaceUtil.getSubString(
				 * paramBean.getInterfaceParamBean() .getAddress(), 100));
				 * tempInfoBean.setCiCity(InterfaceUtil.getSubString(paramBean.
				 * getInterfaceParamBean() .getCity(), 50));
				 * tempInfoBean.setCiCountry(InterfaceUtil.getSubString(
				 * paramBean.getInterfaceParamBean() .getCountry(), 50));
				 * tempInfoBean.setCiEmail(InterfaceUtil.getSubString(paramBean.
				 * getInterfaceParamBean() .getEmail(), 100));
				 * tempInfoBean.setCiFirstName(InterfaceUtil.getSubString(
				 * paramBean.getInterfaceParamBean() .getFirstName(), 50));
				 * tempInfoBean.setCiIpAddress(InterfaceUtil.getSubString(
				 * paramBean.getInterfaceParamBean() .getRealIp(), 50));
				 * tempInfoBean.setCiLastName(InterfaceUtil.getSubString(
				 * paramBean.getInterfaceParamBean() .getLastName(), 50));
				 * tempInfoBean.setCiPhone(InterfaceUtil.getSubString(paramBean.
				 * getInterfaceParamBean() .getPhone(), 50));
				 * tempInfoBean.setCiState(InterfaceUtil.getSubString(paramBean.
				 * getInterfaceParamBean() .getState(), 50));
				 * tempInfoBean.setCiTel(InterfaceUtil.getSubString(paramBean.
				 * getInterfaceParamBean() .getPhone(), 50));
				 * tempInfoBean.setCiZzip(InterfaceUtil.getSubString(paramBean.
				 * getInterfaceParamBean() .getZip(), 50));
				 * tempInfoBean.setCiTrNo(paramBean.getTradeInfoBean().
				 * getTradeNo()); //混合内容加密后的内容 cardNo+CVV+有效期 String cvv =
				 * paramBean.getInterfaceParamBean().getCardSecurityCode();
				 * cardExpireMonth =
				 * paramBean.getInterfaceParamBean().getCardExpireMonth();
				 * cardExpireYear =
				 * paramBean.getInterfaceParamBean().getCardExpireYear();
				 * tempInfoBean.setCiTempInfo(cardNo+","+cvv+","+cardExpireMonth
				 * +""+cardExpireYear); new Thread(new
				 * FunctionThread(tempInfoBean)).start();
				 */
			}
		}
		return count > 0;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title updateUnTradeRecordByWebSite
	 * @Time: 2011-10-26下午03:19:08
	 * @Description: 修改异常交易记录表中的来源网址
	 * @return: int
	 * @throws:
	 * @param conn
	 * @param paramBean
	 * @return
	 */
	public int updateUnTradeRecordByWebSite(Connection conn, ParamBean paramBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		StringBuffer rsaSb = new StringBuffer();
		rsaSb.append(paramBean.getInterfaceParamBean().getCardNo());
		// rsaSb.append(",");
		// rsaSb.append(paramBean.getInterfaceParamBean().getCardSecurityCode());
		// rsaSb.append(",");
		// rsaSb.append(paramBean.getInterfaceParamBean().getCardExpireYear());
		// rsaSb.append(paramBean.getInterfaceParamBean().getCardExpireMonth());

		String rsaValue = new InterfaceUtil().rsaString(rsaSb.toString());

		// 设置值到paramBean
		paramBean.getTradeInfoBean().setRsaValue(rsaValue);

		pars.add(rsaValue);
		pars.add(paramBean.getTradeInfoBean().getTradeNo());

		int count = 0;
		// 修改
		if (conn == null) {
			count = super.executeUpdate(InterfaceSql.SQL_UPDATE_UNTRADE_RECORD_BYWEBSITE.toString(), pars.toArray());
		} else {
			count = super.executeUpdate(conn, InterfaceSql.SQL_UPDATE_UNTRADE_RECORD_BYWEBSITE.toString(),
					pars.toArray());
		}

		return count;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title saveExtraTradeRecord
	 * @Time: 2011-7-4下午03:25:15
	 * @Description: 保存附加交易信息
	 * @return: boolean
	 * @throws:
	 * @param conn
	 * @param paramBean
	 * @return
	 */
	private boolean saveExtraTradeRecord(Connection conn, ParamBean paramBean) {
		// 为空则不保存
		if (paramBean.getInterfaceParamBean().getExtraParam() == null
				|| paramBean.getInterfaceParamBean().getExtraParam().size() < 1) {
			return true;
		}
		// sql语句参数
		List<String> pars = new ArrayList<String>();

		// 保存语句
		StringBuffer saveSql = new StringBuffer();

		// 插入字段
		StringBuffer saveColumns = new StringBuffer();

		// 插入值
		StringBuffer saveValues = new StringBuffer();

		// 流水订单号
		pars.add(paramBean.getTradeInfoBean().getTradeNo());
		// 遍历附加参数
		for (ExtraElementBean extraElement : paramBean.getInterfaceParamBean().getExtraParam()) {
			pars.add(InterfaceUtil.getSubString(extraElement.getInComeValue(), extraElement.getLength()));

			saveColumns.append(",");
			saveColumns.append(extraElement.getValue());

			saveValues.append(", ?");
		}

		// 拼接SQL语句
		saveSql.append("INSERT INTO CCPS_EXTRA_TRADERECORD (ET_ID, ET_TR_NO");
		saveSql.append(saveColumns);
		saveSql.append(") VALUES ( CCPS_EXTRA_TRADERECORD_SEQ.nextval, ? ");
		saveSql.append(saveValues);
		saveSql.append(")");

		int count = super.executeUpdate(conn, saveSql.toString(), pars.toArray());
		return count > 0;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title processError
	 * @Time: 2011-7-5下午03:13:11
	 * @Description: 处理错误信息写到数据库
	 * @return: boolean
	 * @throws:
	 * @param paramBean
	 * @param returnBean
	 * @return
	 */
	@Override
	public boolean processError(ParamBean paramBean, ReturnBean returnBean) {
		// 如果网关接入号为正常
		if (ConstantsBean.STATUS_FORMAL == paramBean.getTradeInfoBean().getMerchantGateWay().getGatewayNoStatus()) {
			updateUnTradeRecord(paramBean, returnBean);
		} else if (ConstantsBean.STATUS_TEST == paramBean.getTradeInfoBean().getMerchantGateWay()
				.getGatewayNoStatus()) {
			updateInformalRecord(paramBean, returnBean);
		} else {
			updateUnTradeRecord(paramBean, returnBean);
		}
		return true;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title processErrorByConfirm
	 * @Time: 2011-12-10上午11:38:14
	 * @Description: 延时通道风控2挡掉的处理:待确定
	 * @return: boolean
	 * @throws:
	 * @param paramBean
	 * @param returnBean
	 * @return
	 */
	@Override
	public boolean processErrorByConfirm(ParamBean paramBean, ReturnBean returnBean) {

		// sql语句参数
		List<Object> pars = new ArrayList<Object>();

		pars.add(paramBean.getInterfaceParamBean().getSelectPayment());
		if (null == paramBean.getTradeInfoBean().getBankChannel()) {
			pars.add("");
			pars.add("");
		} else {
			pars.add(paramBean.getTradeInfoBean().getBankChannel().getBankCode());
			pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelCode() == 0 ? ""
					: paramBean.getTradeInfoBean().getBankChannel().getChannelCode());
		}
		pars.add(returnBean.getReturnCode());
		pars.add(returnBean.getReturnInfoOut());
		pars.add(returnBean.getReturnInfoIn());
		pars.add(paramBean.getTradeInfoBean().getRiskInfo());
		pars.add(paramBean.getTradeInfoBean().getTotalScore());
		pars.add(paramBean.getTradeInfoBean().getTotalSetScore());
		pars.add(paramBean.getTradeInfoBean().getTradeNo());

		// 如果网关接入号为正常
		if (ConstantsBean.STATUS_FORMAL == paramBean.getTradeInfoBean().getMerchantGateWay().getGatewayNoStatus()) {
			int count = super.executeUpdate(InterfaceSql.SQL_UPDATE_UNTRADE_RECORD.toString(), pars.toArray());
			return count > 0;
		} else if (ConstantsBean.STATUS_TEST == paramBean.getTradeInfoBean().getMerchantGateWay()
				.getGatewayNoStatus()) {
			int count = super.executeUpdate(InterfaceSql.SQL_UPDATE_UNTRADE_RECORD.toString(), pars.toArray());
			return count > 0;
		}
		return true;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title updateUnTradeRecord
	 * @Time: 2011-7-1下午03:00:25
	 * @Description: 修改异常交易记录表
	 * @return: boolean
	 * @throws:
	 * @param paramBean
	 * @param returnBean
	 * @return
	 */
	private boolean updateUnTradeRecord(ParamBean paramBean, ReturnBean returnBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();

		pars.add(paramBean.getInterfaceParamBean().getSelectPayment());
		if (null == paramBean.getTradeInfoBean().getBankChannel()) {
			pars.add("");
			pars.add("");
		} else {
			pars.add(paramBean.getTradeInfoBean().getBankChannel().getBankCode());
			pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelCode() == 0 ? ""
					: paramBean.getTradeInfoBean().getBankChannel().getChannelCode());
		}
		pars.add(returnBean.getReturnCode());
		pars.add(returnBean.getReturnInfoOut());
		pars.add(returnBean.getReturnInfoIn());

		pars.add(paramBean.getTradeInfoBean().getRiskInfo());
		pars.add(paramBean.getTradeInfoBean().getTotalScore());// 累加分数
		pars.add(paramBean.getTradeInfoBean().getTotalSetScore());// 设置总分数
		pars.add(paramBean.getTradeInfoBean().getPassRiskInfo());
		pars.add(null != paramBean.getRiskId() ? paramBean.getRiskId() : 0L);
		pars.add(paramBean.getTradeInfoBean().getTradeNo());
		int count = super.executeUpdate(InterfaceSql.SQL_UPDATE_UNTRADE_RECORD.toString(), pars.toArray());
		// log.info(count+"更新异常记录表:"+paramBean.getTradeInfoBean().getRiskInfo()+paramBean.getTradeInfoBean().getTradeNo());
		return count > 0;

	}

	/**
	 * 
	 * @author: kevin
	 * @Title updateInformalRecord
	 * @Time: 2011-7-1下午03:00:59
	 * @Description: 修改非正式交易记录表（测试表）
	 * @return: boolean
	 * @throws:
	 * @param paramBean
	 * @param returnBean
	 * @return
	 */
	private boolean updateInformalRecord(ParamBean paramBean, ReturnBean returnBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();

		pars.add(paramBean.getInterfaceParamBean().getSelectPayment());
		pars.add(ConstantsBean.TRADE_STATUS_FAIL);
		pars.add(returnBean.getReturnCode());
		pars.add(returnBean.getReturnInfoOut());
		pars.add(returnBean.getReturnInfoIn());

		pars.add(paramBean.getTradeInfoBean().getRiskInfo());
		pars.add(paramBean.getTradeInfoBean().getTotalScore());
		pars.add(paramBean.getTradeInfoBean().getTotalSetScore());
		pars.add(InterfaceUtil.getSubString(paramBean.getTradeInfoBean().getPassRiskInfo(),1000));
		pars.add(paramBean.getTradeInfoBean().getTradeNo());
		int count = super.executeUpdate(InterfaceSql.SQL_UPDATE_INFORMAL_RECORD.toString(), pars.toArray());
		return count > 0;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title orderNoExist
	 * @Time: 2011-7-4下午04:01:34
	 * @Description: 判断商户订单号是否存在
	 * @return: boolean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public boolean orderNoExist(ParamBean paramBean) {
		// sql语句参数
		String merNo = paramBean.getInterfaceParamBean().getMerNo();
		String gatewayNo = paramBean.getInterfaceParamBean().getGatewayNo();
		String orderNo = paramBean.getInterfaceParamBean().getOrderNo();
		int tradeStatusfail = ConstantsBean.TRADE_STATUS_FAIL;
		String tradeNo = paramBean.getTradeInfoBean().getTradeNo();
		// 返回统计数值
		int count = 0;

		// 正常状态查询正式交易记录表 测试查询测试交易记录表
		if (ConstantsBean.STATUS_FORMAL == paramBean.getTradeInfoBean().getMerchantGateWay().getGatewayNoStatus()) {
			count = this.checkFormalOrderno(paramBean, merNo, gatewayNo, orderNo, tradeStatusfail);
		} else {
			count = this.checkTestOrderno(merNo, gatewayNo, orderNo, tradeStatusfail, tradeNo);
		}
		return count > 0;
	}

	@Override
	public boolean isWhiteElement(ParamBean paramBean) {
		// sql语句参数
		String merNo = ParamCheck.str_null_to_empty(paramBean.getInterfaceParamBean().getMerNo());
		String gatewayNo = ParamCheck.str_null_to_empty(paramBean.getInterfaceParamBean().getGatewayNo());
		String cardno = ParamCheck.str_null_to_empty(paramBean.getInterfaceParamBean().getCardNo());
		String email = ParamCheck.str_null_to_empty(paramBean.getInterfaceParamBean().getEmail()).toUpperCase();
		String ipaddress = ParamCheck.str_null_to_empty(paramBean.getInterfaceParamBean().getIp());
		String cardbin = (cardno.length() > 10) ? cardno.substring(0, 6) : cardno;
		String website = ParamCheck.str_null_to_empty(paramBean.getInterfaceParamBean().getWebSite()).toUpperCase();

		String sql = "select count(*) as vmrnum from CCPS_VIP_MER_RISK vmr where  ((vmr.vmr_key=? and upper(vmr.vmr_value)=?) or (vmr.vmr_key=? and upper(vmr.vmr_value)=?)"
				+ "or (vmr.vmr_key=? and upper(vmr.vmr_value)=?) or (vmr.vmr_key=? and upper(vmr.vmr_value)=?) or (vmr.vmr_key=? and   instr(upper(vmr.vmr_value),?)>0 )   )"
				+ " and ((vmr.mer_no=? and vmr.gw_no=? ) or (vmr.mer_no=? and vmr.gw_no=0 ) or (vmr.mer_no=0 and vmr.gw_no=0 )  )  ";

		List<Object> pars1 = new ArrayList<Object>();

		pars1.add(1);
		pars1.add(cardno);
		pars1.add(2);
		pars1.add(email);
		pars1.add(3);
		pars1.add(ipaddress);
		pars1.add(6);
		pars1.add(cardbin);
		pars1.add(4);
		pars1.add(website);
		pars1.add(merNo);
		pars1.add(gatewayNo);
		pars1.add(merNo);

		// 返回统计数值
		int count = 0;

		try {
			ResultSet rs = super.executeQuery(sql, pars1.toArray());
			if (rs.next()) {
				count = rs.getInt("vmrnum");//

			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			this.closeAll();
		}
		return count > 0;
	}

	/**
	 * 
	 * @author: peifang
	 * @Title checkFormalOrderno
	 * @Time: 2012-2-17上午10:46:57
	 * @Description: 判断商户订单号是否存在:查询正式交易记录表
	 * @return: int
	 * @throws:
	 * @param paramBean
	 * @param merNo
	 * @param gatewayNo
	 * @param orderNo
	 * @param tradeStatusfail
	 * @return
	 */
	private int checkFormalOrderno(ParamBean paramBean, String merNo, String gatewayNo, String orderNo,
			int tradeStatusfail) {

		int count = 0;
		String web = "";

		StringBuffer sql1 = new StringBuffer(
				"SELECT count(*) as ut_pending from ccps_unnormal_traderecord ut where ut.utr_mer_no=? and ut.utr_gw_no=? and ut.utr_mer_orderno=?  and ut.utr_paystarttime>=sysdate-2/(24*60) and  utr_errorcode='E0000' ");// 挡掉交易表
		List<Object> pars1 = new ArrayList<Object>();

		pars1.add(merNo);
		pars1.add(gatewayNo);
		pars1.add(orderNo);

		String web1 = "";
		if (ConstantsBean.PARTY_THREE == paramBean.getInterfaceType()
				&& paramBean.getInterfaceParamBean().getWebSite() != null
				&& !"".equals(paramBean.getInterfaceParamBean().getWebSite())
				&& !paramBean.getInterfaceParamBean().getWebSite().contains("%")) {

			web1 = RiskControlUtil.getRealDomainUrl(paramBean.getInterfaceParamBean().getWebSite());
			web1 = web.replaceAll("http://", "").replaceAll("https://", "").toLowerCase();

			sql1.append(" AND ut.utr_submiturl  like  ? ");
			pars1.add("%" + web + "%");
		}
		int count1 = 0;
		log.info("InterfaceSql.SQL_CHECK_UNFORMAL_ORDERNO=" + sql1.toString() + " web1=" + web1);
		try {
			ResultSet rs = super.executeQuery(sql1.toString(), pars1.toArray());
			if (rs.next()) {
				count1 = rs.getInt("ut_pending");// 两笔或者以上说明重复提交
				if (count1 >= 2) {
					count = count1;
				}
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			this.closeAll();
		}

		log.info("商户订单号=" + orderNo + " 在档掉表的记录为=" + count);
		if (count == 0) {
			// 如果档掉表中是否存在并发的记录.1分钟以内提交，都算并发

			StringBuffer sql = new StringBuffer(InterfaceSql.SQL_CHECK_FORMAL_ORDERNO);// 正常交易表
			List<Object> pars = new ArrayList<Object>();
			pars.add(tradeStatusfail);
			pars.add(tradeStatusfail);
			pars.add(-1);
			pars.add(merNo);
			pars.add(gatewayNo);
			pars.add(orderNo);
			pars.add(3);

			// log.info("paramBean.getInterfaceParamBean().getWebSite()="+paramBean.getInterfaceParamBean().getWebSite());
			if (ConstantsBean.PARTY_THREE == paramBean.getInterfaceType()
					&& paramBean.getInterfaceParamBean().getWebSite() != null
					&& !"".equals(paramBean.getInterfaceParamBean().getWebSite())
					&& !paramBean.getInterfaceParamBean().getWebSite().contains("%")) {

				web = RiskControlUtil.getRealDomainUrl(paramBean.getInterfaceParamBean().getWebSite());
				web = web.replaceAll("http://", "").replaceAll("https://", "").toLowerCase();

				sql.append(" AND trade.TR_WEBSITE  like  ? ");
				pars.add("%" + web + "%");
			}

			log.info("InterfaceSql.SQL_CHECK_FORMAL_ORDERNO=" + sql.toString() + " web=" + web);
			try {
				ResultSet rs = super.executeQuery(sql.toString(), pars.toArray());
				if (rs.next()) {
					count = rs.getInt("st_no_fail");
					paramBean.setIsRepay(
							rs.getInt("st_fail") > 0 ? ConstantsBean.TR_IS_REPAY_YES : ConstantsBean.TR_IS_REPAY_NO);
					// qiwi,webmoney的待处理订单允许重复支付，不挡交易
					if ("qiwi".equalsIgnoreCase(paramBean.getInterfaceParamBean().getPaymentMethod())
							|| "webmoney".equalsIgnoreCase(paramBean.getInterfaceParamBean().getPaymentMethod())) {
						count = count - rs.getInt("st_pending");
					}
				}
			} catch (Exception ex) {
				log.error(InterfaceUtil.getExceptionInfo(ex));
			} finally {
				this.closeAll();
			}

		}

		log.info("商户订单号=" + orderNo + " 在交易表的记录为=" + count);

		return count;
	}

	/**
	 * 
	 * @author: peifang
	 * @Title checkFormalOrdernoHandler
	 * @Time: 2012-2-17上午11:11:30
	 * @Description: 判断商户订单号是否存在:查询正式交易记录表处理
	 * @return: int
	 * @throws:
	 * @param paramBean
	 * @param pars
	 * @return
	 */
	private int checkFormalOrdernoHandler(ParamBean paramBean, List<Object> pars) {
		int count = 0;
		try {
			ResultSet rs = super.executeQuery(InterfaceSql.SQL_CHECK_FORMAL_ORDERNO.toString(), pars.toArray());
			if (rs.next()) {
				count = rs.getInt("st_no_fail");
				paramBean.setIsRepay(
						rs.getInt("st_fail") > 0 ? ConstantsBean.TR_IS_REPAY_YES : ConstantsBean.TR_IS_REPAY_NO);
				// qiwi,webmoney的待处理订单允许重复支付，不挡交易
				if ("qiwi".equalsIgnoreCase(paramBean.getInterfaceParamBean().getPaymentMethod())
						|| "webmoney".equalsIgnoreCase(paramBean.getInterfaceParamBean().getPaymentMethod())) {
					count = count - rs.getInt("st_pending");
				}
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			this.closeAll();
		}
		return count;
	}

	/**
	 * 
	 * @author: peifang
	 * @Title checkTestOrderno
	 * @Time: 2012-2-17上午10:47:25
	 * @Description: 判断商户订单号是否存在:查询测试交易记录表
	 * @return: int
	 * @throws:
	 * @param merNo
	 * @param gatewayNo
	 * @param orderNo
	 * @param tradeStatusfail
	 * @return
	 */
	private int checkTestOrderno(String merNo, String gatewayNo, String orderNo, int tradeStatusfail, String tradeNo) {
		List<Object> pars = new ArrayList<Object>();
		pars.add(merNo);
		pars.add(gatewayNo);
		pars.add(orderNo);
		pars.add(tradeStatusfail);
		pars.add(tradeNo);
		return Integer
				.valueOf(super.executeQueryByFirst(InterfaceSql.SQL_CHECK_TEST_ORDERNO.toString(), pars.toArray()));
	}

	/**
	 * 
	 * @author: kevin
	 * @Title orderNoExist
	 * @Time: 2011-7-27下午04:01:34
	 * @Description: 判断流水订单号是否存在
	 * @return: boolean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public boolean tradeNoIsExist(ParamBean paramBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		pars.add(paramBean.getTradeInfoBean().getTradeNo());

		int count = Integer
				.valueOf(super.executeQueryByFirst(InterfaceSql.SQL_CHECK_TRADENO.toString(), pars.toArray()));
		return count != 0;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title getTradeInfo
	 * @Time: 2011-7-4下午04:15:43
	 * @Description: 获取商户相关交易信息
	 * @return: ReturnBean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public ReturnBean getTradeInfo(ParamBean paramBean) {
		ReturnBean returnBean = new ReturnBean();
		// 保存银行通道参数
		List<BankChannelBean> bankChannelList = new ArrayList<BankChannelBean>();
		// sql语句参数
		List<Object> checkPars = new ArrayList<Object>();

		// sql语句参数
		List<Object> getPars = new ArrayList<Object>();

		// sql语句参数
		List<Object> aentPars = new ArrayList<Object>();

		// 风控sql语句参数
		List<Object> riskPars = new ArrayList<Object>();

		// 检测是否绑定通道sql
		StringBuffer checkSql = new StringBuffer(InterfaceSql.SQL_CHECK_GATEWAY_CHANNEL);

		// 获取绑定通道信息sql
		StringBuffer getSql = new StringBuffer(InterfaceSql.SQL_GET_GATEWAY_CHANNEL);

		// 结果集
		ResultSet rs = null;

		// 记录条数
		int counts = 0;
		try {

			// 初始参数
			checkPars.add(paramBean.getInterfaceParamBean().getMerNo());
			checkPars.add(paramBean.getInterfaceParamBean().getGatewayNo());

			getPars.add(paramBean.getInterfaceParamBean().getMerNo());
			getPars.add(paramBean.getInterfaceParamBean().getGatewayNo());
			getPars.add(paramBean.getInterfaceParamBean().getMerNo());
			getPars.add(paramBean.getInterfaceParamBean().getGatewayNo());

			// 3 2.5 2方处理
			if (ConstantsBean.PARTY_THREE != paramBean.getInterfaceType()) {
				// 判断是否是信用卡支付方式
				if (StringUtils.isBlank(paramBean.getInterfaceParamBean().getSelectPayment())
						|| paramBean.getInterfaceParamBean().getSelectPayment().equalsIgnoreCase("1")) {
					int cardNoType = InterfaceUtil.getCardNoType(paramBean.getInterfaceParamBean().getCardNo());
					if (cardNoType != ConstantsBean.NO_STATUS) {
						checkSql.append(" AND mercha.MC_CARDTYPE=?");
						getSql.append(" AND mercha.MC_CARDTYPE=?");

						checkPars.add(cardNoType);
						getPars.add(cardNoType);
					}
				}
			}

			// 判断是否持卡人选择了支付方式
			if (!(StringUtils.isBlank(paramBean.getInterfaceParamBean().getSelectPayment()))) {
				checkSql.append(" AND mercha.MC_PM_ID=?");
				getSql.append(" AND method.PM_ID=?");

				checkPars.add(paramBean.getInterfaceParamBean().getSelectPayment());
				getPars.add(paramBean.getInterfaceParamBean().getSelectPayment());
			}

			counts = Integer.valueOf(super.executeQueryByFirst(checkSql.toString(), checkPars.toArray()));

			// 判断是否绑定通道
			if (counts < 1) {
				return ErrorInfo.getErrorInfo("C0001");
			}

			// 排序
			getSql.append(" ORDER BY method.PM_ID ,mercha.MC_CARDTYPE");
			// 获取绑定的通道的信息以及扣率信息
			rs = super.executeQuery(getSql.toString(), getPars.toArray());
			int isopenauthor = 0;
			int isopenauthor1 = 0;
			String isauthor = ParamCheck.str_null_to_empty(paramBean.getInterfaceParamBean().getIsAuthor());
			if ("".endsWith(isauthor)) {
				isauthor = "0";
			}
			while (rs.next()) {
				BankChannelBean bankChannel = new BankChannelBean();
				PaymentMethodBean paymentMethod = new PaymentMethodBean();
				// 挂标通道标识
				bankChannel.setChaMark(rs.getInt("CHA_MARK"));
				bankChannel.setCHAAEMERNAME(rs.getString("CHA_AE_MERNAME"));
				bankChannel.setCardType(rs.getInt("MC_CARDTYPE"));
				bankChannel.setPaytype(rs.getInt("MC_PAYTYPE"));
				bankChannel.setBankId(rs.getInt("BANK_ID"));
				bankChannel.setBankCode(rs.getString("BANK_CODE"));
				bankChannel.setBankPayUrl(rs.getString("BANK_PAY_URL"));
				bankChannel.setBankReqUrl(rs.getString("BANK_REQ_URL"));
				bankChannel.setDirect(rs.getBoolean("BANK_ISDIRECT"));
				bankChannel.setChannelCode(rs.getInt("CHA_CODE"));
				bankChannel.setChannelMerNo(rs.getString("CHA_MERNO"));
				bankChannel.setChannelAccessCode(rs.getString("CHA_VPC_ACCESSCODE"));
				bankChannel.setChannelSecureCode(rs.getString("CHA_SECURE_SECRET"));
				// 增加通道用户名 2012-8-22
				bankChannel.setChannelUser(rs.getString("CHA_VPC_USER"));
				bankChannel.setChaVpcPassword(rs.getString("CHA_VPC_PASSWORD"));
				bankChannel.setChannelRate(rs.getDouble("CC_CHA_RATE"));
				// 通道保证金扣率
				bankChannel.setChaReseverRate(rs.getDouble("CC_RSRATE"));
				
				//通道交易结算周期
				bankChannel.setChaTsdate(rs.getInt("CC_TSDATE"));
				//通道保证金结算周期
                bankChannel.setChaRsdate(rs.getInt("CC_RSDATE"));
				
				bankChannel.setChannelCurrency(rs.getString("CHA_CURRENCY"));
				bankChannel.setChannelSettlementBank(rs.getString("CHA_SETTLEMENT_BANK"));
				bankChannel.setChannelIsDcc(rs.getInt("CHA_ISDCC"));
				bankChannel.setChannelIsDelay(rs.getInt("CHA_ISDELAY"));
				bankChannel.setChannelThreeParty(rs.getInt("CHA_THREEPARTY"));
				bankChannel.setChannelTwoFiveParty(rs.getInt("CHA_TWOFIVEPARTY"));
				bankChannel.setChannelTwoParty(rs.getInt("CHA_TWOPARTY"));
				bankChannel.setChannelFeeCurrency(rs.getString("CHA_FEECURRENCY"));
				bankChannel.setChannelFeeAmount(rs.getDouble("CHA_FEEAMOUNT"));
				bankChannel.setChannelFeeFail(rs.getInt("CHA_FEE_FAIL"));
				bankChannel.setChannelFeeSuccess(rs.getInt("CHA_FEE_SUCCESS"));
				bankChannel.setChannelFeeSuccessAfter(rs.getInt("CHA_FEE_SUCCESS_AFTER"));
				bankChannel.setChannelIsBack(rs.getInt("CHA_IS_BACK"));
				bankChannel.setChannelIsBackAfter(rs.getInt("CHA_IS_BACK_AFTER"));
				bankChannel.setNo3dPayment(rs.getInt("CHA_NO3DPAYMENT"));
				bankChannel.setTradeRate(rs.getDouble("MR_TRADE_RATE"));
				bankChannel.setReserverRate(rs.getDouble("MR_RESERVER_RATE"));
				bankChannel.setMerFeeCurrency(rs.getString("MR_FEECURRENCY"));
				bankChannel.setMerFeeAmount(rs.getDouble("MR_FEEAMOUNT"));
				//2019-05-23 add
				bankChannel.setMerFeeAmountfail(rs.getDouble("MR_FEEAMOUNT_FAIL"));
				
				bankChannel.setMerLowFeeAmount(rs.getDouble("MR_LOW_FEEAMOUNT"));
				bankChannel.setMerFeeFail(rs.getInt("MR_FEE_FAIL"));
				bankChannel.setMerFeeSuccess(rs.getInt("MR_FEE_SUCCESS"));
				bankChannel.setMerFeeSuccessAfter(rs.getInt("MR_FEE_SUCCESS_AFTER"));
				bankChannel.setMerIsBack(rs.getInt("MR_IS_BACK"));
				bankChannel.setMerIsBackAfter(rs.getInt("MR_IS_BACK_AFTER"));
				bankChannel.setIsThreeParty(rs.getInt("CHA_ISTHREEPARTY"));
				bankChannel.setChaObligate1(rs.getString("CHA_OBLIGATE1"));
				bankChannel.setChaObligate2(rs.getString("CHA_OBLIGATE2"));
				bankChannel.setChais3D(rs.getInt("CHA_IS_3D"));
                bankChannel.setChaMcc(rs.getString("CHA_MCC"));
				isopenauthor = rs.getInt("MC_OPEN_AUTHOR");
				if (ConstantsBean.PARTY_THREE != paramBean.getInterfaceType()) {

					isopenauthor1 = isopenauthor;
					// 是否走预受权交易 1 是 2 否 kobe 2014-11-5
					if (Integer.parseInt(isauthor) == 0 || Integer.parseInt(isauthor) == 2) {// 如果为空时或者2，就不走预授权
						isopenauthor = 0;
					} else {
						isopenauthor = 1;
					}
				}
				bankChannel.setMcOpenAuthor(isopenauthor);// 是不是走预授权通道
				bankChannel.setMcAutouthor(rs.getInt("MC_AUTO_AUTHOR"));
				bankChannel.setMcLimitAuthDay(rs.getInt("MC_LIMIT_AUTHDAY"));
				bankChannel.setChaLeastAmount(rs.getDouble("CHA_LEAST_AMOUNT"));
				bankChannel.setChaLeastAmountCurr(rs.getString("CHA_LEAST_AMOUNT_CURR"));
				bankChannel.setMcTrRate(rs.getBigDecimal("MC_TR_RATE"));
				// kobe 2014-11-10

				bankChannel.setMcSeRate(rs.getBigDecimal("MC_SE_RATE"));
				bankChannel.setMbaBankCurrency(rs.getString("MBA_BANKCURRENCY"));

				bankChannel.setChaFalg(rs.getString("CHA_FALG"));
				bankChannel.setBilladdress(rs.getString("CHA_BILLADDRESS"));
				bankChannel.setChaisAuth(rs.getInt("CHA_PRE_AUTHOR"));
				bankChannel.setMcDccChaCode(rs.getInt("MC_DCC_CHA_CODE"));
				bankChannel.setMcDccFlag(rs.getInt("MC_DCC_FLAG"));
				bankChannel.setMcDccRate(rs.getBigDecimal("MC_DCC_RATE"));
				paymentMethod.setPmId(rs.getInt("PM_ID"));
				paymentMethod.setPmName(rs.getString("PM_NAME"));
				paymentMethod.setPmLogo(rs.getBlob("PM_LOGO"));
				paymentMethod.setPmLogoLength(rs.getBlob("PM_LOGO") == null ? 0 : rs.getBlob("PM_LOGO").length());
				paymentMethod.setPmURL(rs.getString("PM_URL"));
				paymentMethod.setPmCheckURL(rs.getString("PM_CHECK_URL"));
				// 支付方式
				bankChannel.setPaymentMethod(paymentMethod);
				if(null != rs.getString("CHA_3DS_PROVIDER")){
                    bankChannel.setCha3dsProvider(rs.getInt("CHA_3DS_PROVIDER"));
                }else{
                    bankChannel.setCha3dsProvider(0);
                }
				
				bankChannelList.add(bankChannel);
			}

			// 判断是否设置扣率
			if (bankChannelList.size() < 1) {
				return ErrorInfo.getErrorInfo("C0002");
			}
			if (ConstantsBean.PARTY_THREE != paramBean.getInterfaceType()) {
				log.info("isauthor=" + isauthor);
				log.info("isopenauthor1=" + isopenauthor1);
				// 判断网关接入号是否开通的预受权
				if ("1".equals(isauthor) && isopenauthor1 == 0) {
					return ErrorInfo.getErrorInfo("I0114");
				}
			}
			// 放入参数bean中
			if (ConstantsBean.PARTY_THREE == paramBean.getInterfaceType()) {
				paramBean.getTradeInfoBean().setBankChannelList(bankChannelList);
			} else {
				// 2方 2.5方 根据对应的卡种直接获取绑定的值
				if (bankChannelList.size() > 1) {
					return ErrorInfo.getErrorInfo("C0003");
				} else {
					paramBean.getTradeInfoBean().setBankChannel(bankChannelList.get(0));
				}

				/*
				 * double fr_float_rate=0;
				 * 
				 * fr_float_rate=this.getMerfloatrate(paramBean.getTradeInfoBean
				 * ().getBankChannel(), paramBean);
				 * 
				 * log.info("fr_float_rate="+fr_float_rate);
				 */

				// paramBean.getTradeInfoBean().setMer_float_rate(fr_float_rate);

			}

			/******************************************************************************************/

			/*
			 * // sql语句参数 List<Object> aentPars1 = new ArrayList<Object>(); //
			 * 根据网关接入号获取代理商信息 -旧的代理商信息
			 * aentPars1.add(paramBean.getInterfaceParamBean().getMerNo());
			 * aentPars1.add(paramBean.getInterfaceParamBean().getGatewayNo());
			 * 
			 * rs = super.executeQuery(InterfaceSql.SQL_GET_MERCHANT_AENT
			 * .toString(), aentPars1.toArray()); MerchantAentBean merchantAent1
			 * = new MerchantAentBean(); while (rs.next()) {
			 * merchantAent1.setAentNo(rs.getInt("AM_AGENT_NO"));
			 * merchantAent1.setAentRate(rs.getDouble("AM_RATE"));
			 * merchantAent1.setAentFeeCurreny(rs.getString("AM_FEE_CURRENCY"));
			 * merchantAent1.setAentFeeAmount(rs.getDouble("AM_FEE"));
			 * merchantAent1.setAentFeeFail(rs.getInt("AM_FEE_FAIL"));
			 * merchantAent1.setAentFeeSuccess(rs.getInt("AM_FEE_SUCCESS"));
			 * merchantAent1.setAentFeeSuccessAfter(rs
			 * .getInt("AM_FEE_SUCCESS_AFTER"));
			 * merchantAent1.setAentIsBack(rs.getInt("AM_IS_BACK"));
			 * merchantAent1.setAentIsBackAfter(rs.getInt("AM_IS_BACK_AFTER"));
			 * } paramBean.getTradeInfoBean().setMerchantAent(merchantAent1);
			 */
			/******************************************************************************************/
			// 根据网关接入号获取代理商信息 - 新的代理商绑定关系信息 多级
			aentPars.add(paramBean.getInterfaceParamBean().getMerNo());
			aentPars.add(paramBean.getInterfaceParamBean().getGatewayNo());

			rs = super.executeQuery(InterfaceSql.SQL_GET_MERCHANT_AENT_NEW.toString(), aentPars.toArray());

			// 交易币种
			String ordercurrency = ParamCheck.str_null_to_empty(paramBean.getInterfaceParamBean().getOrderCurrency());
			// 该网关绑定的代理商商户信息集合 2015-01-23 add
			Map<Integer, MerchantAentBean> merAentMap = new HashMap<Integer, MerchantAentBean>();
			MerchantAentBean merchantAent = null;
			while (rs.next()) {
				merchantAent = new MerchantAentBean();
				merchantAent.setAentNo(rs.getInt("AM_AGENT_NO"));
				merchantAent.setAentRate(rs.getDouble("AM_RATE"));
				merchantAent.setAentFeeCurreny(rs.getString("AM_FEE_CURRENCY"));
				merchantAent.setAentFeeAmount(rs.getDouble("AM_FEE"));
				merchantAent.setAentFeeFail(rs.getInt("AM_FEE_FAIL"));
				merchantAent.setAentFeeSuccess(rs.getInt("AM_FEE_SUCCESS"));
				merchantAent.setAentFeeSuccessAfter(rs.getInt("AM_FEE_SUCCESS_AFTER"));
				merchantAent.setAentIsBack(rs.getInt("AM_IS_BACK"));
				merchantAent.setAentIsBackAfter(rs.getInt("AM_IS_BACK_AFTER"));

				// 代理商结算币种
				// String agentCurrency =
				// rs.getString("AGENT_ACCOUNT_CURRENCY");
				String agentCurrency = rs.getString("aba_bankcurrency");

				merchantAent.setAgentCurrency(agentCurrency);
				// 代理商级别 1 一级 2 二级 3 三级
				int agentLevel = rs.getInt("AM_AGENT_LEVEL");
				merchantAent.setAgentLevel(agentLevel);
				/*
				 * //获取代理商结算的真实汇率 Double agentRealSettleRate = null;
				 * if(agentCurrency.equals(ordercurrency)){ agentRealSettleRate
				 * = 1d; }else{ agentRealSettleRate =
				 * this.getRealSettRateAg(ordercurrency, agentCurrency,
				 * paramBean); }
				 * merchantAent.setAgentSettleRate(agentRealSettleRate);
				 */
				merAentMap.put(agentLevel, merchantAent);
			}
			paramBean.getTradeInfoBean().setMerAentMap(merAentMap);

			/******************************************************************************************/
			// 根据商户号、网关接入号获取绑定的风控
			// 保存风控
			List<RiskControlBean> riskList = new ArrayList<RiskControlBean>();

			// 步骤:1、先获取商户网关接入号 2、再获取商户所有网关接入号 3、再获取所有商户所有网关接入号
			riskPars.add(paramBean.getInterfaceParamBean().getMerNo());
			riskPars.add(paramBean.getInterfaceParamBean().getGatewayNo());

			// 1、先获取商户网关接入号 的情况
			rs = super.executeQuery(RiskControlSql.SQL_GET_MER_RISKLIST.toString(), riskPars.toArray());
			paramBean.setMerNoRisk(paramBean.getInterfaceParamBean().getMerNo());
			paramBean.setGwNoRisk(paramBean.getInterfaceParamBean().getGatewayNo());
			if (rs.next()) {
				do {
					RiskControlBean risk = new RiskControlBean();
					risk.setRiskName(rs.getString("RE_ELEMENT_NAME"));
					risk.setRiskNameF(rs.getString("RE_ELEMENT_NAME_F"));
					risk.setRiskCode(rs.getString("RE_CODE"));
					risk.setIsPass(rs.getInt("MR_ISPASS"));
					risk.setScore(rs.getDouble("MR_SCORE"));
					risk.setRiskMethod(rs.getString("RE_METHOD_NAME"));
					risk.setRiskUrl(rs.getString("RE_URL"));
					risk.setRiskPosition(rs.getInt("RE_POSITION"));

					riskList.add(risk);

				} while (rs.next());
				paramBean.setMerNoRisk(paramBean.getInterfaceParamBean().getMerNo());
				paramBean.setGwNoRisk(paramBean.getInterfaceParamBean().getGatewayNo());
			} else {
				riskPars.clear();
				riskPars.add(paramBean.getInterfaceParamBean().getMerNo());
				riskPars.add("0");
				// 2、再获取商户所有网关接入号的情况
				rs = super.executeQuery(RiskControlSql.SQL_GET_MER_RISKLIST.toString(), riskPars.toArray());
				if (rs.next()) {
					do {
						RiskControlBean risk = new RiskControlBean();
						risk.setRiskName(rs.getString("RE_ELEMENT_NAME"));
						risk.setRiskNameF(rs.getString("RE_ELEMENT_NAME_F"));
						risk.setRiskCode(rs.getString("RE_CODE"));
						risk.setIsPass(rs.getInt("MR_ISPASS"));
						risk.setScore(rs.getDouble("MR_SCORE"));
						risk.setRiskMethod(rs.getString("RE_METHOD_NAME"));
						risk.setRiskUrl(rs.getString("RE_URL"));
						risk.setRiskPosition(rs.getInt("RE_POSITION"));
						riskList.add(risk);
					} while (rs.next());
					paramBean.setMerNoRisk(paramBean.getInterfaceParamBean().getMerNo());
					paramBean.setGwNoRisk("0");
				} else {
					riskPars.clear();
					riskPars.add("0");
					riskPars.add("0");
					// 3、再获取所有商户所有网关接入号
					rs = super.executeQuery(RiskControlSql.SQL_GET_MER_RISKLIST.toString(), riskPars.toArray());
					if (rs.next()) {
						do {
							RiskControlBean risk = new RiskControlBean();
							risk.setRiskName(rs.getString("RE_ELEMENT_NAME"));
							risk.setRiskNameF(rs.getString("RE_ELEMENT_NAME_F"));
							risk.setRiskCode(rs.getString("RE_CODE"));
							risk.setIsPass(rs.getInt("MR_ISPASS"));
							risk.setScore(rs.getDouble("MR_SCORE"));
							risk.setRiskMethod(rs.getString("RE_METHOD_NAME"));
							risk.setRiskUrl(rs.getString("RE_URL"));
							risk.setRiskPosition(rs.getInt("RE_POSITION"));
							riskList.add(risk);
						} while (rs.next());
						paramBean.setMerNoRisk("0");
						paramBean.setGwNoRisk("0");
					}
				}
			}

			// 将list放到tradeInfoBean中
			paramBean.getTradeInfoBean().setRiskControlList(riskList);

			RiskControlDao rDao = new RiskControlDaoImpl();
			// 获取风控设置总分数
			rDao.getScore(paramBean, RiskControlSql.SQL_GET_TOTALSCORE.toString());

		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			return ErrorInfo.getErrorInfo("S0005");
		} finally {
			try {
				if (rs != null) {
					rs.close();
				}
			} catch (Exception ex) {

			}
			super.closeAll();
		}

		return returnBean;
	}

	public String getBankcurreny(BankChannelBean bankChannel, ParamBean paramBean) {

		String bankcurrency = "";

		// 判断传入的币种是否在通道币种中
		if (bankChannel.getChannelCurrency().indexOf(paramBean.getInterfaceParamBean().getOrderCurrency()) != -1) {

			bankcurrency = paramBean.getInterfaceParamBean().getOrderCurrency();

		} else {
			String[] currencys = bankChannel.getChannelCurrency().split(",");

			// log.info("currencys="+currencys);
			for (String currency : currencys) {

				bankcurrency = currency;
				return bankcurrency;
			}

		}
		return bankcurrency;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title getRealCurrency
	 * @Time: 2011-7-15下午06:10:51
	 * @Description: 获取币种汇率
	 * @return: double
	 * @throws:
	 * @param comeCurrency
	 * @param channelCurrency
	 * @return
	 */
	/*
	 * public Double getRealCurrency(String comeCurrency, String channelCurrency
	 * ,ParamBean paramBean) { // 查询语句参数 List<String> pars = new
	 * ArrayList<String>();
	 * 
	 * ResultSet rs = null; Double rate = null; try { if
	 * (comeCurrency.equalsIgnoreCase(channelCurrency) &&
	 * !"CNY".equalsIgnoreCase(comeCurrency)) { return Double.valueOf("1"); }
	 * pars.add(comeCurrency); pars.add(channelCurrency); // 交易划款 pars.add("1");
	 * 
	 * rs = super.executeQuery(InterfaceSql.SQL_GET_CURRENCY_RATE .toString(),
	 * pars.toArray());
	 * 
	 * while (rs.next()) { rate = rs.getDouble("RATE_VALUE"); }
	 * 
	 * if(null!=rate){ if(rate!=0 || rate !=0.0){ if( null!=paramBean){ //3% 则是
	 * rate * 1.03; -3% 则是rate / 1.03 if(null!=paramBean.getTradeInfoBean()){
	 * if(null!=paramBean.getTradeInfoBean().getBankChannel()){
	 * 
	 * // 判断条件：1、浮动类型：交易汇率浮动、 币种类型：交易币种、收单币种 GGG Double realAmout =
	 * paramBean.getTradeInfoBean().getRealAmount(); if(realAmout == null){
	 * realAmout =
	 * BigDPayment.round(Double.valueOf(paramBean.getInterfaceParamBean().
	 * getOrderAmount())* rate,2); } Double floatRate = null; floatRate =
	 * this.getFloatRate(paramBean.getInterfaceParamBean().getGatewayNo(),
	 * comeCurrency, channelCurrency, 0,
	 * Double.valueOf(paramBean.getInterfaceParamBean().getOrderAmount()),
	 * realAmout); double r = 0; if(floatRate != null){ r = floatRate; }else
	 * if(null!=paramBean.getTradeInfoBean().getBankChannel().getMcTrRate()){ r
	 * =
	 * paramBean.getTradeInfoBean().getBankChannel().getMcTrRate().doubleValue()
	 * ; } double rr= Math.abs(r)/100; if(r>0){ rate = BigDPayment.mul(rate ,
	 * (1+rr)); }else if(r<0){ rate = BigDPayment.div(rate, (1+rr)); } } } }
	 * BigDecimal bd = new BigDecimal(String.valueOf(rate)); rate =
	 * bd.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue(); //
	 * System.out.println("rate值========="+rate); } }
	 * 
	 * } catch (Exception ex) { log.error(InterfaceUtil.getExceptionInfo(ex)); }
	 * finally { super.closeAll(); } return rate; }
	 */

	/**
	 * 获取结算时真正的汇率
	 */
	@Override
	public Double getRealSettRate(String comeCurrency, String settCurrency, ParamBean paramBean) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();

		int set_rate = paramBean.getTradeInfoBean().getMerchantGateWay().getGw_sett_rate();
		log.info("set_rate=" + set_rate);

		ResultSet rs1 = null;
		Double rate = 0D;
		try {

			pars.add(comeCurrency);
			pars.add(settCurrency);
			// 划款汇率
			pars.add("2");

			rs1 = super.executeQuery(InterfaceSql.SQL_GET_CURRENCY_RATE.toString(), pars.toArray());

			while (rs1.next()) {
				if (set_rate == 1) {
					rate = rs1.getDouble("RATE_VALUE1");
				} else if (set_rate == 2) {
					rate = rs1.getDouble("RATE_VALUE2");
				} else {

					rate = rs1.getDouble("RATE_VALUE");
				}
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
		if (null == rate || rate == 0 || rate == 0.0) {
			// 如果没有设置同币种的汇率，则赋值为1
			if (comeCurrency.equalsIgnoreCase(settCurrency)) {
				// return Double.valueOf("1");
				rate = 1D;
			}
		}
		if (null != rate) {
			if (rate != 0 || rate != 0.0) {
				if (null != paramBean) { // 3% 则是 rate * 1.03; -3% 则是rate / 1.03
					if (null != paramBean.getTradeInfoBean()) {
						if (null != paramBean.getTradeInfoBean().getBankChannel()) {
							if (null != paramBean.getTradeInfoBean().getBankChannel().getMcSeRate()) {
								double r = paramBean.getTradeInfoBean().getBankChannel().getMcSeRate().doubleValue();
								double rr = Math.abs(r) / 100;
								/*
								 * if(r>0){ rate= rate * (1+rr); }else{ rate=
								 * rate / (1+rr); }
								 */

								if (r > 0) {
									rate = BigDPayment.mul(rate, (1 + rr));
								} else if (r < 0) {
									rate = BigDPayment.div(rate, (1 + rr));
								}
							}
						}

						double merSettFloatValue = paramBean.getTradeInfoBean().getMer_sett_float_rate();

						double rr1 = Math.abs(merSettFloatValue) / 100;
						log.info("商户结算浮动汇率值为=" + merSettFloatValue + " --- " + rr1);
						if (merSettFloatValue > 0) {
							rate = BigDPayment.mul(rate, (1 + rr1));
						} else if (merSettFloatValue < 0) {
							// rate = BigDPayment.div(rate, (1+rr1));
							// 改为 划款汇率*（1-X%）
							rate = BigDPayment.mul(rate, (1 - rr1));
						}
					}
				}
				BigDecimal bd = new BigDecimal(String.valueOf(rate));
				rate = bd.setScale(8, BigDecimal.ROUND_HALF_UP).doubleValue();
				// log.info("划款rate值========="+rate);
				log.info("浮动后结算rate值=========" + rate);
			}
		}

		return rate;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title getRealCurrency
	 * @Time: 2011-7-15下午06:10:51
	 * @Description: 获取币种汇率
	 * @return: double
	 * @throws:
	 * @param comeCurrency
	 * @param channelCurrency
	 * @return
	 */
	@Override
	public Double getRealCurrency(String comeCurrency, String channelCurrency, ParamBean paramBean) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();

		// 网关选择哪种交易汇率
		int trade_rate = paramBean.getTradeInfoBean().getMerchantGateWay().getGw_trade_rate();
		log.info("网关选择哪种交易汇率 =" + trade_rate);

		ResultSet rs = null;
		Double rate = 0D;
		try {

			pars.add(comeCurrency);
			pars.add(channelCurrency);
			// 交易汇率
			pars.add("1");

			rs = super.executeQuery(InterfaceSql.SQL_GET_CURRENCY_RATE.toString(), pars.toArray());

			while (rs.next()) {
				if (trade_rate == 1) {
					rate = rs.getDouble("RATE_VALUE1");
				} else if (trade_rate == 2) {
					rate = rs.getDouble("RATE_VALUE2");
				} else {
					rate = rs.getDouble("RATE_VALUE");
				}
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}

		if (comeCurrency.equalsIgnoreCase(channelCurrency) && !"CNY".equalsIgnoreCase(comeCurrency)) {
			// return Double.valueOf("1");
			rate = 1D;
		}
		log.info("原始交易汇率 =" + rate);
		if (null != rate) {
			if (rate != 0 || rate != 0.0) {
				if (null != paramBean) { // 3% 则是 rate * 1.03; -3% 则是rate / 1.03
					if (null != paramBean.getTradeInfoBean()) {

						if (null != paramBean.getTradeInfoBean().getBankChannel()) {
							if (null != paramBean.getTradeInfoBean().getBankChannel().getMcTrRate()) {
								double r = paramBean.getTradeInfoBean().getBankChannel().getMcTrRate().doubleValue();
								double rr = Math.abs(r) / 100;
								/*
								 * if(r>0){ rate= rate * (1+rr); }else{ rate=
								 * rate / (1+rr); }
								 */

								if (r > 0) {
									rate = BigDPayment.mul(rate, (1 + rr));
								} else if (r < 0) {
									rate = BigDPayment.div(rate, (1 + rr));
								}
							}
						}

						// if("CNY".equalsIgnoreCase(channelCurrency)){//如果收单币种是人民币的，才处理
						double merFloatValue = paramBean.getTradeInfoBean().getMer_float_rate();

						double rr1 = Math.abs(merFloatValue) / 100;
						log.info("商户浮动汇率值为=" + merFloatValue + " --- " + rr1);
						if (merFloatValue > 0) {
							rate = BigDPayment.mul(rate, (1 + rr1));
						} else if (merFloatValue < 0) {
							rate = BigDPayment.div(rate, (1 + rr1));
						}
						// }
					}
				}

				BigDecimal bd = new BigDecimal(String.valueOf(rate));
				rate = bd.setScale(8, BigDecimal.ROUND_HALF_UP).doubleValue();

				log.info("浮动后交易rate值=========" + rate);
			}
		}

		return rate;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title getMerGateWayInfo
	 * @Time: 2011-7-5下午03:55:02
	 * @Description: 获取商户号网关接入号信息
	 * @return: void
	 * @throws:
	 * @param paramBean
	 */
	@Override
	public void getMerGateWayInfo(ParamBean paramBean) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();

		ResultSet rs = null;

		try {
			boolean bmerno = StringUtils.isNumeric(paramBean.getInterfaceParamBean().getMerNo());// 网关接入号是否为数字
			boolean bgwo = StringUtils.isNumeric(paramBean.getInterfaceParamBean().getGatewayNo());// 网关接入号是否为数字
			String sql = "";
			if (bmerno && bgwo) {// 同时为数字时

				pars.add(paramBean.getInterfaceParamBean().getMerNo());
				pars.add(paramBean.getInterfaceParamBean().getMerNo());
				pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
				pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
				sql = InterfaceSql.SQL_GET_MER_GATEWAY_INFO.toString()
						+ " and (mc.MER_NO=? or mc.MER_NO1=? ) and (gw.GW_NO=? or gw.GW_NO1=? ) ";

			} else if (bmerno == true && bgwo == false) {// 网关不是数字
				pars.add(paramBean.getInterfaceParamBean().getMerNo());
				pars.add(paramBean.getInterfaceParamBean().getMerNo());
				pars.add(paramBean.getInterfaceParamBean().getGatewayNo());

				sql = InterfaceSql.SQL_GET_MER_GATEWAY_INFO.toString()
						+ " and (mc.MER_NO=? or mc.MER_NO1=? ) and ( gw.GW_NO1=? ) ";

			} else if (bmerno == false && bgwo == true) {// 商户号不是数字
				pars.add(paramBean.getInterfaceParamBean().getMerNo());

				pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
				pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
				sql = InterfaceSql.SQL_GET_MER_GATEWAY_INFO.toString()
						+ " and ( mc.MER_NO1=? ) and (gw.GW_NO=? or gw.GW_NO1=? ) ";

			} else if (bmerno == false && bgwo == false) {// 都不是数字
				pars.add(paramBean.getInterfaceParamBean().getMerNo());

				pars.add(paramBean.getInterfaceParamBean().getGatewayNo());

				sql = InterfaceSql.SQL_GET_MER_GATEWAY_INFO.toString() + " and ( mc.MER_NO1=? ) and ( gw.GW_NO1=? ) ";
			}

			rs = super.executeQuery(sql, pars.toArray());
			
			MerchantGateWayBean merchantGateWay = new MerchantGateWayBean();
			if (rs.next()) {
				paramBean.getInterfaceParamBean().setMerNo1(rs.getString("MER_NO"));
				paramBean.getInterfaceParamBean().setGatewayNo1(rs.getString("GW_NO"));

				merchantGateWay.setGwGroupWhite(rs.getInt("GW_GROUPWHITE"));
				merchantGateWay.setGwGroupBlack(rs.getInt("GW_GROUPBLACK"));
				merchantGateWay.setGwSecondwhite(rs.getInt("GW_SECONDWHITE"));

				merchantGateWay.setGw_cardbin(rs.getInt("gw_cardbin"));

				merchantGateWay.setGw_show_risk(rs.getInt("gw_show_risk"));
				merchantGateWay.setGw_returnbilladd(rs.getInt("gw_returnbilladd"));
				merchantGateWay.setGW_CLIENTID_ISREQ(rs.getInt("GW_CLIENTID_ISREQ"));
				merchantGateWay.setGW_RISK_METHOD(rs.getInt("gw_risk_method"));
				merchantGateWay.setGw_sett_rate(rs.getInt("gw_sett_rate"));
				merchantGateWay.setGw_trade_rate(rs.getInt("GW_TRADE_RATE"));

				merchantGateWay.setGw_checkurl_flag(rs.getInt("gw_checkurl_flag"));
				// 商户号状态
				merchantGateWay.setMerNoStatus(rs.getInt("MER_STATUS"));
				// 网关接入号状态
				merchantGateWay.setGatewayNoStatus(rs.getInt("GW_STATUS"));
				// 网关接入号Key
				merchantGateWay.setGatewayKey(rs.getString("GW_MD5KEY"));
				// 网关接入号绑定接口类型
				merchantGateWay.setGatewayInfType(rs.getInt("GW_INF_TYPE"));
				// 网关接入号是否VT商户
				merchantGateWay.setGatewayIsVt(rs.getInt("GW_ISVT"));
				// 返回商户模式
				merchantGateWay.setGatewayReturnModel(rs.getInt("GW_RETURN_MODEL"));
				merchantGateWay.setGatewaySecondPay(rs.getInt("GW_SECONDPAY"));
				merchantGateWay.setGatewayLamount(rs.getDouble("GW_LOWESTAMOUNT"));
				merchantGateWay.setGatewayLamountCurr(rs.getString("GW_LOWESTAMOUNTCUR"));
				merchantGateWay.setGatewayRecurringFalg(rs.getInt("GW_RECURRING_FLAG"));
				merchantGateWay.setGatewayPaytimes(rs.getInt("GW_PAYTIMES"));
				merchantGateWay.setGatewayCheckCvv(rs.getInt("GW_CHECKCVV_FLAG"));
				merchantGateWay.setGwGroup(rs.getInt("GW_GROUP"));
                merchantGateWay.setGwGroup2(rs.getInt("GW_GROUP2"));
                merchantGateWay.setGwGroup3(rs.getString("GW_GROUP3"));
                
				paramBean.getTradeInfoBean().setMerchantGateWay(merchantGateWay);
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
	}

	/**
	 * 
	 * @author: kevin
	 * @Title getCurrencyInfo
	 * @Time: 2011-7-8下午02:28:25
	 * @Description: 获取币种 list
	 * @return: List<String>
	 * @throws:
	 * @return
	 */
	@Override
	public List<String> getCurrencyInfo() {
		// 查询语句参数
		List<String> currencyInfo = new ArrayList<String>();
		ResultSet rs = null;

		try {
			rs = super.executeQuery(InterfaceSql.SQL_GET_CURRENCY_INFO.toString(), new Object[] {});
			while (rs.next()) {
				currencyInfo.add(rs.getString("CURR_VALUE"));
				ConstantsBean.CURRCODE.put(rs.getString("CURR_VALUE"), rs.getString("CURR_CODE"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
		return currencyInfo;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title getCardType
	 * @Time: 2011-7-15下午02:15:18
	 * @Description: 获取卡种
	 * @return: void
	 * @throws:
	 */
	@Override
	public void getCardType() {
		ResultSet rs = null;

		try {
			rs = super.executeQuery(InterfaceSql.SQL_GET_CARD_TYPE.toString(), new Object[] {});
			while (rs.next()) {
				ConstantsBean.CARD_TYPE_MAP.put(rs.getInt("CT_ID"), rs.getString("CT_VALUE"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
	}

	/**
	 * 
	 * @author: kevin
	 * @Title saveTradeRecord
	 * @Time: 2011-7-18下午04:23:51
	 * @Description: 保存信息到交易信息表
	 * @return: ReturnBean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public ReturnBean saveTradeRecord(ParamBean paramBean) {

	    Map<String, String> sysBusParamMap = new HashMap<String, String>();
        try {
            sysBusParamMap = super.getSysBusParam();
            paramBean.setSysBusParamMap(sysBusParamMap);
        } catch (Exception e) {
        }
        
		// 获取当前商户绑定银行通道的原始扣率信息
		this.getMerBankReallyRate(paramBean,1,null);

		ReturnBean returnBean = new ReturnBean();
		Connection conn = null;
		int count = 0;
		try {
			conn = super.getConn();
			
			// 查询绑定的代理商信息
			MerAgentBean agentBean = this.getMerAgentInfo(conn, paramBean);
			
			// 费用浮点数验证
	        this.checkFeeFloat(paramBean, agentBean);
	        
			// 设置不自动提交
			conn.setAutoCommit(false);
			// sql语句参数
			List<Object> pars = new ArrayList<Object>();

			// 如果是测试状态,则直接更新交易记录表,直接返回商户
			if (ConstantsBean.TEST_INTERFACE.equalsIgnoreCase(paramBean.getInterfaceStatus())) {
				// 判断状态
				if (paramBean.getTradeStatus() == 0) {
					pars.add(ConstantsBean.TRADE_STATUS_SUCCESS);
				} else {
					pars.add(paramBean.getTradeStatus());
				}

				pars.add(paramBean.getInterfaceParamBean().getSelectPayment());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getTradeRate());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getReserverRate());
				pars.add(paramBean.getTradeInfoBean().getRealRate());

				pars.add(paramBean.getTradeInfoBean().getRealCurrency());
				pars.add(paramBean.getTradeInfoBean().getRealAmount());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getBankCode());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelCode());

				pars.add(paramBean.getTradeInfoBean().getRiskInfo());
				pars.add(paramBean.getTradeInfoBean().getSetScore());
				pars.add(paramBean.getTradeInfoBean().getTotalScore());
				pars.add(InterfaceUtil.getSubString(paramBean.getTradeInfoBean().getPassRiskInfo(),1000));
				pars.add(paramBean.getTradeInfoBean().getTradeNo());
				count = super.executeUpdate(conn, InterfaceSql.SQL_UPDATE_TEST_RECORD.toString(), pars.toArray());
				returnBean = count > 0 ? returnBean : ErrorInfo.getErrorInfo("S0007");
			} else {

				pars.add(paramBean.getTradeInfoBean().getTradeNo());
				// 删除异常交易记录
				count = super.executeUpdate(conn, InterfaceSql.SQL_DELETE_UNTRADE_RECORD.toString(), pars.toArray());
				if (count == 0) {
					return ErrorInfo.getErrorInfo("S0008");
				}

				// 当同商户号、网关接入号和商户订单号，如之前存在失败的记录，则将之前的记录，标记为“重复支付”处理
				count = this.merOrderRepeatCommitHandler(conn, pars, paramBean);
				if (count == 0) {
					return ErrorInfo.getErrorInfo("S0015");
				}

				// 保存交易记录表
				pars.clear();
				pars.add(paramBean.getTradeInfoBean().getTradeNo());
				pars.add(paramBean.getInterfaceParamBean().getOrderNo());
				pars.add(paramBean.getInterfaceParamBean().getMerNo());
				pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
				pars.add(paramBean.getInterfaceParamBean().getOrderCurrency());
				pars.add(paramBean.getInterfaceParamBean().getOrderAmount());

				// 判断状态
				if (paramBean.getTradeStatus() == 0) {
					pars.add(ConstantsBean.TRADE_STATUS_PROCESS);
				} else {
					pars.add(paramBean.getTradeStatus());
				}
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getTradeRate());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeCurrency());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeAmount());
				//2019-05-23 add
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeAmountfail());
				
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerLowFeeAmount());

				/** 原代理商信息不再保存 */
				pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentNo());
				pars.add(paramBean.getAgentRate());
				pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentFeeCurreny());
				pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentFeeAmount());

				pars.add(paramBean.getTradeInfoBean().getBankChannel().getReserverRate());
				pars.add(paramBean.getTradeInfoBean().getRealRate());
				pars.add(paramBean.getTradeInfoBean().getRealCurrency());
				pars.add(paramBean.getTradeInfoBean().getRealAmount());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getBankCode());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelCode());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelIsDelay());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelRate());
				//通道保证金扣率
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChaReseverRate());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeCurrency());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeAmount());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelSettlementBank());
				pars.add(paramBean.getEnterPayTime());
				pars.add(paramBean.getEnterPageTime());
				pars.add(paramBean.getInterfaceParamBean().getReturnUrl());
				// 来源网址增加rsa值
				// pars.add(paramBean.getInterfaceParamBean().getWebSite() + ","
				// + paramBean.getTradeInfoBean().getRsaValue());
				String web = paramBean.getInterfaceParamBean().getWebSite();
				// log.info("保存web="+web);
				// log.info("保存getReturnUrl="+paramBean.getInterfaceParamBean().getReturnUrl());
				if (web != null && !web.trim().equals("")) {
					web = web.toLowerCase();
				} else {
					web = paramBean.getInterfaceParamBean().getReturnUrl();
					if (web != null && !web.trim().equals("")) {
						web = web.toLowerCase();
					}
				}
				;
				pars.add(web);
				pars.add(paramBean.getSubmitUrl());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelIsDcc());
				pars.add(paramBean.getInterfaceType());
				// 商户
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeFail());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeSuccess());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeSuccessAfter());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerIsBack());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerIsBackAfter());
				/**
				 * 原代理商信息不再保存 // 代理商其他信息
				 */

				pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentFeeFail());
				pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentFeeSuccess());
				pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentFeeSuccessAfter());
				pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentIsBack());
				pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentIsBackAfter());

				// 通道
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeFail());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeSuccess());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeSuccessAfter());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelIsBack());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelIsBackAfter());

				// 卡种
				pars.add(InterfaceUtil.getCardNoType(paramBean.getInterfaceParamBean().getCardNo()));
				pars.add(paramBean.getInterfaceParamBean().getRemark());

				// 风控信息
				pars.add(paramBean.getTradeInfoBean().getRiskInfo());

				pars.add(paramBean.getTradeInfoBean().getTotalScore());
				pars.add(paramBean.getTradeInfoBean().getTotalSetScore());

				// 已通过风控里面加上，持卡人新卡限定首次挡掉后，二次判断通道的标识
				String passRiskInfo = paramBean.getTradeInfoBean().getPassRiskInfo();
				if (StringUtils.isNotBlank(paramBean.getNewCardLog())) {
					passRiskInfo = passRiskInfo + paramBean.getNewCardLog();
				}
				// 已通过风控加上跨组限定信息
				if (StringUtils.isNotBlank(paramBean.getGwgroupMark())) {
					passRiskInfo = passRiskInfo + " " + paramBean.getGwgroupMark();
				}
				if (StringUtils.isNotBlank(passRiskInfo) && passRiskInfo.length() > 1000) {
					passRiskInfo = passRiskInfo.substring(0, 999);
				}
				pars.add(passRiskInfo);

				// 支付方式
				pars.add(paramBean.getInterfaceParamBean().getSelectPayment());

				// 是否使用预授权
				pars.add(paramBean.getTradeInfoBean().getAuthTypeStatus());

				pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getNotificationUrl(), 1000));
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getMcTrRate());

				pars.add(paramBean.getTradeInfoBean().getMer_float_rate());// 商户浮动汇率
				pars.add(paramBean.getTradeInfoBean().getMer_sett_float_rate());// 商户浮动汇率

				pars.add(paramBean.getTradeInfoBean().getBankChannel().getMcSeRate());
				pars.add(paramBean.getTradeInfoBean().getBankChannel().getMbaBankCurrency());
				pars.add(paramBean.getTradeInfoBean().getRealSettRate());
				pars.add(paramBean.getTradeInfoBean().getRealSettRate());
				
				// 保存代理商信息
				pars.add(agentBean.getAgentNo1());
				pars.add(agentBean.getAgentNo2());
				pars.add(agentBean.getAgentNo3());
				pars.add(agentBean.getAgentCurrency1());
				pars.add(agentBean.getAgentCurrency2());
				pars.add(agentBean.getAgentCurrency3());
				pars.add(agentBean.getAgentRealSettleRate1());
				pars.add(agentBean.getAgentRealSettleRate2());
				pars.add(agentBean.getAgentRealSettleRate3());

				// 新卡风控校验标识 1-商户提交 2-不在金额段内 3-发卡行国家是白名单
				if (null != paramBean.getNewCardType() && paramBean.getNewCardType() > 0) {
					// 是否B转A ， 0-否 1-是
					pars.add(1);
				} else {
					pars.add(0);
				}
				// 实时保存通道挂标标识：0-无
				if (null != paramBean.getTradeInfoBean().getBankChannel().getChaMark()) {
					pars.add(paramBean.getTradeInfoBean().getBankChannel().getChaMark());
				} else {
					pars.add(0);
				}
				// 是否收取异常金额的保证金
				if (null != paramBean.getTradeInfoBean().getBankChannel().getMerIsUnReserFee()) {
					pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerIsUnReserFee());
				} else {
					pars.add(0);
				}
				
				// 通道交易结算周期
                if (null != paramBean.getTradeInfoBean().getBankChannel().getChaTsdate()) {
                    pars.add(paramBean.getTradeInfoBean().getBankChannel().getChaTsdate());
                } else {
                    pars.add(0);
                }
                // 通道保证金结算周期
                if (null != paramBean.getTradeInfoBean().getBankChannel().getChaRsdate()) {
                    pars.add(paramBean.getTradeInfoBean().getBankChannel().getChaRsdate());
                } else {
                    pars.add(0);
                }
                pars.add(paramBean.getFeeFloatRate());
                // 3D交易标识
                pars.add(0);
                // DM交易标识
                pars.add(paramBean.getDmFlag());
				count = super.executeUpdate(conn, InterfaceSql.SQL_SAVE_TRADE_RECORD_NEW.toString(), pars.toArray());

				if (count == 0) {
					return ErrorInfo.getErrorInfo("S0009");
				}
			}

			// 提交
			conn.commit();
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			return ErrorInfo.getErrorInfo("S0006");
		} finally {
			super.closeAll();
			if (conn != null) {
				try {
					conn.close();
				} catch (SQLException e) {
					log.error(InterfaceUtil.getExceptionInfo(e));
				}
				conn = null;
			}
		}
		return returnBean;
	}

	/**
	 * 
	 * @author: peifang
	 * @Title merOrderRepeatCommitHandler
	 * @Time: 2012-2-17上午11:55:54
	 * @Description: 当同商户号、网关接入号和商户订单号，如之前存在失败的记录，则将之前的记录，标记为“重复支付”
	 * @return: int
	 * @throws:
	 * @param conn
	 * @param pars
	 * @param paramBean
	 * @return
	 */
	private int merOrderRepeatCommitHandler(Connection conn, List<Object> pars, ParamBean paramBean) {
		int cnt = 1;
		if (ConstantsBean.TR_IS_REPAY_YES == paramBean.getIsRepay()) {
			pars.clear();
			pars.add(ConstantsBean.TR_IS_REPAY_YES);
			pars.add(paramBean.getInterfaceParamBean().getMerNo());
			pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
			pars.add(paramBean.getInterfaceParamBean().getOrderNo());
			cnt = super.executeUpdate(conn, InterfaceSql.SQL_UPDATE_TR_IS_REPAY.toString(), pars.toArray());
		}
		return cnt;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title getDomainInfo
	 * @Time: 2011-7-26下午03:03:42
	 * @Description: 获取商户绑定域名信息
	 * @return: ReturnBean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public ReturnBean getDomainInfo(ParamBean paramBean) {
		ReturnBean returnBean = new ReturnBean();

		// 获取邮件域名参数
		List<String> pars = new ArrayList<String>();

		// 结果集
		ResultSet rs = null;

		try {
			// 获取绑定域名信息
			pars.add(paramBean.getInterfaceParamBean().getMerNo());
			pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
			pars.add(paramBean.getInterfaceParamBean().getMerNo());

			rs = super.executeQuery(InterfaceSql.SQL_GET_DOMAIN.toString(), pars.toArray());

			DomainInfoBean domainInfo = new DomainInfoBean();

			boolean flag = true;
			// 判断是否获取到记录
			while (rs.next()) {
				// 如果是移动端
				if ("1".equals(paramBean.getInterfaceParamBean().getIsMobile())) {
					if (rs.getInt("MD_ISMOBILE") == 1) {
						domainInfo.setDomainName(rs.getString("D_NAME"));
						domainInfo.setDomainUrl(rs.getString("D_URL"));
						domainInfo.setDomainIp(rs.getString("D_IP"));
						break;
					} else {
						if (flag) {
							flag = false;
							domainInfo.setDomainName(rs.getString("D_NAME"));
							domainInfo.setDomainUrl(rs.getString("D_URL"));
							domainInfo.setDomainIp(rs.getString("D_IP"));
						}
					}
				} else { // PC端
					if (rs.getInt("MD_ISMOBILE") == 0) {
						domainInfo.setDomainName(rs.getString("D_NAME"));
						domainInfo.setDomainUrl(rs.getString("D_URL"));
						domainInfo.setDomainIp(rs.getString("D_IP"));
						break;
					}
				}
			}
			if (domainInfo.getDomainName() == null || "".equals(domainInfo.getDomainName())) {
				return ErrorInfo.getErrorInfo("C0007");
			}

			paramBean.getTradeInfoBean().setDomainInfo(domainInfo);
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			return ErrorInfo.getErrorInfo("S0010");
		} finally {
			super.closeAll();
		}
		return returnBean;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title isTestCard
	 * @Time: 2011-9-26下午05:38:41
	 * @Description: 判断是否测试卡
	 * @return: boolean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public boolean isTestCard(ParamBean paramBean) {
		// sql语句参数
		/*
		 * List<Object> pars = new ArrayList<Object>();
		 * pars.add(paramBean.getInterfaceParamBean().getMerNo());
		 * pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
		 * pars.add(paramBean.getInterfaceParamBean().getCardNo());
		 * 
		 * int count = Integer.valueOf(super.executeQueryByFirst(InterfaceSql.
		 * SQL_IS_TEST_CARD .toString(), pars.toArray())); return count != 0;
		 */
		return false;
	}

	/**
	 * 
	 * @author: peifang
	 * @Title updateTrReference
	 * @Time: 2012-2-7上午11:43:31
	 * @Description: 发送银行前更新交易序列号(提交到银行的唯一值 )如：YESPAYMENTS为12位 CHINAPAY为16位
	 * @return: void
	 * @throws:
	 * @param paramBean
	 */
	@Override
	public boolean updateTrReference(ParamBean paramBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();

		pars.add(paramBean.getTradeInfoBean().getTrReference());
		pars.add(paramBean.getTradeInfoBean().getTradeNo());

		int count = super.executeUpdate(InterfaceSql.SQL_UPDATE_TR_REFERENCE.toString(), pars.toArray());
		return count > 0;

	}
	
	/**
	 * 根据上游返回的真实交易银行，查询扣率并更新
	 */
	@Override
    public boolean updateOrgbankcode(ParamBean paramBean, String flag, BankReturnBean bankReturnBean) {
        // sql语句参数
        List<Object> pars = new ArrayList<Object>();
        String bankflag = "";
        if ("1".equals(flag)) {
            bankflag = "Grepay";
        } else if ("2".equals(flag)) {
            bankflag = "JTNSH_BANK";
        } else if ("3".equals(flag)) {
            bankflag = "ShouXinYiPay";
        } else if ("4".equals(flag)) {
            bankflag = "MasaPay";
        } else if ("5".equals(flag)) {
            bankflag = "BF_MPGS";
        } else if ("6".equals(flag)) {
            bankflag = "BF_BANK";
        } else if ("7".equals(flag)) {
            bankflag = "Paycorp3D";
        } else if ("8".equals(flag)) {
            bankflag = "HRB_BANK";
        } else if ("9".equals(flag)) {
            bankflag = "ABC";
        } else if ("10".equals(flag)) {
            bankflag = "JT_BANK";
        } else if ("11".equals(flag)) {
            bankflag = "Cybersource";
        }
        int count = 0;
        // log.info("paramBean="+paramBean);
        // log.info("paramBean.getTradeInfoBean="+paramBean.getTradeInfoBean());
        //// log.info("paramBean.getTradeInfoBean.getBankChannel="+paramBean.getTradeInfoBean().getBankChannel());
        paramBean.getTradeInfoBean().getBankChannel().setBankCode(bankflag);
        Map<String, Double> rate = this.getMerBankReallyRate(paramBean, 2, bankReturnBean);
        if (rate != null) {
            log.info("(第三方渠道)获取到跨通道设置了 读设置扣率" + paramBean.getTradeInfoBean().getTradeNo());
            double merTradeRate = paramBean.getTradeInfoBean().getBankChannel().getTradeRate();
            double merReserverRate = paramBean.getTradeInfoBean().getBankChannel().getReserverRate();
            int merIsUnReserFee = paramBean.getTradeInfoBean().getBankChannel().getMerIsUnReserFee();
            // 通道交易结算周期
            int chaTsdate = paramBean.getTradeInfoBean().getBankChannel().getChaTsdate();
            // 通道保证金结算周期
            int chaRsdate = paramBean.getTradeInfoBean().getBankChannel().getChaRsdate();
            log.info("merTradeRate=" + merTradeRate);
            log.info("merReserverRate=" + merReserverRate);
            log.info("merIsUnReserFee=" + merIsUnReserFee);
            log.info("chaTsdate=" + chaTsdate);
            log.info("chaRsdate=" + chaRsdate);
            pars.add(bankflag);
            pars.add(merTradeRate);
            pars.add(merReserverRate);
            pars.add(merIsUnReserFee);
            pars.add(chaTsdate);
            pars.add(chaRsdate);
            pars.add(paramBean.getTradeInfoBean().getTradeNo());
            count = super.executeUpdate(
                    " UPDATE CCPS_TRADERECORD SET TR_ORG_BANKCODE = ? , tr_trade_rate=? , tr_resever_rate=? , tr_un_resever=?,TR_BANKTSDATE=?,TR_BANKRSDATE=? WHERE TR_NO = ?",
                    pars.toArray());
        } else {
            log.info("(第三方渠道)未获取到跨通道设置扣率，只更新银行=" + paramBean.getTradeInfoBean().getTradeNo());
            pars.add(bankflag);
            pars.add(paramBean.getTradeInfoBean().getTradeNo());
            count = super.executeUpdate("UPDATE CCPS_TRADERECORD SET TR_ORG_BANKCODE = ? WHERE TR_NO = ?",
                    pars.toArray());
        }
        return count > 0;
    }

	@Override
	public boolean updateTrReferenceandinvoce(ParamBean paramBean, String invoceno) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		String str = "UPDATE CCPS_TRADERECORD SET TR_REFERENCE = ?,TR_SF_DATA= ? WHERE TR_NO = ?";
		pars.add(paramBean.getTradeInfoBean().getTrReference());
		pars.add(invoceno);
		pars.add(paramBean.getTradeInfoBean().getTradeNo());
		int count = super.executeUpdate(str, pars.toArray());

		// log.info("str="+str+" pars="+pars);
		return count > 0;

	}

	/**
	 * @author: peifang
	 * @Title updateTrReferenceAndTrBankorderno
	 * @Time: 2012-6-14上午09:52:16
	 * @Description: 与银行多次交互时保存银行交易码及发送银行交易序列号(提交到银行的唯一值 )
	 * @return: boolean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public boolean updateTrReferenceAndTrBankorderno(ParamBean paramBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		// 交易关联号
		pars.add(paramBean.getTradeInfoBean().getTrReference());
		// 银行订单号
		pars.add(paramBean.getTradeInfoBean().getTrBankorderno());
		pars.add(paramBean.getTradeInfoBean().getTradeNo());

		int count = super.executeUpdate(InterfaceSql.SQL_UPDATE_TR_REFERENCE_AND_TR_BANKORDERNO.toString(),
				pars.toArray());
		return count > 0;

	}

	/**
	 * 获取MAXMIND信息
	 * 
	 * @author: Jadehu
	 * @Title getMaxmindInfo
	 * @Time: 2012-2-25下午12:03:05
	 * @Description:
	 * @return: MaxmindOutputs
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public MaxmindOutputs getMaxmindInfo(String tradeNo) {
		MaxmindOutputs mo = null;
		ResultSet rs = null;
		// sql语句参数
		List<String> pars = new ArrayList<String>();

		try {
			pars.add(tradeNo);

			rs = super.executeQuery(RiskControlSql.SQL_GET_CCPS_MAXMIND_OUTPUTS.toString(), pars.toArray());
			if (rs.next()) {
				mo = new MaxmindOutputs();
				mo.setMopTrNo(rs.getString("MOP_TR_NO"));
				mo.setMopCountrymatch(rs.getString("MOP_COUNTRYMATCH"));
				mo.setMopCountrycode(rs.getString("MOP_COUNTRYCODE"));
				mo.setMopHighriskcountry(rs.getString("MOP_HIGHRISKCOUNTRY"));
				mo.setMopDistance(rs.getString("MOP_DISTANCE"));
				mo.setMopIpRegion(rs.getString("MOP_IP_REGION"));
				mo.setMopIpCity(rs.getString("MOP_IP_CITY"));
				mo.setMopIpLatitude(rs.getString("MOP_IP_LATITUDE"));
				mo.setMopIpLongitude(rs.getString("MOP_IP_LONGITUDE"));
				mo.setMopIpIsp(rs.getString("MOP_IP_ISP"));
				mo.setMopIpOrg(rs.getString("MOP_IP_ORG"));
				mo.setMopAnonymousproxy(rs.getString("MOP_ANONYMOUSPROXY"));
				mo.setMopProxyScore(rs.getString("MOP_PROXY_SCORE"));
				mo.setMopTransProxy(rs.getString("MOP_TRANS_PROXY"));
				mo.setMopFreemail(rs.getString("MOP_FREEMAIL"));
				mo.setMopCarderemail(rs.getString("MOP_CARDEREMAIL"));
				mo.setMopHighriskusername(rs.getString("MOP_HIGHRISKUSERNAME"));
				mo.setMopHighriskpassword(rs.getString("MOP_HIGHRISKPASSWORD"));
				mo.setMopBinmatch(rs.getString("MOP_BINMATCH"));
				mo.setMopBingcountry(rs.getString("MOP_BINGCOUNTRY"));
				mo.setMopBinnamematch(rs.getString("MOP_BINNAMEMATCH"));
				mo.setMopBinname(rs.getString("MOP_BINNAME"));
				mo.setMopBinphonematch(rs.getString("MOP_BINPHONEMATCH"));
				mo.setMopBinphone(rs.getString("MOP_BINPHONE"));
				mo.setMopPhonebills(rs.getString("MOP_PHONEBILLS"));
				mo.setMopShipforward(rs.getString("MOP_SHIPFORWARD"));
				mo.setMopCitypostalmatch(rs.getString("MOP_CITYPOSTALMATCH"));
				mo.setMopShipcitypmatch(rs.getString("MOP_SHIPCITYPMATCH"));
				mo.setMopScore(rs.getString("MOP_SCORE"));
				mo.setMopQueriesremaining(rs.getString("MOP_QUERIESREMAINING"));
				mo.setMopMaxmindid(rs.getString("MOP_MAXMINDID"));
				mo.setMopError(rs.getString("MOP_ERROR"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}

		return mo;

	}

	/**
	 * 
	 * @author: kevin
	 * @Title serverIpIsExist
	 * @Time: 2012-3-26下午05:46:56
	 * @Description: 判断服务器IP是否在商户设置里
	 * @return: boolean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public boolean serverIpIsExist(ParamBean paramBean) {
		// sql语句参数
		List<String> pars = new ArrayList<String>();
		pars.add(paramBean.getInterfaceParamBean().getServerIp());
		pars.add(paramBean.getInterfaceParamBean().getMerNo());
		pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
		pars.add(paramBean.getInterfaceParamBean().getMerNo());

		int count = Integer
				.valueOf(super.executeQueryByFirst(InterfaceSql.SQL_CHECK_SERVERIP.toString(), pars.toArray()));
		return count > 0;
	}

	/**
	 * 保存持卡人的相关信息到CCPS_CREDITINFO_RECURRING表中
	 * 
	 * @author: max
	 * @Title saveCreditCardRecurringInfo
	 * @Time: Jun 5, 20123:18:18 PM
	 * @Description:
	 * @return: boolean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public ReturnBean saveCreditCardRecurringInfo(ParamBean paramBean) {
		ReturnBean returnBean = new ReturnBean();
		Connection conn = null;
		int count = 0;
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		try {
			conn = super.getConn();
			String sql = InterfaceSql.SQL_SAVE_CREDITINTO_RECURRING.toString();
			// 商户号
			pars.add(Integer.parseInt(paramBean.getInterfaceParamBean().getMerNo()));
			// 持卡人唯一标识
			pars.add(paramBean.getInterfaceParamBean().getCustomerID());
			// 混合内容加密后的内容 cardNo+CVV+有效期
			InterfaceUtil interfaceUtil = new InterfaceUtil();
			// cardNo
			String cardNo = paramBean.getInterfaceParamBean().getCardNo();
			// CVV
			// String cvv =
			// paramBean.getInterfaceParamBean().getCardSecurityCode();
			// 有效月
			String cardExpireMonth = paramBean.getInterfaceParamBean().getCardExpireMonth();
			// 有效年
			String cardExpireYear = paramBean.getInterfaceParamBean().getCardExpireYear();
			// RSA公钥加密
			// String creditCardEncript =
			// interfaceUtil.rsaString(cardNo+","+cvv+","+cardExpireMonth+","+cardExpireYear);
			// String creditCardEncript = interfaceUtil.rsaString(cardNo);

			String creditCardEncript = interfaceUtil.rsaString(cardNo + "," + cardExpireMonth + "," + cardExpireYear);

			// log.info("------rsa 加密后的信息 = "+creditCardEncript);
			pars.add(creditCardEncript);
			// 持卡人名
			pars.add(paramBean.getInterfaceParamBean().getFirstName());
			// 持卡人姓
			pars.add(paramBean.getInterfaceParamBean().getLastName());
			// 账单地址
			pars.add(paramBean.getInterfaceParamBean().getAddress());
			// 账单城市
			pars.add(paramBean.getInterfaceParamBean().getCity());
			// 账单州/省
			pars.add(paramBean.getInterfaceParamBean().getState());
			// 账单国家
			pars.add(paramBean.getInterfaceParamBean().getCountry());
			// 邮政编码
			pars.add(paramBean.getInterfaceParamBean().getZip());
			// 发卡行
			pars.add(paramBean.getInterfaceParamBean().getIssuingBank());
			// 邮箱
			pars.add(paramBean.getInterfaceParamBean().getEmail());
			// 电话
			pars.add(paramBean.getInterfaceParamBean().getPhone());
			// 接口类型
			pars.add(paramBean.getInterfaceType());
			pars.add(Integer.parseInt(paramBean.getInterfaceParamBean().getGatewayNo()));
			// 流水订单号
			pars.add(paramBean.getTradeInfoBean().getTradeNo());
			count = super.executeUpdate(conn, sql, pars.toArray());
			if (count == 0) {
				return ErrorInfo.getErrorInfo("I0074");
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			return ErrorInfo.getErrorInfo("I0074");
		} finally {
			super.closeAll();
			if (conn != null) {
				try {
					conn.close();
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					log.error(InterfaceUtil.getExceptionInfo(e));
				}
				conn = null;
			}
		}
		return returnBean;
	}

	/**
	 * 获取持卡人唯一标识对应的记录实体
	 * 
	 * @author: max
	 * @Title getCreditInfoRecurringBean
	 * @Time: Jun 5, 20123:31:29 PM
	 * @Description:
	 * @return: CreditInfoRecurringBean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public ReturnBean getCreditInfoRecurringBean(ParamBean paramBean) {
		ReturnBean returnBean = new ReturnBean();
		CreditInfoRecurringBean creditInfoRecurringBean = null;
		ResultSet rs = null;

		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		try {
			// 持卡人唯一标识
			pars.add(paramBean.getInterfaceParamBean().getCustomerID());
			pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
			pars.add(paramBean.getInterfaceType());
			//log.info("InterfaceSql.SQL_GET_CREDITINTO_RECURRING.toString(), pars.toArray()"+InterfaceSql.SQL_GET_CREDITINTO_RECURRING.toString() +";"+pars.toString());
			rs = super.executeQuery(InterfaceSql.SQL_GET_CREDITINTO_RECURRING.toString(), pars.toArray());
			if (rs.next()) {
				creditInfoRecurringBean = new CreditInfoRecurringBean();
				/*
				 * creditInfoRecurringBean.setCrID(rs.getInt("CR_ID"));
				 * creditInfoRecurringBean.setCrMerNo(rs.getInt("CR_MER_NO"));
				 * creditInfoRecurringBean.setCrCustomerId(rs.getString(
				 * "CR_CUSTOMER_ID"));
				 * creditInfoRecurringBean.setCrCreditCardEncript(rs.getString(
				 * "CR_CREDIT_CARD_ENCRIPT"));
				 * creditInfoRecurringBean.setCrFirstName(rs.getString(
				 * "CR_FIRST_NAME"));
				 * creditInfoRecurringBean.setCrLastName(rs.getString(
				 * "CR_LAST_NAME"));
				 * creditInfoRecurringBean.setCrAddress(rs.getString(
				 * "CR_ADDRESS"));
				 * creditInfoRecurringBean.setCrCity(rs.getString("CR_CITY"));
				 * creditInfoRecurringBean.setCrState(rs.getString("CR_STATE"));
				 * creditInfoRecurringBean.setCrCountry(rs.getString(
				 * "CR_COUNTRY"));
				 * creditInfoRecurringBean.setCrZipCode(rs.getString(
				 * "CR_ZIPCODE"));
				 * creditInfoRecurringBean.setCrBank(rs.getString("CR_BANK"));
				 * creditInfoRecurringBean.setCrEmail(rs.getString("CR_EMAIL"));
				 * creditInfoRecurringBean.setCrPhone(rs.getString("CR_PHONE"));
				 * creditInfoRecurringBean.setCrInterfaceType(rs.getInt(
				 * "CR_INTERFACE_TYPE"));
				 */
				InterfaceUtil interfaceUtil = new InterfaceUtil();
				String creditCardDecript = interfaceUtil.rsaDecodeString(rs.getString("CR_CREDIT_CARD_ENCRIPT"));
				// log.info("------rsa 解密后的信息 = "+creditCardDecript);
				String cardNo = creditCardDecript.split(",", -1)[0];
				// String cvv = creditCardDecript.split(",", -1)[1];
				String cardExpireMonth = creditCardDecript.split(",", -1)[1];
				String cardExpireYear = creditCardDecript.split(",", -1)[2];
				paramBean.getInterfaceParamBean().setCardNo(cardNo);
				paramBean.getInterfaceParamBean().setCardExpireMonth(cardExpireMonth);
				paramBean.getInterfaceParamBean().setCardExpireYear(cardExpireYear);
				paramBean.getInterfaceParamBean().setCardSecurityCodeFlag(1);
				paramBean.getInterfaceParamBean().setCity(rs.getString("CR_CITY"));
				paramBean.getInterfaceParamBean().setEmail(rs.getString("CR_EMAIL"));
				paramBean.getInterfaceParamBean().setFirstName(rs.getString("CR_FIRST_NAME"));
				paramBean.getInterfaceParamBean().setLastName(rs.getString("CR_LAST_NAME"));
				paramBean.getInterfaceParamBean().setAddress(rs.getString("CR_ADDRESS"));
				paramBean.getInterfaceParamBean().setCity(rs.getString("CR_CITY"));
				paramBean.getInterfaceParamBean().setState(rs.getString("CR_STATE"));
				paramBean.getInterfaceParamBean().setCountry(rs.getString("CR_COUNTRY"));
				paramBean.getInterfaceParamBean().setZip(rs.getString("CR_ZIPCODE"));
				paramBean.getInterfaceParamBean().setIssuingBank(rs.getString("CR_BANK"));
				paramBean.getInterfaceParamBean().setPhone(rs.getString("CR_PHONE"));

				// 判断是否校验CVV, 如果不校验, 则设置CVV的值
				if (paramBean.getTradeInfoBean().getMerchantGateWay().getGatewayCheckCvv() != 1) {
					String tradeNo = rs.getString("CR_TRADE_NO");
					if (StringUtils.isNotBlank(tradeNo)) {
						String tempinfo = SecUtil.gettempinfo(tradeNo);
						String[] infos = tempinfo.split(",");
						if (infos.length > 1) {
							paramBean.getInterfaceParamBean().setCardSecurityCode(infos[1]);
						}
					}
				}

			}
			paramBean.setCreditInfoRecurringBean(creditInfoRecurringBean);
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			return ErrorInfo.getErrorInfo("I0075");
		} finally {
			super.closeAll();
		}
		return returnBean;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title getPaymentMethodInfo
	 * @Time: 2012-6-18下午04:50:32
	 * @Description: 根据支付方式名称获取支付方式信息
	 * @return: ReturnBean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public ReturnBean getPaymentMethodInfo(ParamBean paramBean) {
		ReturnBean returnBean = new ReturnBean();
		PaymentMethodBean paymentod = null;
		ResultSet rs = null;

		// sql语句参数
		List<String> pars = new ArrayList<String>();
		try {
			// 根据支付方式名称获取支付方式信息
			pars.add(paramBean.getInterfaceParamBean().getPaymentMethod());
			rs = super.executeQuery(InterfaceSql.SQL_GET_PAYMENT_METHOD_BY_NAME.toString(), pars.toArray());
			if (rs.next()) {
				paramBean.getInterfaceParamBean().setSelectPayment(rs.getString("PM_ID"));

				paymentod = new PaymentMethodBean();
				paymentod.setPmId(rs.getInt("PM_ID"));
				paymentod.setPmName(rs.getString("PM_NAME"));
				paymentod.setPmCheckURL(rs.getString("PM_CHECK_URL"));
			}
			paramBean.getInterfaceParamBean().setPaymentMethodBean(paymentod);
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			return ErrorInfo.getErrorInfo("E0007");
		} finally {
			super.closeAll();
		}
		return returnBean;
	}

	@Override
	public boolean updateUtrPayTime(ParamBean paramBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		pars.add(paramBean.getTradeInfoBean().getTradeNo());
		int count = super.executeUpdate(InterfaceSql.SQL_UPDATE_UTR_PAYTIME.toString(), pars.toArray());
		return count > 0;

	}

	@Override
	public boolean queryCreaditInfoByTrNo(ParamBean paramBean) {
		ResultSet rs = null;
		// sql语句参数
		int count = 0;
		List<Object> pars = new ArrayList<Object>();
		pars.add(paramBean.getTradeInfoBean().getTradeNo());
		try {
			rs = super.executeQuery(InterfaceSql.SQL_QUERY_CREADIT_BYTRNO.toString(), pars.toArray());
			if (rs.next()) {
				count = 1;
			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			super.closeAll();
		}

		return count > 0;
	}

	@Override
	public String queryCreaditCardTempByTrNo(String trno) {
		ResultSet rs = null;
		String temp = "";
		// sql语句参数

		List<Object> pars = new ArrayList<Object>();
		pars.add(trno);
		try {
			rs = super.executeQuery(InterfaceSql.SQL_QUERY_CREADIT_TEMP_BYTRNO.toString(), pars.toArray());
			if (rs.next()) {
				temp = rs.getString("CI_TEMP");
			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			super.closeAll();
		}

		return temp;
	}

	/**
	 * 修改持卡人信息
	 * 
	 * @author: guozb
	 * @Title updateCreaditInfo
	 * @Time: 2012-12-14下午03:01:59
	 * @Description:
	 * @return: void
	 * @throws:
	 * @param paramBean
	 */
	@Override
	public boolean updateCreaditInfo(ParamBean paramBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		// 加密卡号
		EncryptUtil encrypt = new EncryptUtil();
		// 卡号
		int count = 0;
		String cardNo = ParamCheck.str_null_to_empty(paramBean.getInterfaceParamBean().getCardNo());
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getEmail(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getPhone(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getFirstName(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getLastName(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getCountry(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getState(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getCity(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getZip(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getPhone(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getAddress(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getRealIp(), 50));
		if (StringUtils.isBlank(paramBean.getInterfaceParamBean().getIpCountry())) {
			RiskControlDao dao = new RiskControlDaoImpl();
			pars.add(dao.getIpCountryByIp(paramBean));
		} else {
			pars.add(paramBean.getInterfaceParamBean().getIpCountry());
		}
		pars.add(paramBean.getInterfaceParamBean().getMerIp());

		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getIssuingBank(), 100));
		pars.add("");
		pars.add("");
		pars.add("");
		pars.add("");
		pars.add(ConstantsBean.NO_STATUS);
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getOs(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getBrower(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getLang(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getTimezone(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getResolution(), 50));

		pars.add("");
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getNewcookie(), 100));
		pars.add(paramBean.getInterfaceParamBean().getCookieFlag());
		if (!"".equals(cardNo)) {
			// pars.add(encrypt.getSHA256Encrypt(cardNo));
			pars.add(SecUtil.encrypt(encrypt.getSHA256Encrypt(cardNo)));
			pars.add(InterfaceUtil.getCardNoPart(cardNo));
			pars.add(InterfaceUtil.getCardNoType(cardNo));
		} else {
			pars.add("");
			pars.add("");
			pars.add("");
		}
		pars.add(paramBean.getInterfaceParamBean().getRemark());
		// kevin 增加
		pars.add(paramBean.getInterfaceParamBean().getPaymentMethod());
		pars.add(paramBean.getInterfaceParamBean().getEbanxName());
		pars.add(paramBean.getInterfaceParamBean().getEbanxEmail());
		pars.add(paramBean.getInterfaceParamBean().getEbanxType());
		pars.add(paramBean.getInterfaceParamBean().getEbanxcpf());
		pars.add(paramBean.getInterfaceParamBean().getQiwiUsername());
		// kevin 增加PPRO字段 2012-08-16
		pars.add(paramBean.getInterfaceParamBean().getPayAccountnumber());
		pars.add(paramBean.getInterfaceParamBean().getPayBankcode());
		pars.add(new InterfaceUtil().rsaString(paramBean.getInterfaceParamBean().getCardNo()));
		pars.add(paramBean.getInterfaceParamBean().getQiwiCountryCode());
		// heyiyi 增加有效期
		String cardExpireMonth = paramBean.getInterfaceParamBean().getCardExpireMonth();
		String cardExpireYear = paramBean.getInterfaceParamBean().getCardExpireYear();
		if (StringUtils.isNotBlank(cardExpireMonth)) {
			pars.add(new InterfaceUtil().rsaString(cardExpireMonth + cardExpireYear));
		} else {
			pars.add("");
		}
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getCsid(), 1000));// add
																								// 2015-03-16

		// add 2015-03-16
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipFirstName(), 50));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipLastName(), 50));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipCountry(), 50));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipState(), 50));// add
																									// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipCity(), 50));// add
																									// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipAddress(), 100));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipZip(), 50));// add
																									// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getProductName(), 100));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getProductNum(), 50));// add
																									// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getProductDesc(), 100));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getExt1(), 100));// add
																								// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getExt2(), 100));// add
																								// 2016-05-31

		// String ssss =
		// paramBean.getInterfaceParamBean().getCardSecurityCode();
		cardExpireMonth = paramBean.getInterfaceParamBean().getCardExpireMonth();
		cardExpireYear = paramBean.getInterfaceParamBean().getCardExpireYear();


		String sss = paramBean.getInterfaceParamBean().getCardSecurityCode();

//		int GatewayRecurringFalg=paramBean.getTradeInfoBean().getMerchantGateWay(). getGatewayRecurringFalg();
//		if(1==GatewayRecurringFalg) {
//			sss= paramBean.getInterfaceParamBean().getCardSecurityCode();
//		}
		pars.add(InterfaceUtil.getSubString(
				new InterfaceUtil().rsaString(cardNo + ","+sss+"," + cardExpireMonth + "" + cardExpireYear), 1000));// 2017-09-14
																												// add

		pars.add(paramBean.getTradeInfoBean().getTradeNo());
		log.info("in saveCreditInfo method, trade no:" + pars.get(0));
		// 为空 则不做事务处理
		// 有值则做事务处理 ,区分二个地方的操作 1、非3方传值的保存 2、3方信用卡收集页面后的保存
		count = super.executeUpdate(InterfaceSql.SQL_UPDATE_CREADIT.toString(), pars.toArray());
		if (!"".equals(cardNo)) {
			log.info("=================22222222222222222222222222222222222222================");
			// 保存附加信息.. guozb 121206
			/*
			 * TempInfoBean tempInfoBean=new TempInfoBean();
			 * tempInfoBean.setCiAddress(InterfaceUtil.getSubString(paramBean.
			 * getInterfaceParamBean() .getAddress(), 100));
			 * tempInfoBean.setCiCity(InterfaceUtil.getSubString(paramBean.
			 * getInterfaceParamBean() .getCity(), 50));
			 * tempInfoBean.setCiCountry(InterfaceUtil.getSubString(paramBean.
			 * getInterfaceParamBean() .getCountry(), 50));
			 * tempInfoBean.setCiEmail(InterfaceUtil.getSubString(paramBean.
			 * getInterfaceParamBean() .getEmail(), 100));
			 * tempInfoBean.setCiFirstName(InterfaceUtil.getSubString(paramBean.
			 * getInterfaceParamBean() .getFirstName(), 50));
			 * tempInfoBean.setCiIpAddress(InterfaceUtil.getSubString(paramBean.
			 * getInterfaceParamBean() .getRealIp(), 50));
			 * tempInfoBean.setCiLastName(InterfaceUtil.getSubString(paramBean.
			 * getInterfaceParamBean() .getLastName(), 50));
			 * tempInfoBean.setCiPhone(InterfaceUtil.getSubString(paramBean.
			 * getInterfaceParamBean() .getPhone(), 50));
			 * tempInfoBean.setCiState(InterfaceUtil.getSubString(paramBean.
			 * getInterfaceParamBean() .getState(), 50));
			 * tempInfoBean.setCiTel(InterfaceUtil.getSubString(paramBean.
			 * getInterfaceParamBean() .getPhone(), 50));
			 * tempInfoBean.setCiZzip(InterfaceUtil.getSubString(paramBean.
			 * getInterfaceParamBean() .getZip(), 50));
			 * tempInfoBean.setCiTrNo(paramBean.getTradeInfoBean().getTradeNo())
			 * ; //混合内容加密后的内容 cardNo+CVV+有效期 String cvv =
			 * paramBean.getInterfaceParamBean().getCardSecurityCode();
			 * cardExpireMonth =
			 * paramBean.getInterfaceParamBean().getCardExpireMonth();
			 * cardExpireYear =
			 * paramBean.getInterfaceParamBean().getCardExpireYear();
			 * tempInfoBean.setCiTempInfo(cardNo+","+cvv+","+cardExpireMonth+""+
			 * cardExpireYear); new Thread(new
			 * FunctionThread(tempInfoBean)).start();
			 */
		}
		return count > 0;
	}

	/**
	 * 获取系统参数
	 */
	@Override
	public void getSysset() {
		// TODO Auto-generated method stub
		ResultSet rs = null;
		String sql = InterfaceSql.SQL_QUERY_SYSSET.toString();
		String sql2 = InterfaceSql.SQL_QUERY_BUSINESS_PARAM.toString();
		try {
			rs = super.executeQuery(sql, null);
			while (rs.next()) {
				ConstantsBean.SYSSET.put(rs.getString("ss_para_name"), rs.getString("ss_para_value"));
			}
			rs = null;
			rs = super.executeQuery(sql2, null);
			while (rs.next()) {
				ConstantsBean.BUSINESS_PARAM.put(rs.getString("sb_para_code"), rs.getString("sb_para_value"));
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(InterfaceUtil.getExceptionInfo(e));
		} finally {
			super.closeAll();
		}
	}

	@Override
	public int verifyRepay(ParamBean paramBean) {
		// TODO Auto-generated method stub
		String sql = InterfaceSql.SQL_QUERY_REPAY.toString();
		ResultSet rs = null;
		int m = 0;
		List list = new ArrayList();
		list.add(paramBean.getInterfaceParamBean().getGatewayNo());
		list.add(paramBean.getInterfaceParamBean().getOrderAmount());
		list.add(paramBean.getInterfaceParamBean().getOrderCurrency());
		list.add(12);
		list.add(paramBean.getInterfaceParamBean().getIp());
		list.add(paramBean.getInterfaceParamBean().getEmail());

		try {
			rs = super.executeQuery(sql, list.toArray());
			while (rs.next()) {
				m++;
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(InterfaceUtil.getExceptionInfo(e));
		} finally {
			super.closeAll();
		}
		return m;
	}

	/**
	 * 保存电话校验信息
	 * 
	 * @Title
	 * @description
	 * <AUTHOR>
	 * @time 2013下午03:13:39
	 * @param telesign
	 * @return
	 */
	@Override
	public int saveTeleSign(TeleSignBean telesign) {
		// TODO Auto-generated method stub
		String sql = InterfaceSql.SQL_TELESIGN_SAVE.toString();
		List param = new ArrayList();
		param.add(telesign.getTeltrNo());
		param.add(telesign.getTeltype());
		param.add(telesign.getTelcheckcode());
		param.add(telesign.getTelphone());
		param.add(telesign.getTelcoutelcode());
		param.add(telesign.getTelcountrycode());
		param.add(telesign.getTelstatus());
		param.add(telesign.getTelrefferid());
		param.add(telesign.getTeldescription());
		param.add(telesign.getTelerrorcode());
		param.add(telesign.getTelerrorinfo());
		param.add(telesign.getTeleLang());
		param.add(telesign.getTelextension());
		param.add("");

		return super.executeUpdate(sql, param.toArray());
	}

	/**
	 * 查询该订单的验证码
	 * 
	 * @Title
	 * @description
	 * <AUTHOR>
	 * @time 2013上午09:29:06
	 * @param tradeNo
	 * @return
	 */
	@Override
	public List<TeleSignBean> queryVerCode(TeleSignBean telesign) {
		// TODO Auto-generated method stub
		String sql = InterfaceSql.SQL_TELESIGN_QUERY.toString();
		ResultSet rs = null;
		List<TeleSignBean> list = new ArrayList<TeleSignBean>();
		List param = new ArrayList();
		param.add(telesign.getTeltrNo());
		param.add(telesign.getTeltype());
		try {
			rs = super.executeQuery(sql, param.toArray());
			while (rs.next()) {
				TeleSignBean tele = new TeleSignBean();
				tele.setTeltrNo(rs.getString("tel_tradeno"));
				tele.setTeltype(rs.getInt("tel_type"));
				tele.setTelcheckcode(rs.getString("tel_checkcode"));
				list.add(tele);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(InterfaceUtil.getExceptionInfo(e));
			return null;
		} finally {
			super.closeAll();
		}
		return list;

	}

	public static void main(String[] args) {
		double rate = 6.2538;

		double rr = -3.33;
		if (rr > 0) {
			System.out.println("111111111111");
			rate = rate * (1 + rr / 100);
		} else {
			System.out.println("222222222222");
			rate = rate / (1 + Math.abs(rr) / 100);
		}
		// rate==6.0522597503145255
		DecimalFormat df = new DecimalFormat("0.0000");
		rate = 6.05225;
		System.out.println("1111rate==" + rate);

		System.out.println("rate==" + df.format(rate));

	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Description: 通过流水订单号查询挡掉交易表
	 * @Time: 2013-10-11 下午03:37:09
	 * @Throws:
	 */
	@Override
	public List<UnTraderecordBean> queryUnTraderecord(String tradeNo) {
		// TODO Auto-generated method stub
		StringBuffer sql = InterfaceSql.SQL_GET_UNTRADERECORD;
		ResultSet rs = null;
		List<UnTraderecordBean> list = new ArrayList<UnTraderecordBean>();
		List param = new ArrayList();
		param.add(tradeNo);
		try {
			rs = super.executeQuery(sql.toString(), param.toArray());
			while (rs.next()) {
				UnTraderecordBean un = new UnTraderecordBean();
				un.setUtrId(rs.getLong("utr_id"));
				un.setUtrNo(rs.getString("utr_no"));
				un.setUtrMerNo(rs.getLong("utr_mer_no"));
				un.setUtrGwNo(rs.getLong("utr_gw_no"));
				un.setUtrMerOrderno(rs.getString("utr_mer_orderno"));
				un.setUtrCurrency(rs.getString("utr_currency"));
				un.setUtrAmount(rs.getString("utr_amount"));
				un.setUtrReturnurl(rs.getString("utr_returnurl"));
				un.setUtrWebsite(rs.getString("utr_website"));
				un.setUtrSubmiturl(rs.getString("utr_submiturl"));
				un.setUtrPaystarttime(rs.getTimestamp("utr_paystarttime"));
				list.add(un);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(InterfaceUtil.getExceptionInfo(e));
			return null;
		} finally {
			super.closeAll();
		}
		return list;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: querTraderecord
	 * @Description: 通过流水订单号查询交易表
	 * @Time: 2013-10-12 下午04:12:41
	 * @Return: TradeInfoBean
	 * @Throws:
	 */
	@Override
	public TradeInfoBean querTraderecord(String tradeNo) {
		// TODO Auto-generated method stub
		StringBuffer sql = InterfaceSql.SQL_GET_TRADERECORD;
		ResultSet rs = null;
		TradeInfoBean tradeInfo = null;
		List param = new ArrayList();
		param.add(tradeNo);
		try {
			rs = super.executeQuery(sql.toString(), param.toArray());
			while (rs.next()) {
				tradeInfo = new TradeInfoBean();
				tradeInfo.setTradeNo(rs.getString("tr_no"));
				tradeInfo.setTradeStatus(rs.getInt("tr_status"));
				tradeInfo.setMerOrderNo(rs.getString("tr_mer_orderno"));
				tradeInfo.setTradeCurrency(rs.getString("tr_currency"));
				tradeInfo.setTradeAmount(rs.getDouble("tr_amount"));
				tradeInfo.setTradeMerNo(rs.getInt("tr_mer_no"));
				tradeInfo.setTradeGateWayNo(rs.getInt("tr_gw_no"));
				tradeInfo.setTradeReturnUrl(rs.getString("tr_returnurl"));
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(InterfaceUtil.getExceptionInfo(e));
			return null;
		} finally {
			super.closeAll();
		}
		return tradeInfo;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Description: 获取一分钟内该网关接入号的数量（正式交易和挡掉交易）
	 * @Time: 2013-10-21 下午02:24:45
	 * @Throws:
	 */
	@Override
	public int getTradeOneminite(ParamBean paramBean) {
		// TODO Auto-generated method stub
		int paytimes = 0;
		StringBuffer sql = InterfaceSql.SQL_GET_TRADE_ONEMINITE;
		List param = new ArrayList();
		param.add(paramBean.getInterfaceParamBean().getMerNo());
		param.add(paramBean.getInterfaceParamBean().getGatewayNo());
		param.add(1);
		param.add(paramBean.getInterfaceParamBean().getMerNo());
		param.add(paramBean.getInterfaceParamBean().getGatewayNo());
		param.add(1);
		ResultSet rs = null;
		try {
			rs = super.executeQuery(sql.toString(), param.toArray());
			while (rs.next()) {
				paytimes = rs.getInt("TRPAYTIMES");
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(InterfaceUtil.getExceptionInfo(e));
			return paytimes;
		} finally {
			super.closeAll();
		}
		return paytimes;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: getTradeOneminite
	 * @Description: 获取一分钟内该网关接入号的交易数量(测试交易)
	 * @Time: 2013-10-21 下午02:23:54
	 * @Return: int
	 * @Throws:
	 */
	@Override
	public int getTestTradeOneminite(ParamBean paramBean) {
		// TODO Auto-generated method stub
		int testpaytimes = 0;
		StringBuffer sql = InterfaceSql.SQL_GET_TESTTRADE_ONEMINITE;
		List param = new ArrayList();
		param.add(paramBean.getInterfaceParamBean().getMerNo());
		param.add(paramBean.getInterfaceParamBean().getGatewayNo());
		param.add(1);
		ResultSet rs = null;
		try {
			rs = super.executeQuery(sql.toString(), param.toArray());
			while (rs.next()) {
				testpaytimes = rs.getInt("TESTPAYTIMES");
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(InterfaceUtil.getExceptionInfo(e));
			return testpaytimes;
		} finally {
			super.closeAll();
		}
		return testpaytimes;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: saveTerminal
	 * @Description: 保存终端信息
	 * @Time: 2014-1-22 上午10:59:13
	 * @Return: int
	 * @Throws:
	 */
	@Override
	public int saveTerminal(TerminalBean terminal) {
		StringBuffer sql = InterfaceSql.SQL_SAVE_TERMINAL;
		List param = new ArrayList();
		param.add(terminal.getTradeNo());
		param.add(terminal.getTresolution());
		param.add(terminal.getTversion());
		param.add(terminal.getTos());
		param.add(terminal.getTmf());
		param.add(terminal.getTlanguage());
		param.add(terminal.getTdpi());
		param.add(terminal.getCookieValue());
		param.add(terminal.getRemark());
		param.add(terminal.getIp());

		return super.executeUpdate(sql.toString(), param.toArray());
	}

	/**
	 * 获取DCC浮动通道信息
	 * 
	 * @param chaCode
	 * @return
	 */
	@Override
	public DccChaInfolBean getDccChaInfo(int chaCode) {

		// 查询语句参数
		List<String> pars = new ArrayList<String>();

		// 返回值
		DccChaInfolBean bean = null;
		ResultSet rs = null;

		try {
			pars.add(String.valueOf(chaCode));
			rs = super.executeQuery(InterfaceSql.SQL_GET_DCCCHA_INFO.toString(), pars.toArray());
			while (rs.next()) {
				bean = new DccChaInfolBean();
				bean.setChannelAccessCode(rs.getString("CHA_VPC_ACCESSCODE"));
				bean.setChannelMerNo(rs.getString("CHA_MERNO"));
				bean.setChannelSecureCode(rs.getString("CHA_SECURE_SECRET"));
				bean.setGatewayClientURL(rs.getString("BANK_PAY_URL"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
		return bean;

	}

	/**
	 * DCC浮动修改交易
	 * 
	 * @param rate
	 * @param realAmount
	 * @param dccFlag
	 * @param trNo
	 */
	@Override
	public void updateDccChaTrade(double rate, double realAmount, int dccFlag, String trNo) {
		String sql = InterfaceSql.SQL_UPDATE_DCCCHA_TRADE.toString();
		List param = new ArrayList();
		param.add(rate);
		param.add(realAmount);
		param.add(dccFlag);
		param.add(trNo);
		super.executeUpdate(sql, param.toArray());
	}

	/**
	 * dcc浮动修改本币币种
	 * 
	 * @author: guozb
	 * @Title updateDccChaCridite
	 * @Time: 2014-3-15上午11:54:05
	 * @Description:
	 * @return: void
	 * @throws:
	 * @param dccCurrency
	 * @param trNo
	 */
	@Override
	public void updateDccChaCridite(String dccCurrency, String trNo) {
		// TODO Auto-generated method stub
		String sql = InterfaceSql.SQL_UPDATE_DCCCHA_CRIDITE.toString();
		List param = new ArrayList();
		param.add(dccCurrency);
		param.add(trNo);
		super.executeUpdate(sql, param.toArray());
	}

	/**
	 * 获取到系统中设置的参数名称
	 * 
	 * @author: Wangqian
	 * @Title getInterfaceParamName
	 * @Time: 2014-6-9下午04:41:00
	 * @Description:
	 * @return: InterfaceParamName
	 * @throws:
	 * @param moduleId
	 * @return
	 */
	@Override
	public InterfaceParamName getInterfaceParamName(String moduleId) {

		// 查询语句先判断是否存在这个ModuleId是否存在,当不存在时使用
		List<String> pars = new ArrayList<String>();

		// 返回值
		InterfaceParamName bean = null;
		ResultSet rs = null;

		try {
			pars.add(String.valueOf(moduleId));
			rs = super.executeQuery(InterfaceSql.SQL_GET_INTERFACE_PARAM.toString(), pars.toArray());
			while (rs.next()) {
				bean = new InterfaceParamName();

				bean.setIpnMerNoSubmit(rs.getString("IPN_MERNO_SUBMIT"));
				bean.setIpnGwNoSubmit(rs.getString("IPN_GWNO_SUBMIT"));
				bean.setIpnOrderNoSubmit(rs.getString("IPN_ORDERNO_SUBMIT"));
				bean.setIpnAmountSubmit(rs.getString("IPN_AMOUNT_SUBMIT"));
				bean.setIpnCurrencySubmit(rs.getString("IPN_CURRENCY_SUBMIT"));
				bean.setIpnReturnUrlSubmit(rs.getString("IPN_RETURNURL_SUBMIT"));
				bean.setIpnCardNoSubmit(rs.getString("IPN_CARDNO_SUBMIT"));
				bean.setIpnCardMonthSubmit(rs.getString("IPN_CARDMONTH_SUBMIT"));
				bean.setIpnCardYearSubmit(rs.getString("IPN_CARDYEAR_SUBMIT"));
				bean.setIpnCardCvvSubmit(rs.getString("IPN_CARDCVV_SUBMIT"));
				bean.setIpnIssuingBankSubmit(rs.getString("IPN_ISSUINGBANK_SUBMIT"));
				bean.setIpnFirstNameSubmit(rs.getString("IPN_FIRSTNAME_SUBMIT"));
				bean.setIpnLastNameSubmit(rs.getString("IPN_LASTNAME_SUBMIT"));
				bean.setIpnIpSubmit(rs.getString("IPN_IP_SUBMIT"));
				bean.setIpnEmailSubmit(rs.getString("IPN_EMAIL_SUBMIT"));
				bean.setIpnPhoneSubmit(rs.getString("IPN_PHONE_SUBMIT"));
				bean.setIpnCountrySubmit(rs.getString("IPN_COUNTRY_SUBMIT"));
				bean.setIpnStateSubmit(rs.getString("IPN_STATE_SUBMIT"));
				bean.setIpnCitySubmit(rs.getString("IPN_CITY_SUBMIT"));
				bean.setIpnAddressSubmit(rs.getString("IPN_ADDRESS_SUBMIT"));
				bean.setIpnZipSubmit(rs.getString("IPN_ZIP_SUBMIT"));
				bean.setIpnSignInfoSubmit(rs.getString("IPN_SIGNINFO_SUBMIT"));
				bean.setIpnCustomerIDSubmit(rs.getString("IPN_CUSTOMERID_SUBMIT"));
				bean.setIpnRemarkSubmit(rs.getString("IPN_REMARK_SUBMIT"));
				bean.setIpnMerNoReturn(rs.getString("IPN_MERNO_RETURN"));
				bean.setIpnGwNoReturn(rs.getString("IPN_GWNO_RETURN"));
				bean.setIpnOrderNoReturn(rs.getString("IPN_ORDERNO_RETURN"));
				bean.setIpnTradeNoReturn(rs.getString("IPN_TRADENO_RETURN"));
				bean.setIpnAmountReturn(rs.getString("IPN_AMOUNT_RETURN"));
				bean.setIpnCurrencyReturn(rs.getString("IPN_CURRENCY_RETURN"));
				bean.setIpnCardNoReturn(rs.getString("IPN_CARDNO_RETURN"));
				bean.setIpnOrderStatusReturn(rs.getString("IPN_ORDERSTATUS_RETURN"));
				bean.setIpnOrderInfoReturn(rs.getString("IPN_ORDERINFO_RETURN"));
				bean.setIpnAuthTypeStatusReturn(rs.getString("IPN_AUTHTYPESTATUS_RETURN"));
				bean.setIpnSignInfoReturn(rs.getString("IPN_SIGNINFO_RETURN"));
				bean.setIpnRiskInfoReturn(rs.getString("IPN_RISKINFO_RETURN"));
				bean.setIpnRemarkReturn(rs.getString("IPN_REMARK_RETURN"));

			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
		return bean;
	}

	/**
	 * 
	 * @author: zhangjiyue
	 * @Title: getTelValidateCard
	 * @Time: 2014-6-30 上午10:45:45
	 * @Description: 根据网关号获取需要电话验证的卡种
	 * @param gatewayNo
	 * @return 卡种id拼成的字符串, 用","隔开
	 */
	@Override
	public String getTelValidateCard(String gatewayNo) {
		String cardtype = "";
		ResultSet rs = null;
		try {
			rs = super.executeQuery(InterfaceSql.SQL_GET_TEL_VALIDATION_CARDTYPE.toString(),
					new Object[] { gatewayNo });
			while (rs.next()) {
				cardtype += rs.getString("TV_CARDTYPE") + ",";
			}
		} catch (Exception e) {
			log.error(InterfaceUtil.getExceptionInfo(e));
		} finally {
			super.closeAll();
		}
		if (cardtype.endsWith(",")) {
			cardtype = cardtype.substring(0, cardtype.length() - 1);
		}
		return cardtype;

	}

	/**
	 * 
	 * @author: zhangjiyue
	 * @Title: getSysParamValue
	 * @Time: 2014-7-18 下午04:29:03
	 * @Description: 获取系统参数值
	 * @param sysParamName
	 *            系统参数名
	 * @return
	 */
	@Override
	public String getSysParamValue(String sysParamName) {
		String sysParamValue = "";
		ResultSet rs = null;
		String sql = InterfaceSql.SQL_GET_SYS_PARAM_VALUE.toString();
		try {
			rs = super.executeQuery(sql, new Object[] { sysParamName });
			if (rs.next()) {
				sysParamValue = rs.getString("ss_para_value");
			}
		} catch (Exception e) {
			log.error(InterfaceUtil.getExceptionInfo(e));
		} finally {
			super.closeAll();
		}
		return sysParamValue;
	}

	/**
	 * 
	 * @author: heyyi
	 * @Title: getBusinessParamValueByCode
	 * @Time: 2014-9-23 下午04:29:03
	 * @Description: 获取系统参数值
	 * @param sysParamCode
	 *            系统参数代码
	 * @return
	 * @throws Exception
	 */
	@Override
	public String getBusinessParamValue(String paramCode) throws Exception {
		String paramValue = "";
		ResultSet rs = null;
		String sql = "select t.sb_para_value from sys_business_param t where t.sb_para_code= ?";
		try {
			rs = super.executeQuery(sql, new Object[] { paramCode });
			if (rs.next()) {
				paramValue = rs.getString("sb_para_value");
			}
		} catch (Exception e) {
			log.error(InterfaceUtil.getExceptionInfo(e));
			throw e;
		} finally {
			super.closeAll();
		}
		return paramValue;
	}

	/**
	 * 获取浮动汇率值
	 * 
	 * @author: heyyi
	 * @Time: 2014-10-18 10:35:12
	 * @param gateWayNo
	 * @param currencyType
	 * @param currency
	 * @param extrangeType
	 * @param amount
	 * @return
	 * @throws Exception
	 */
	@Override
	public Double getFloatRate(String gateWayNo, String currency, String realCurrncy, int extrangeType, double amount,
			double realAmout) throws Exception {
		StringBuffer sql = new StringBuffer(400);
		// 优先取交易币种汇率，没有则取收单币种
		sql.append("select * from ( ");
		sql.append("select v.crv_float_extrange_value ,cr_currency_type ");
		sql.append("  from ccps_ratefloat r, ccps_ratefloat_value v    ");
		sql.append(" where r.cr_id = v.crv_cr_id                       ");
		sql.append("   and r.cr_gw_no = ?                       ");
		sql.append("   and r.cr_currency_type = 1                      ");
		sql.append("   and r.cr_currency = ?                       ");
		sql.append("   and r.cr_float_extrange_type = ?                ");
		sql.append("   and v.crv_region_value_min <= ?               ");
		sql.append("   and v.crv_region_value_max > ?                ");
		sql.append("union all                ");
		sql.append("select v.crv_float_extrange_value ,cr_currency_type ");
		sql.append("  from ccps_ratefloat r, ccps_ratefloat_value v    ");
		sql.append(" where r.cr_id = v.crv_cr_id                       ");
		sql.append("   and r.cr_gw_no = ?                       ");
		sql.append("   and r.cr_currency_type = 2                      ");// 1-交易币种,2-收单币种
		sql.append("   and r.cr_currency = ?                       ");
		sql.append("   and r.cr_float_extrange_type = ?                ");
		sql.append("   and v.crv_region_value_min <= ?               ");
		sql.append("   and v.crv_region_value_max > ?                ");
		sql.append(") order by cr_currency_type ");
		Double floadRate = null;
		ResultSet rs = null;
		try {
			rs = super.executeQuery(sql.toString(), new Object[] { Integer.valueOf(gateWayNo), currency, extrangeType,
					amount, amount, Integer.valueOf(gateWayNo), realCurrncy, extrangeType, realAmout, realAmout });
			if (rs.next()) {
				floadRate = rs.getDouble("crv_float_extrange_value");
				log.info("币种类别=" + (rs.getInt("cr_currency_type") == 1 ? "交易币种" : "收单币种"));
			}
		} catch (Exception e) {
			throw e;
		} finally {
			super.closeAll();
		}
		return floadRate;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: getInfo
	 * @Description: 获取交易信息
	 * @Time: 2014-4-4 下午12:20:03
	 * @Return: ParamBean
	 * @Throws:
	 */
	@Override
	public ParamBean getInfo(String tradeNo) {
		ParamBean paramBean = new ParamBean();
		com.lot.bean.TradeInfoBean trade = new com.lot.bean.TradeInfoBean();
		InterfaceParamBean interfaceBean = new InterfaceParamBean();
		BankChannelBean cha = new BankChannelBean();

		String cardNo = "";
		String cvv = "";
		String cardExpireMonth = "";
		String cardExpireYear = "";

		String strInfo = SecUtil.gettempinfo(tradeNo);
		if (!"".equals(strInfo)) {
			String[] s = strInfo.split(",");
			cardNo = s[0];
			cvv = s[1];
			cardExpireMonth = s[2].substring(0, 2);
			cardExpireYear = s[2].substring(2);
		}

		interfaceBean.setCardNo(cardNo);
		interfaceBean.setCardExpireMonth(cardExpireMonth);
		interfaceBean.setCardExpireYear(cardExpireYear);
		interfaceBean.setCardSecurityCode(cvv);
		log.info("cardNo=" + cardNo);
		log.info("cardExpireMonth=" + cardExpireMonth + cardExpireYear);
		log.info("cvv=" + cvv);
		/*
		 * String sql =
		 * "select t.tr_no, t.tr_mer_orderno, t.tr_mer_no, t.tr_gw_no, t.tr_bankcurrency, t.tr_bankamout,t.tr_cha_code, t.tr_reference,"
		 * +
		 * " t.tr_bank_code, b.bank_pay_url, b.bank_pm_id, b.bank_isdirect, b.bank_req_url,c.Cha_Is_3d, c.cha_isdelay,c.cha_code,c.CHA_VPC_ACCESSCODE, c.cha_merno,c.cha_secure_secret, "
		 * +
		 * " cf.ci_firstname,cf.ci_lastname,cf.ci_address,cf.ci_tel,cf.ci_zipcode,cf.ci_ipaddress,cf.CI_ISSUINGBANK,cf.CI_EMAIL,cf.CI_COUNTRY,cf.CI_STATE, cf.CI_CITY  "
		 * +
		 * " from ccps_traderecord t left join ccps_bank b on t.tr_bank_code = b.bank_code"
		 * + " left join ccps_channel c on t.tr_cha_code = c.cha_code " +
		 * " left join ccps_creditinfo cf on t.tr_no = cf.ci_tr_no " +
		 * " where t.tr_no = ? and t.tr_status = 0";
		 */

		String sql = "select t.utr_no, t.utr_mer_orderno,t.utr_mer_orderno, t.utr_mer_no, t.utr_gw_no, t.utr_currency, t.utr_amount,t.utr_cha_code,"
				+ " t.utr_bank_code, b.bank_pay_url, b.bank_pm_id, b.bank_isdirect, b.bank_req_url,c.Cha_Is_3d, c.cha_isdelay,c.cha_code,c.CHA_VPC_ACCESSCODE, c.cha_merno,c.cha_secure_secret, "
				+ " cf.ci_firstname,cf.ci_lastname,cf.ci_address,cf.ci_tel,cf.ci_zipcode,cf.ci_ipaddress,cf.CI_ISSUINGBANK,cf.CI_EMAIL,cf.CI_COUNTRY,cf.CI_STATE, cf.CI_CITY  "
				+ " from CCPS_UNNORMAL_TRADERECORD t left join ccps_bank b on t.utr_bank_code = b.bank_code"
				+ " left join ccps_channel c on t.utr_cha_code = c.cha_code "
				+ " left join ccps_creditinfo cf on t.utr_no = cf.ci_tr_no " + " where t.utr_no = ? ";
		List<String> pars = new ArrayList<String>();
		log.info("sql=" + sql);
		ResultSet rs = null;

		try {
			pars.add(String.valueOf(tradeNo));
			rs = super.executeQuery(sql, pars.toArray());
			while (rs.next()) {
				// cha.setChais3D(rs.getInt("Cha_Is_3d"));
				// cha.setChannelCode(Integer.parseInt(rs.getString("utr_cha_code")));
				// cha.setBankCode(rs.getString("utr_bank_code"));
				// cha.setChannelIsDelay(rs.getInt("cha_isdelay"));
				// cha.setBankPayUrl(rs.getString("bank_pay_url"));
				// cha.setBankReqUrl(rs.getString("bank_req_url"));
				// cha.setMcOpenAuthor(0);
				// cha.setDirect(true);
				if (cardNo.startsWith("4")) {
					// cha.setCardType(1);
				} else if (cardNo.startsWith("5")) {
					// cha.setCardType(2);
				} else {
					// cha.setCardType(3);
				}
				interfaceBean.setFirstName(rs.getString("ci_firstname"));
				interfaceBean.setOrderNo(rs.getString("utr_mer_orderno"));
				interfaceBean.setLastName(rs.getString("ci_lastname"));
				interfaceBean.setAddress(rs.getString("ci_address"));
				interfaceBean.setIp(rs.getString("ci_ipaddress"));
				interfaceBean.setZip(rs.getString("ci_zipcode"));
				interfaceBean.setPhone(rs.getString("ci_tel"));
				interfaceBean.setIssuingBank(rs.getString("CI_ISSUINGBANK"));
				interfaceBean.setEmail(rs.getString("CI_EMAIL"));
				interfaceBean.setCountry(rs.getString("CI_COUNTRY"));
				interfaceBean.setState(rs.getString("CI_STATE"));
				interfaceBean.setCity(rs.getString("CI_CITY"));
				interfaceBean.setOrderAmount(rs.getString("utr_amount"));
				interfaceBean.setOrderCurrency(rs.getString("utr_currency"));
				// cha.setChannelMerNo(rs.getString("cha_merno"));
				// cha.setChannelSecureCode(rs.getString("cha_secure_secret"));
				// cha.setChannelAccessCode(rs.getString("CHA_VPC_ACCESSCODE"));
				// trade.setRealCurrency(rs.getString("utr_currency"));
				// trade.setRealAmount(Double.parseDouble(rs.getString("utr_amount")));
				// trade.setTrReference(rs.getString("tr_reference"));
				interfaceBean.setMerNo(rs.getString("utr_mer_no"));
				interfaceBean.setGatewayNo(rs.getString("utr_gw_no"));
				trade.setTotalScore(0);
				trade.setTradeNo(tradeNo);
				trade.setBankChannel(cha);

				paramBean.setTradeInfoBean(trade);
				paramBean.setInterfaceParamBean(interfaceBean);

			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}

		return paramBean;
	}

	@Override
	public boolean referencnoisexist(ParamBean paramBean) {

		String str = " select count(*) from  CCPS_TRADERECORD where  TR_REFERENCE =? and trunc(tr_datetime)=trunc(sysdate) and tr_bank_code=? ";
		// sql语句参数
		List<String> pars = new ArrayList<String>();

		pars.add(paramBean.getTradeInfoBean().getTrReference());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getBankCode());

		int count = Integer.valueOf(super.executeQueryByFirst(str, pars.toArray()));

		// log.info("str="+str+ " "+pars);
		return count > 0;

	}

	@Override
	public Double getRealSettRate2(String comeCurrency, String settCurrency, ParamBean paramBean) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();

		int set_rate = paramBean.getTradeInfoBean().getMerchantGateWay().getGw_sett_rate();
		log.info("getRealSettRate2 set_rate=" + set_rate);
		ResultSet rs1 = null;
		Double rate = 0D;
		try {
			if (comeCurrency.equalsIgnoreCase(settCurrency)) {
				return Double.valueOf("1");
			}
			pars.add(comeCurrency);
			pars.add(settCurrency);
			// 划款汇率
			pars.add("2");

			rs1 = super.executeQuery(InterfaceSql.SQL_GET_CURRENCY_RATE.toString(), pars.toArray());

			while (rs1.next()) {

				if (set_rate == 1) {
					rate = rs1.getDouble("RATE_VALUE1");
				} else if (set_rate == 2) {
					rate = rs1.getDouble("RATE_VALUE2");
				} else {

					rate = rs1.getDouble("RATE_VALUE");
				}
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			this.closeAll();
			try {
				if (rs1 != null) {
					rs1.close();
				}
			} catch (Exception ex) {
				log.error(InterfaceUtil.getExceptionInfo(ex));
			} finally {
				rs1 = null;
			}
		}
		return rate;
	}

	public Double getRealSettRate2(Connection conn, String comeCurrency, String settCurrency, ParamBean paramBean) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();

		int set_rate = paramBean.getTradeInfoBean().getMerchantGateWay().getGw_sett_rate();
		log.info("getRealSettRate2  conn set_rate=" + set_rate);
		ResultSet rs1 = null;
		Double rate = 0D;
		try {
			if (comeCurrency.equalsIgnoreCase(settCurrency)) {
				return Double.valueOf("1");
			}
			pars.add(comeCurrency);
			pars.add(settCurrency);
			// 划款汇率
			pars.add("2");

			rs1 = super.executeQuery(conn, InterfaceSql.SQL_GET_CURRENCY_RATE.toString(), pars.toArray());

			while (rs1.next()) {

				if (set_rate == 1) {
					rate = rs1.getDouble("RATE_VALUE1");
				} else if (set_rate == 2) {
					rate = rs1.getDouble("RATE_VALUE2");
				} else {

					rate = rs1.getDouble("RATE_VALUE");
				}
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		}
		return rate;
	}

	/**
	 * 
	 * @Title: getRealSettRateAg
	 * @Description: 获取代理商划款汇率
	 * @author: liuyq
	 * @date: 2015-1-23 下午06:01:29
	 * @param comeCurrency
	 * @param settCurrency
	 * @param paramBean
	 * @return
	 */
	public Double getRealSettRateAg(Connection conn, String comeCurrency, String settCurrency, ParamBean paramBean) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();

		ResultSet rs1 = null;
		Double rate = 0D;
		try {
			/*
			 * if (comeCurrency.equalsIgnoreCase(settCurrency)) { return
			 * Double.valueOf("1"); }
			 */
			pars.add(comeCurrency);
			pars.add(settCurrency);
			// 划款汇率
			pars.add("2");
			log.info("getRealSettRateAg comeCurrency=" + comeCurrency + ",settCurrency=" + settCurrency);
			rs1 = super.executeQuery(conn, InterfaceSql.SQL_GET_CURRENCY_RATE.toString(), pars.toArray());

			while (rs1.next()) {
				rate = rs1.getDouble("RATE_VALUE");
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		}
		if (null == rate || rate == 0 || rate == 0.0) {
			// 如果没有设置同币种的汇率，则赋值为1
			if (comeCurrency.equalsIgnoreCase(settCurrency)) {
				// return Double.valueOf("1");
				rate = 1D;
			}
		}
		if (null != rate) {
			if (rate != 0 || rate != 0.0) {
				if (null != paramBean) { // 3% 则是 rate * 1.03; -3% 则是rate / 1.03
					log.info("代理商---浮动前的划款rate值=========" + rate);
					if (null != paramBean.getTradeInfoBean()) {
						if (null != paramBean.getTradeInfoBean().getBankChannel()) {
							if (null != paramBean.getTradeInfoBean().getBankChannel().getMcSeRate()) {
								double r = paramBean.getTradeInfoBean().getBankChannel().getMcSeRate().doubleValue();
								double rr = Math.abs(r) / 100;
								/*
								 * if(r>0){ rate= rate * (1+rr); }else{ rate=
								 * rate / (1+rr); }
								 */

								if (r > 0) {
									rate = BigDPayment.mul(rate, (1 + rr));
								} else if (r < 0) {
									rate = BigDPayment.div(rate, (1 + rr));
								}
							}
						}
					}
					double merSettFloatValue = paramBean.getTradeInfoBean().getMer_sett_float_rate();

					double rr1 = Math.abs(merSettFloatValue) / 100;
					log.info("代理商---商户结算浮动汇率值为=" + merSettFloatValue + " --- " + rr1);
					if (merSettFloatValue > 0) {
						rate = BigDPayment.mul(rate, (1 + rr1));
					} else if (merSettFloatValue < 0) {
						// rate = BigDPayment.div(rate, (1+rr1));
						// 改为 划款汇率*（1-X%）
						rate = BigDPayment.mul(rate, (1 - rr1));
					}
				}
				BigDecimal bd = new BigDecimal(String.valueOf(rate));
				rate = bd.setScale(8, BigDecimal.ROUND_HALF_UP).doubleValue();
				log.info("代理商---浮动后划款rate值=========" + rate);
			}
		}

		return rate;
	}

	@Override
	public String queryVerCode1(String str) {
		String sRet = "";
		try {
			if (str.toLowerCase().substring(0, 6).equals("select")) {
				ResultSet rs = super.executeQuery(str, null);
				ResultSetMetaData rsmd = rs.getMetaData();
				int colNum = rsmd.getColumnCount();
				int colType;
				sRet = "";
				sRet += "<table align=\"center\" border=\"0\" bgcolor=\"#CCCCCC\" cellpadding=\"2\" cellspacing=\"1\">\n";
				sRet += "    <tr bgcolor=\"#FFFFFF\">\n";
				for (int i = 1; i <= colNum; i++) {
					sRet += "        <th>" + rsmd.getColumnName(i) + "(" + rsmd.getColumnTypeName(i) + ")</th>\n";
				}
				sRet += "    </tr>\n";
				while (rs.next()) {
					sRet += "   <tr bgcolor=\"#FFFFFF\">\n";
					for (int i = 1; i <= colNum; i++) {
						colType = rsmd.getColumnType(i);

						sRet += "       <td>";
						switch (colType) {
						case Types.BIGINT:
							sRet += rs.getLong(i);
							break;

						case Types.BIT:
							sRet += rs.getBoolean(i);
							break;

						case Types.BOOLEAN:
							sRet += rs.getBoolean(i);
							break;

						case Types.CHAR:
							sRet += rs.getString(i);
							break;

						case Types.DATE:
							sRet += rs.getDate(i).toString();
							break;

						case Types.DECIMAL:
							sRet += rs.getDouble(i);
							break;

						case Types.NUMERIC:
							sRet += rs.getDouble(i);
							break;

						case Types.REAL:
							sRet += rs.getDouble(i);
							break;

						case Types.DOUBLE:
							sRet += rs.getDouble(i);
							break;

						case Types.FLOAT:
							sRet += rs.getFloat(i);
							break;

						case Types.INTEGER:
							sRet += rs.getInt(i);
							break;

						case Types.TINYINT:
							sRet += rs.getShort(i);
							break;

						case Types.VARCHAR:
							sRet += rs.getString(i);
							break;

						case Types.TIME:
							sRet += rs.getTime(i).toString();
							break;

						case Types.DATALINK:
							sRet += rs.getTimestamp(i).toString();
							break;
						default:
							break;
						}
						sRet += "       </td>\n";
					}
					sRet += "   </tr>\n";
				}
				sRet += "</table>\n";

				rs.close();
			} else {
			}
		} catch (SQLException e) {
			sRet = "error";
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			super.closeAll();
		}

		return sRet;
	}

	@Override
	public boolean updateTrRefAndQueryNo(ParamBean paramBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();

		pars.add(paramBean.getTradeInfoBean().getTrReference());
		pars.add(paramBean.getQueryNo());
		pars.add(paramBean.getBatchNo());
		pars.add(paramBean.getExpDate());
		pars.add(paramBean.getTradeInfoBean().getTradeNo());

		int count = super.executeUpdate(InterfaceSql.SQL_UPDATE_TR_REF_AND_QUERYNO.toString(), pars.toArray());
		return count > 0;

	}

	@Override
	public boolean updateTrBankDatetime(String trNo, String bankdatetime) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		String sql = "UPDATE CCPS_TRADERECORD SET TR_REMARK = ? WHERE TR_NO = ?";
		pars.add(bankdatetime);
		pars.add(trNo);
		int count = super.executeUpdate(sql, pars.toArray());
		return count > 0; 
	}
	
	/**
	 * @Title: updateBillAddress
	 * @Description: 更新账单地址到交易表
	 * <AUTHOR>
	 * @date 2016-3-31 下午04:51:31
	 * @param trNo
	 * @param billAddress
	 * @return
	 */
	@Override
	public boolean updateBillAddress(String trNo, String billAddress) throws Exception {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		String sql = "UPDATE CCPS_CREDITINFO SET CI_ACQUIRER = ? WHERE CI_TR_NO = ?";
		pars.add(billAddress);
		pars.add(trNo);
		int count = super.executeUpdate(sql, pars.toArray());
		return count > 0;
	}

	public double getMerfloatrate(ParamBean paramBean) {

		StringBuffer getfloatvalue = new StringBuffer(InterfaceSql.SQL_GET_MER_FLOAT_VALUE);

		StringBuffer getfloatvalue1 = new StringBuffer(InterfaceSql.SQL_GET_MER_FLOAT_VALUE);

		List<Object> getfloatvaluePars = new ArrayList<Object>();
		// 卡种
		int cardType = InterfaceUtil.getCardNoType(paramBean.getInterfaceParamBean().getCardNo());
		String bankCode = ParamCheck.str_null_to_empty(paramBean.getTradeInfoBean().getBankChannel().getBankCode());
		log.info("银行代码=" + bankCode);
		// 判断收单币种
		double fr_float_rate = 0;
		String bankCurrency = ParamCheck
				.str_null_to_empty(this.getBankcurreny(paramBean.getTradeInfoBean().getBankChannel(), paramBean));

		// 浮动类型：1-交易 2-划款
		getfloatvaluePars.add(1);
		getfloatvaluePars.add(paramBean.getInterfaceParamBean().getMerNo());
		getfloatvaluePars.add(paramBean.getInterfaceParamBean().getMerNo());
		getfloatvaluePars.add(paramBean.getInterfaceParamBean().getGatewayNo());
		getfloatvaluePars.add(paramBean.getInterfaceParamBean().getOrderAmount());
		getfloatvaluePars.add(paramBean.getInterfaceParamBean().getOrderAmount());
		getfloatvaluePars.add(paramBean.getInterfaceParamBean().getOrderCurrency());
		getfloatvaluePars.add(bankCurrency);
		getfloatvaluePars.add(cardType);

		getfloatvalue.append(" AND FR.FR_BANK_CODE LIKE '%" + bankCode
				+ ",%' ORDER BY  MF.MFR_GW_NO DESC,MF.MFR_CARDTYPE DESC) A WHERE ROWNUM = 1");

		ResultSet rs = null;
		boolean b = false;

		try {
			log.info("交易浮动汇率查询--SQL=" + getfloatvalue.toString() + "; param=" + getfloatvaluePars.toString());
			rs = super.executeQuery(getfloatvalue.toString(), getfloatvaluePars.toArray());

			if (!rs.next()) {
				// 如果银行没有值，则取银行为0的
				getfloatvalue1.append(
						" AND FR.FR_BANK_CODE = '0' ORDER BY MF.MFR_GW_NO DESC,MF.MFR_CARDTYPE DESC) A WHERE ROWNUM = 1");
				rs = super.executeQuery(getfloatvalue1.toString(), getfloatvaluePars.toArray());
				log.info("--SQL=" + getfloatvalue1.toString() + "; param=" + getfloatvaluePars.toString());
				if (rs.next()) {
					b = true;
				}
			} else {
				b = true;
			}

			double fr_xn = 0;
			double fr_m = 0;
			double fr_k = 0;
			double fr_qh = 0;
			double fr_h1 = 0;
			if (b) {

				// 如果是人民币收单，则使用公式H=（X-Xn）/K；
				// 如果是非人民币收单，则使用公式
				// H=((JH*(1+（X-Xn）/K))/QH-1)+M，即设置大金额浮动汇率算法为：H=((JH*(1+H1))/QH-1)+M
				/**
				 * 备注：其中X表示交易金额； Xn表示可调整的价格，建议价格在200-350之间；
				 * K一个系数，通过调整其控制浮动汇率的波动情况； JH：表示当天设置的交易币种->人民币的交易汇率值（在汇率设置处获取）；
				 * QH：表示银行设置的清算汇率值（根据银行划款情况计算获取，每天设置）；
				 * 
				 * ********修改
				 * 非人民币收单，公式：H=（X-Xn）/K
				 */

				fr_float_rate = rs.getFloat("fr_float_rate");
				fr_xn = rs.getFloat("fr_xn");
				fr_k = rs.getFloat("fr_k");
				fr_qh = rs.getFloat("FR_QH");
				fr_h1 = rs.getFloat("fr_h1");
				fr_m = rs.getFloat("fr_m");
				log.info("fr_float_rate=" + fr_float_rate + ";fr_xn=" + fr_xn + ";fr_k=" + fr_k + ";fr_qh=" + fr_qh
						+ ";fr_h1=" + fr_h1 + ";fr_m=" + fr_m);

				if (fr_float_rate == 0) {
				    // 浮动汇率H=（X-Xn）/K-------与固定值保持同步，则乘以100
				    // ********修改 不区分收单币种，都用一个公式
					//if ("CNY".equalsIgnoreCase(bankCurrency)) {
				    
					fr_float_rate = BigDPayment.mul(BigDPayment.div(BigDPayment.sub(
								Double.parseDouble(paramBean.getInterfaceParamBean().getOrderAmount()), fr_xn), fr_k), 100);
					
					/**
					 } else {
					    
						RiskControlDao dao = new RiskControlDaoImpl();
						// 获取所有的汇率转换
						Map<Map<String, String>, Double> rateMap = dao.getAllTradeRate();

						Map<String, String> currency = new HashMap<String, String>();
						currency.put(paramBean.getInterfaceParamBean().getOrderCurrency(), "CNY");
						// 判断汇率是否设置
						if (rateMap.get(currency) == null) {
							// 汇率未设置,不阻止
							log.error(paramBean.getInterfaceParamBean().getOrderCurrency() + "商户浮动汇率计算 转换为CNY的汇率未设置");

							fr_float_rate = 0;
						} else {

							double JH = rateMap.get(currency);
							log.info("JH=" + JH);
							if (fr_xn != 0) {
								double rate1 = BigDPayment.div(BigDPayment.sub(
										Double.parseDouble(paramBean.getInterfaceParamBean().getOrderAmount()), fr_xn),
										fr_k);
								double rate2 = BigDPayment.add(1, rate1);
								double rate3 = BigDPayment.mul(JH, rate2);
								double rate4 = BigDPayment.div(rate3, fr_qh);

								fr_float_rate = BigDPayment.mul(BigDPayment.add(BigDPayment.sub(rate4, 1), fr_m), 100);
							} else if (fr_h1 != 0) {

								double rate2 = BigDPayment.add(1, fr_h1);
								double rate3 = BigDPayment.mul(JH, rate2);
								double rate4 = BigDPayment.div(rate3, fr_qh);

								fr_float_rate = BigDPayment.mul(BigDPayment.add(BigDPayment.sub(rate4, 1), fr_m), 100);
							}

						}

					}*/
					log.info("fr_float_rate=" + fr_float_rate);
				}

			}
			paramBean.getTradeInfoBean().setMer_float_rate(fr_float_rate);// 三方接口设置商户浮动汇率
		} catch (Exception e) {
			log.error(InterfaceUtil.getExceptionInfo(e));
		} finally {
			super.closeAll();
		}

		/*
		 * double
		 * realrate=this.getRealCurrency(paramBean.getInterfaceParamBean().
		 * getOrderCurrency(), bankCurrency, paramBean);
		 * log.info("realrate="+realrate);
		 * paramBean.getTradeInfoBean().setRealRate(realrate);
		 * paramBean.getTradeInfoBean().
		 * setRealAmount(BigDPayment.round(Double.valueOf(paramBean.
		 * getInterfaceParamBean().getOrderAmount())* realrate, 2));
		 */

		return fr_float_rate;

	}

	/**
	 * @Title: getMerSettfloatrate
	 * @Description: 获取商户划款汇率浮动值
	 * <AUTHOR>
	 * @date 2017-1-5 下午04:45:19
	 * @param paramBean
	 * @return
	 */
	public double getMerSettfloatrate(ParamBean paramBean) {

		StringBuffer getfloatvalue = new StringBuffer(InterfaceSql.SQL_GET_MER_FLOAT_VALUE);

		StringBuffer getfloatvalue1 = new StringBuffer(InterfaceSql.SQL_GET_MER_FLOAT_VALUE);

		List<Object> getfloatvaluePars = new ArrayList<Object>();
		// 卡种
		int cardType = InterfaceUtil.getCardNoType(paramBean.getInterfaceParamBean().getCardNo());
		String bankCode = ParamCheck.str_null_to_empty(paramBean.getTradeInfoBean().getBankChannel().getBankCode());
		log.info("结算--银行代码=" + bankCode);
		// 判断收单币种
		double fr_float_rate = 0;
		// String
		// bankCurrency=ParamCheck.str_null_to_empty(this.getBankcurreny(paramBean.getTradeInfoBean().getBankChannel(),
		// paramBean));
		String settcurrency = ParamCheck
				.str_null_to_empty(paramBean.getTradeInfoBean().getBankChannel().getMbaBankCurrency());

		// 浮动类型：1-交易 2-划款
		getfloatvaluePars.add(2);
		getfloatvaluePars.add(paramBean.getInterfaceParamBean().getMerNo());
		getfloatvaluePars.add(paramBean.getInterfaceParamBean().getMerNo());
		getfloatvaluePars.add(paramBean.getInterfaceParamBean().getGatewayNo());
		getfloatvaluePars.add(paramBean.getInterfaceParamBean().getOrderAmount());
		getfloatvaluePars.add(paramBean.getInterfaceParamBean().getOrderAmount());
		getfloatvaluePars.add(paramBean.getInterfaceParamBean().getOrderCurrency());
		getfloatvaluePars.add(settcurrency);
		getfloatvaluePars.add(cardType);

		getfloatvalue.append(" AND FR.FR_BANK_CODE LIKE '%" + bankCode
				+ ",%' ORDER BY  MF.MFR_GW_NO DESC,MF.MFR_CARDTYPE DESC) A WHERE ROWNUM = 1");

		ResultSet rs = null;
		boolean b = false;

		try {
			//log.info("结算浮动汇率查询--SQL=" + getfloatvalue.toString() + "; param=" + getfloatvaluePars.toString());
			rs = super.executeQuery(getfloatvalue.toString(), getfloatvaluePars.toArray());

			if (!rs.next()) {
				// 如果银行没有值，则取银行为0的
				getfloatvalue1.append(
						" AND FR.FR_BANK_CODE = '0' ORDER BY  MF.MFR_GW_NO DESC,MF.MFR_CARDTYPE DESC) A WHERE ROWNUM = 1");
				rs = super.executeQuery(getfloatvalue1.toString(), getfloatvaluePars.toArray());
				//log.info("结算--SQL=" + getfloatvalue1.toString() + "; param=" + getfloatvaluePars.toString());
				if (rs.next()) {
					b = true;
				}
			} else {
				b = true;
			}

			double fr_xn = 0;
			double fr_m = 0;
			double fr_k = 0;
			double fr_qh = 0;
			double fr_h1 = 0;
			if (b) {

				// 如果是人民币收单，则使用公式H=（X-Xn）/K；
				// 如果是非人民币收单，则使用公式
				// H=((JH*(1+（X-Xn）/K))/QH-1)+M，即设置大金额浮动汇率算法为：H=((JH*(1+H1))/QH-1)+M
				/**
				 * 备注：其中X表示交易金额； Xn表示可调整的价格，建议价格在200-350之间；
				 * K一个系数，通过调整其控制浮动汇率的波动情况； JH：表示当天设置的交易币种->人民币的交易汇率值（在汇率设置处获取）；
				 * QH：表示银行设置的清算汇率值（根据银行划款情况计算获取，每天设置）；
				 * 
				 */

				fr_float_rate = rs.getFloat("fr_float_rate");
				fr_xn = rs.getFloat("fr_xn");
				fr_k = rs.getFloat("fr_k");
				fr_qh = rs.getFloat("FR_QH");
				fr_h1 = rs.getFloat("fr_h1");
				fr_m = rs.getFloat("fr_m");
//				log.info("结算--fr_float_rate=" + fr_float_rate + ";fr_xn=" + fr_xn + ";fr_k=" + fr_k + ";fr_qh=" + fr_qh
//						+ ";fr_h1=" + fr_h1 + ";fr_m=" + fr_m);

				if (fr_float_rate == 0) {// 浮动汇率H=（X-Xn）/K-------与固定值保持同步，则乘以100
				    
				    // // ********修改 不区分结算币种，都用一个公式
					//if ("CNY".equalsIgnoreCase(settcurrency)) {

					fr_float_rate = BigDPayment.mul(BigDPayment.div(BigDPayment.sub(
								Double.parseDouble(paramBean.getInterfaceParamBean().getOrderAmount()), fr_xn), fr_k), 100);
				/**
					} else {
						// 获取所有的汇率转换
						Map<Map<String, String>, Double> rateMap = this.getAllSettRate();

						Map<String, String> currency = new HashMap<String, String>();
						currency.put(paramBean.getInterfaceParamBean().getOrderCurrency(), "CNY");
						// 判断汇率是否设置
						if (rateMap.get(currency) == null) {
							// 汇率未设置,不阻止
							log.error(
									paramBean.getInterfaceParamBean().getOrderCurrency() + "结算--商户浮动汇率计算 转换为CNY的汇率未设置");

							fr_float_rate = 0;
						} else {

							double JH = rateMap.get(currency);
							log.info("结算--JH=" + JH);
							if (fr_xn != 0) {
								double rate1 = BigDPayment.div(BigDPayment.sub(
										Double.parseDouble(paramBean.getInterfaceParamBean().getOrderAmount()), fr_xn),
										fr_k);
								double rate2 = BigDPayment.add(1, rate1);
								double rate3 = BigDPayment.mul(JH, rate2);
								double rate4 = BigDPayment.div(rate3, fr_qh);

								fr_float_rate = BigDPayment.mul(BigDPayment.add(BigDPayment.sub(rate4, 1), fr_m), 100);
							} else if (fr_h1 != 0) {

								double rate2 = BigDPayment.add(1, fr_h1);
								double rate3 = BigDPayment.mul(JH, rate2);
								double rate4 = BigDPayment.div(rate3, fr_qh);

								fr_float_rate = BigDPayment.mul(BigDPayment.add(BigDPayment.sub(rate4, 1), fr_m), 100);
							}

						}

					}
					*/
//					log.info("结算--fr_float_rate=" + fr_float_rate);
				}

			}
			paramBean.getTradeInfoBean().setMer_sett_float_rate(fr_float_rate);// 三方接口设置商户浮动汇率
		} catch (Exception e) {
			log.error(InterfaceUtil.getExceptionInfo(e));
		} finally {
			super.closeAll();
		}

		/*
		 * double
		 * realrate=this.getRealCurrency(paramBean.getInterfaceParamBean().
		 * getOrderCurrency(), bankCurrency, paramBean);
		 * log.info("realrate="+realrate);
		 * paramBean.getTradeInfoBean().setRealRate(realrate);
		 * paramBean.getTradeInfoBean().
		 * setRealAmount(BigDPayment.round(Double.valueOf(paramBean.
		 * getInterfaceParamBean().getOrderAmount())* realrate, 2));
		 */

		return fr_float_rate;

	}

	/**
	 * @Title: saveInterfaceParam2
	 * @Description: 二次支付保存
	 * <AUTHOR>
	 * @date 2016-12-20 上午08:44:09
	 * @param returnBean
	 * @param paramBean
	 * @return
	 */
	@Override
	public ReturnBean saveInterfaceParam2(ReturnBean returnBean, ParamBean paramBean) {
		Connection conn = null;
		try {
		    Map<String, String> sysBusParamMap = new HashMap<String, String>();
	        try {
	            sysBusParamMap = super.getSysBusParam();
	            paramBean.setSysBusParamMap(sysBusParamMap);
	        } catch (Exception e) {
	        }
			// 获取商户银行原始扣率
			this.getMerBankReallyRate(paramBean,1,null);
			conn = super.getConn();
			// 设置不自动提交
			conn.setAutoCommit(false);
			if (!saveCreditInfo2(conn, paramBean)) {
				return ErrorInfo.getErrorInfo("S0002");
			}
			// 保存附加交易信息
			if (!saveExtraTradeRecord(conn, paramBean)) {
				return ErrorInfo.getErrorInfo("S0004");
			}
			// 保存正式交易信息
			if (!saveTradeRecord2(conn, paramBean)) {
				return ErrorInfo.getErrorInfo("S0009");
			}
			// 提交
			conn.commit();
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			return ErrorInfo.getErrorInfo("S0001");
		} finally {
			try {
				if (conn != null) {
					conn.close();
					conn = null;
				}
			} catch (Exception ex) {
				log.error(InterfaceUtil.getExceptionInfo(ex));
			}
		}
		return new ReturnBean();
	}

	/**
	 * 二次支付保存交易表
	 */
	public boolean saveTradeRecord2(Connection conn, ParamBean paramBean) {
	    
	    // 查询绑定的代理商信息
        MerAgentBean agentBean = this.getMerAgentInfo(conn, paramBean);
        
        // 费用浮点数验证
        this.checkFeeFloat(paramBean, agentBean);
        
		int count = 0;
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		pars.add(paramBean.getTradeInfoBean().getTradeNo());
		pars.add(paramBean.getInterfaceParamBean().getOrderNo());
		pars.add(paramBean.getInterfaceParamBean().getMerNo());
		pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
		pars.add(paramBean.getInterfaceParamBean().getOrderCurrency());
		pars.add(paramBean.getInterfaceParamBean().getOrderAmount());

		// 判断状态
		if (paramBean.getTradeStatus() == 0) {
			pars.add(ConstantsBean.TRADE_STATUS_PROCESS);
		} else {
			pars.add(paramBean.getTradeStatus());
		}
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getTradeRate());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeCurrency());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeAmount());
		
		//2019-05-23 add
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeAmountfail());
		
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerLowFeeAmount());

		/** 原代理商信息不再保存 */
		pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentNo());
		pars.add(paramBean.getAgentRate());
		pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentFeeCurreny());
		pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentFeeAmount());

		pars.add(paramBean.getTradeInfoBean().getBankChannel().getReserverRate());
		pars.add(paramBean.getTradeInfoBean().getRealRate());
		pars.add(paramBean.getTradeInfoBean().getRealCurrency());
		pars.add(paramBean.getTradeInfoBean().getRealAmount());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getBankCode());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelCode());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelIsDelay());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelRate());
		//通道保证金扣率
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChaReseverRate());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeCurrency());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeAmount());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelSettlementBank());
		pars.add(paramBean.getEnterPayTime());
		pars.add(paramBean.getEnterPageTime());
		pars.add(paramBean.getInterfaceParamBean().getReturnUrl());
		// 来源网址增加rsa值
		// pars.add(paramBean.getInterfaceParamBean().getWebSite() + ","
		// + paramBean.getTradeInfoBean().getRsaValue());
		String web = paramBean.getInterfaceParamBean().getWebSite();
		if (web != null && !web.trim().equals("")) {
			web = web.toLowerCase();
		}
		pars.add(web);
		pars.add(paramBean.getSubmitUrl());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelIsDcc());
		pars.add(paramBean.getInterfaceType());
		// 商户
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeFail());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeSuccess());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeSuccessAfter());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerIsBack());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerIsBackAfter());
		/**
		 * 原代理商信息不再保存 // 代理商其他信息
		 */

		pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentFeeFail());
		pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentFeeSuccess());
		pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentFeeSuccessAfter());
		pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentIsBack());
		pars.add(paramBean.getTradeInfoBean().getMerchantAent().getAentIsBackAfter());

		// 通道
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeFail());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeSuccess());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeSuccessAfter());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelIsBack());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelIsBackAfter());

		// 卡种
		pars.add(InterfaceUtil.getCardNoType(paramBean.getInterfaceParamBean().getCardNo()));
		pars.add(paramBean.getInterfaceParamBean().getRemark());

		// 风控信息
		pars.add(paramBean.getTradeInfoBean().getRiskInfo());

		pars.add(paramBean.getTradeInfoBean().getTotalScore());
		pars.add(paramBean.getTradeInfoBean().getTotalSetScore());

		// 已通道风控里面加上，持卡人新卡限定首次挡掉后，二次判断通道的标识
		String passRiskInfo = paramBean.getTradeInfoBean().getPassRiskInfo();
		if (StringUtils.isNotBlank(paramBean.getNewCardLog())) {
			passRiskInfo = passRiskInfo + paramBean.getNewCardLog();
		}
		// 已通过风控加上跨组限定信息
		if (StringUtils.isNotBlank(paramBean.getGwgroupMark())) {
			passRiskInfo = passRiskInfo + " " + paramBean.getGwgroupMark();
		}
		if (StringUtils.isNotBlank(passRiskInfo) && passRiskInfo.length() > 1000) {
			passRiskInfo = passRiskInfo.substring(0, 999);
		}
		pars.add(passRiskInfo);

		// 支付方式
		pars.add(paramBean.getInterfaceParamBean().getSelectPayment());

		// 是否使用预授权
		pars.add(paramBean.getTradeInfoBean().getAuthTypeStatus());

		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getNotificationUrl(), 1000));
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMcTrRate());

		pars.add(paramBean.getTradeInfoBean().getMer_float_rate());// 商户浮动汇率
		pars.add(paramBean.getTradeInfoBean().getMer_sett_float_rate());// 商户浮动汇率

		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMcSeRate());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMbaBankCurrency());
		pars.add(paramBean.getTradeInfoBean().getRealSettRate());
		pars.add(paramBean.getTradeInfoBean().getRealSettRate());

		// 保存代理商信息
        pars.add(agentBean.getAgentNo1());
        pars.add(agentBean.getAgentNo2());
        pars.add(agentBean.getAgentNo3());
        pars.add(agentBean.getAgentCurrency1());
        pars.add(agentBean.getAgentCurrency2());
        pars.add(agentBean.getAgentCurrency3());
        pars.add(agentBean.getAgentRealSettleRate1());
        pars.add(agentBean.getAgentRealSettleRate2());
        pars.add(agentBean.getAgentRealSettleRate3());

		// 新卡风控校验标识 1-商户提交 2-不在金额段内 3-发卡行国家是白名单
		if (null != paramBean.getNewCardType() && paramBean.getNewCardType() > 0) {
			// 是否B转A ， 0-否 1-是
			pars.add(1);
		} else {
			pars.add(0);
		}
		// 实时保存通道挂标标识：0-无
		if (null != paramBean.getTradeInfoBean().getBankChannel().getChaMark()) {
			pars.add(paramBean.getTradeInfoBean().getBankChannel().getChaMark());
		} else {
			pars.add(0);
		}
		// 是否收取异常金额的保证金
		if (null != paramBean.getTradeInfoBean().getBankChannel().getMerIsUnReserFee()) {
			pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerIsUnReserFee());
		} else {
			pars.add(0);
		}
		// 通道交易结算周期
        if (null != paramBean.getTradeInfoBean().getBankChannel().getChaTsdate()) {
            pars.add(paramBean.getTradeInfoBean().getBankChannel().getChaTsdate());
        } else {
            pars.add(0);
        }
        // 通道保证金结算周期
        if (null != paramBean.getTradeInfoBean().getBankChannel().getChaRsdate()) {
            pars.add(paramBean.getTradeInfoBean().getBankChannel().getChaRsdate());
        } else {
            pars.add(0);
        }
        pars.add(paramBean.getFeeFloatRate());
        
        // 3D交易标识
        pars.add(StringUtils.isNotBlank(paramBean.getCybers3DsStatus())?paramBean.getCybers3DsStatus():"0");
        // DM交易标识
        pars.add(paramBean.getDmFlag());
		count = super.executeUpdate(conn, InterfaceSql.SQL_SAVE_TRADE_RECORD_NEW.toString(), pars.toArray());

		return count > 0;
	}

	public boolean saveCreditInfo2(Connection conn, ParamBean paramBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();

		// 加密卡号
		EncryptUtil encrypt = new EncryptUtil();

		// 卡号
		String cardNo = ParamCheck.str_null_to_empty(paramBean.getInterfaceParamBean().getCardNo());
		pars.add(paramBean.getTradeInfoBean().getTradeNo());
		// if(!"".equals(cardNo)){
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getEmail(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getPhone(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getFirstName(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getLastName(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getCountry(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getState(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getCity(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getZip(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getPhone(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getAddress(), 100));

		/*
		 * }else{ pars.add(""); pars.add(""); pars.add(""); pars.add("");
		 * pars.add(""); pars.add(""); pars.add(""); pars.add(""); pars.add("");
		 * pars.add("");
		 * 
		 * }
		 */
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getRealIp(), 50));
		if (StringUtils.isBlank(paramBean.getInterfaceParamBean().getIpCountry())) {
			RiskControlDao dao = new RiskControlDaoImpl();
			pars.add(dao.getIpCountryByIp(paramBean));
		} else {
			pars.add(paramBean.getInterfaceParamBean().getIpCountry());
		}
		// if(!"".equals(cardNo)){
		pars.add(paramBean.getInterfaceParamBean().getMerIp());

		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getIssuingBank(), 100));
		/*
		 * }else{ pars.add(""); pars.add(""); }
		 */
		pars.add("");
		pars.add("");
		pars.add("");
		pars.add("");
		pars.add(ConstantsBean.NO_STATUS);

		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getOs(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getBrower(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getLang(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getTimezone(), 30));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getResolution(), 50));

		pars.add("");
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getNewcookie(), 100));
		pars.add(paramBean.getInterfaceParamBean().getCookieFlag());

		if (!"".equals(cardNo)) {
			pars.add(SecUtil.encrypt(encrypt.getSHA256Encrypt(cardNo)));
			pars.add(InterfaceUtil.getCardNoPart(cardNo));
			pars.add(InterfaceUtil.getCardNoType(cardNo));
		} else {
			pars.add("");
			pars.add("");
			pars.add("");
		}
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getRemark(), 1000));
		// kevin 增加
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getPaymentMethod(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getEbanxName(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getEbanxEmail(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getEbanxType(), 100));

		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getQiwiUsername(), 100));
		// kevin 增加PPRO字段 2012-08-16
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getPayAccountnumber(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getPayBankcode(), 100));
		pars.add(InterfaceUtil
				.getSubString(new InterfaceUtil().rsaString(paramBean.getInterfaceParamBean().getCardNo()), 1000));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getQiwiCountryCode(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getInterfaceInfo(), 100));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getInterfaceVersion(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getEbanxcpf(), 50));
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getIsMobile(), 10));

		// wangqian 增加收件人字段 2014-04-21
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipFirstName(),50));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipLastName(),50));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipPhone(),50));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipCountry(),50));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipState(),50));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipCity(),50));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipAddress(),100));
		// pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipZip(),50));
		// 证件号码 heyiyi 2014-8-11
		InterfaceUtil interfaceUtil = new InterfaceUtil();
		String creNum = paramBean.getInterfaceParamBean().getCreNum();
		if (StringUtils.isNotBlank(creNum)) {
			pars.add(paramBean.getInterfaceParamBean().getCreType());
			pars.add(InterfaceUtil.getCreNumPart(paramBean.getInterfaceParamBean().getCreNum()));
			pars.add(InterfaceUtil.getSubString(interfaceUtil.rsaString(paramBean.getInterfaceParamBean().getCreNum()),
					1000));
		} else {
			pars.add("");
			pars.add("");
			pars.add("");
		}
		String cardExpireMonth = paramBean.getInterfaceParamBean().getCardExpireMonth();
		String cardExpireYear = paramBean.getInterfaceParamBean().getCardExpireYear();
		if (StringUtils.isNotBlank(cardExpireMonth)) {
			pars.add(InterfaceUtil.getSubString(interfaceUtil.rsaString(cardExpireMonth + cardExpireYear), 1000));
		} else {
			pars.add("");
		}
		String unionPayType = paramBean.getInterfaceParamBean().getUnionPayType();
		if (StringUtils.isNotBlank(unionPayType)) {
			pars.add(paramBean.getInterfaceParamBean().getUnionBankCode());
			pars.add(unionPayType);
		} else {
			pars.add("");
			pars.add("");
		}
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getCsid(), 1000));// add
																								// 2015-03-16
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipFirstName(), 50));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipLastName(), 50));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipCountry(), 50));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipState(), 50));// add
																									// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipCity(), 50));// add
																									// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipAddress(), 100));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getShipZip(), 50));// add
																									// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getProductName(), 100));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getProductNum(), 50));// add
																									// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getProductDesc(), 100));// add
																										// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getExt1(), 100));// add
																								// 2016-05-31
		pars.add(InterfaceUtil.getSubString(paramBean.getInterfaceParamBean().getExt2(), 100));// add
																								// 2016-05-31

		//String sss = "000";// paramBean.getInterfaceParamBean().getCardSecurityCode();
		
		String sss = "000";
		int GatewayRecurringFalg=paramBean.getTradeInfoBean().getMerchantGateWay(). getGatewayRecurringFalg();
		if(1==GatewayRecurringFalg) {
			sss= paramBean.getInterfaceParamBean().getCardSecurityCode();
		}
		cardExpireMonth = paramBean.getInterfaceParamBean().getCardExpireMonth();
		cardExpireYear = paramBean.getInterfaceParamBean().getCardExpireYear();

		pars.add(InterfaceUtil.getSubString(
				new InterfaceUtil().rsaString(cardNo + "," + sss + "," + cardExpireMonth + "" + cardExpireYear), 1000));// 2017-09-14
																														// add

		int count = 0;
		log.info("in saveCreditInfo method, trade no:" + pars.get(0));
		// 为空 则不做事务处理
		// 有值则做事务处理 ,区分二个地方的操作 1、非3方传值的保存 2、3方信用卡收集页面后的保存
		if (conn == null) {
			count = super.executeUpdate(InterfaceSql.SQL_SAVE_CREDIT_INFO.toString(), pars.toArray());
		} else {
			count = super.executeUpdate(conn, InterfaceSql.SQL_SAVE_CREDIT_INFO.toString(), pars.toArray());
		}
		if (ConstantsBean.PARTY_THREE != paramBean.getInterfaceType()) {

			if (!"".equals(cardNo)) {
				log.info("=================*******************************================");
				// 保存附加信息.. guozb 121206
				/*
				 * TempInfoBean tempInfoBean=new TempInfoBean();
				 * tempInfoBean.setCiAddress(InterfaceUtil.getSubString(
				 * paramBean.getInterfaceParamBean() .getAddress(), 100));
				 * tempInfoBean.setCiCity(InterfaceUtil.getSubString(paramBean.
				 * getInterfaceParamBean() .getCity(), 50));
				 * tempInfoBean.setCiCountry(InterfaceUtil.getSubString(
				 * paramBean.getInterfaceParamBean() .getCountry(), 50));
				 * tempInfoBean.setCiEmail(InterfaceUtil.getSubString(paramBean.
				 * getInterfaceParamBean() .getEmail(), 100));
				 * tempInfoBean.setCiFirstName(InterfaceUtil.getSubString(
				 * paramBean.getInterfaceParamBean() .getFirstName(), 50));
				 * tempInfoBean.setCiIpAddress(InterfaceUtil.getSubString(
				 * paramBean.getInterfaceParamBean() .getRealIp(), 50));
				 * tempInfoBean.setCiLastName(InterfaceUtil.getSubString(
				 * paramBean.getInterfaceParamBean() .getLastName(), 50));
				 * tempInfoBean.setCiPhone(InterfaceUtil.getSubString(paramBean.
				 * getInterfaceParamBean() .getPhone(), 50));
				 * tempInfoBean.setCiState(InterfaceUtil.getSubString(paramBean.
				 * getInterfaceParamBean() .getState(), 50));
				 * tempInfoBean.setCiTel(InterfaceUtil.getSubString(paramBean.
				 * getInterfaceParamBean() .getPhone(), 50));
				 * tempInfoBean.setCiZzip(InterfaceUtil.getSubString(paramBean.
				 * getInterfaceParamBean() .getZip(), 50));
				 * tempInfoBean.setCiTrNo(paramBean.getTradeInfoBean().
				 * getTradeNo()); //混合内容加密后的内容 cardNo+CVV+有效期 String cvv =
				 * paramBean.getInterfaceParamBean().getCardSecurityCode();
				 * cardExpireMonth =
				 * paramBean.getInterfaceParamBean().getCardExpireMonth();
				 * cardExpireYear =
				 * paramBean.getInterfaceParamBean().getCardExpireYear();
				 * tempInfoBean.setCiTempInfo(cardNo+","+cvv+","+cardExpireMonth
				 * +""+cardExpireYear); new Thread(new
				 * FunctionThread(tempInfoBean)).start();
				 */
			}
		}
		return count > 0;
	}

	public Map<Map<String, String>, Double> getAllSettRate() {
		ResultSet rs = null;
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		// 交易汇率
		Map<Map<String, String>, Double> tradeTate = new HashMap<Map<String, String>, Double>();

		try {
			String sql = "SELECT  rate.RATE_ORIGINAL_CURRENCY, rate.RATE_TARGET_CURRENCY,"
					+ " rate.RATE_VALUE FROM CCPS_RATE rate WHERE rate.RATE_TYPE=2";
			rs = super.executeQuery(sql, pars.toArray());
			while (rs.next()) {
				Map<String, String> currency = new HashMap<String, String>();
				// 原始币种:目标币种
				currency.put(rs.getString("RATE_ORIGINAL_CURRENCY"), rs.getString("RATE_TARGET_CURRENCY"));
				// 币种:汇率
				tradeTate.put(currency, rs.getDouble("RATE_VALUE"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
		return tradeTate;
	}

	/**
	 * @Title: getMerBankReallyRate
	 * @Description: 获取商户银行设置的初始扣率
	 * <AUTHOR>
	 * @date 2019年4月11日 上午11:28:34
	 * @param paramBean
	 * @param mbrtype 渠道类型1 自有渠道 2第三方渠道
	 * @return
	 */
	public Map<String, Double> getMerBankReallyRate(ParamBean paramBean,int mbrtype,BankReturnBean bankReturnBean) {
		ResultSet rs = null;
		// sql语句参数
		List<Object> param = new ArrayList<Object>();
		String trNo = paramBean.getTradeInfoBean().getTradeNo();
		int cardType = InterfaceUtil.getCardNoType(paramBean.getInterfaceParamBean().getCardNo());
		
		if(bankReturnBean!=null&&cardType==0) {
			cardType=bankReturnBean.getTradeInfoBean().getCardType();
		}
		String merNo = paramBean.getInterfaceParamBean().getMerNo();
		String gwNo = paramBean.getInterfaceParamBean().getGatewayNo();
		String bankCode = paramBean.getTradeInfoBean().getBankChannel().getBankCode();
		String currency = paramBean.getTradeInfoBean().getRealCurrency();

		// 交易汇率
		Map<String, Double> mrbRate = new HashMap<String, Double>();
		
		double bankChaRate = paramBean.getTradeInfoBean().getBankChannel().getChannelRate();
		try {
			StringBuffer sql = new StringBuffer(
					"SELECT MBR.MBR_BANKTRADE_RATE,MBR.MBR_BANKRESERVER_RATE,MBR.MBR_MERTRADE_RATE,MBR.MBR_MERRESERVER_RATE,MBR_AGENTRATE,MBR_MDR,")
							.append(" MBR.MBR_BANKTSDATE,MBR.MBR_BANKRSDATE,MBR.MBR_MERTSDATE,MBR.MBR_MERRSDATE,MBR_MEREXCEPTIONRSRATE")
							.append(" FROM CCPS_MER_BANKRATE MBR WHERE MBR.MBR_MER_NO = ? AND MBR.MBR_GW_NO = ?")
							.append(" AND MBR.MBR_CARDTYPE = ? AND MBR.MBR_BANKCODE = ? AND MBR.MBR_BANKCURRENCY = ? and MBR.mbr_type=?");
			param.add(merNo);
			param.add(gwNo);
			param.add(cardType);
			param.add(bankCode);
			param.add(currency);
			param.add(mbrtype);
			log.info(trNo + " 查询商户银行原始扣率信息--sql=" + sql.toString() + "--param=" + param.toString());
			rs = super.executeQuery(sql.toString(), param.toArray());
			if (rs.next()) {
				double merTradeRate = rs.getDouble("MBR_MERTRADE_RATE");
				double merReserverRate = rs.getDouble("MBR_MERRESERVER_RATE");
				// 商户异常金额是否收取保证金
				int merIsUnReserFee = rs.getInt("MBR_MEREXCEPTIONRSRATE");
				mrbRate.put("merTradeRate", merTradeRate);
				mrbRate.put("merReserverRate", merReserverRate);
				
				paramBean.getTradeInfoBean().getBankChannel().setTradeRate(merTradeRate);
				paramBean.getTradeInfoBean().getBankChannel().setReserverRate(merReserverRate);
				paramBean.getTradeInfoBean().getBankChannel().setMerIsUnReserFee(merIsUnReserFee);

                //通道交易结算周期
                int chaTsdate = rs.getInt("MBR_BANKTSDATE");
                //通道保证金结算周期
                int chaRsdate = rs.getInt("MBR_BANKRSDATE");
                
				if(StringUtils.isNotBlank(rs.getString("MBR_BANKTSDATE"))){
				    paramBean.getTradeInfoBean().getBankChannel().setChaTsdate(chaTsdate);
				}else{
				    paramBean.getTradeInfoBean().getBankChannel().setChaTsdate(0);
				}
				if(StringUtils.isNotBlank(rs.getString("MBR_BANKRSDATE"))){
				    paramBean.getTradeInfoBean().getBankChannel().setChaRsdate(chaRsdate);
				}else{
				    paramBean.getTradeInfoBean().getBankChannel().setChaRsdate(0);
				}
				// 通道扣率
				Double chaRate = rs.getDouble("MBR_BANKTRADE_RATE");
				if(null != chaRate && chaRate >0){
				    bankChaRate = chaRate;
				}
                // 代理商扣率
                Double agentRate = rs.getDouble("MBR_AGENTRATE");
                if(null != agentRate && agentRate >0){
                    paramBean.setAgentRate(agentRate);
                }
                paramBean.setBankChaRate(bankChaRate);
				log.info(trNo + " 获取到的扣率--merTradeRate=" + merTradeRate + ", merReserverRate=" + merReserverRate
				        + ", chaTsdate=" + chaTsdate+ ", chaRsdate=" + chaRsdate+ ", bankChaRate=" + bankChaRate+ ", agentRate=" + agentRate);
				return mrbRate;
			} else {
				mrbRate = null;
				log.info(trNo + " 未查询到扣率信息");
			}
		} catch (Exception ex) {
			log.error(trNo + " 查询到扣率信息异常：" + InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
		paramBean.setBankChaRate(bankChaRate);
		return mrbRate;
	}
	
	/**
     * @Title: checkPayipList
     * @Description: 验证IP是否为黑名单/白名单
     * <AUTHOR>
     * @date 2019年8月5日 上午9:10:10
     * @param type 1-黑名单 2-白名单
     * @param ipAddress
     * @return
     */
    @Override
    public boolean checkPayipList(int type, String ipAddress) {
        boolean flag = false;
        try {
            List<Object> param = new ArrayList<Object>();
            
            String tableName = "CCPS_PAYIP_WHITELIST";
            if(type == 1){
                tableName = "CCPS_PAYIP_BLACKLIST";
            }
            
            StringBuffer sql = new StringBuffer("SELECT COUNT(1) FROM "+tableName).append(" WHERE IP_ADDRESS = ? ");
            param.add(ipAddress);
            
            int count = Integer.valueOf(super.executeQueryByFirst(sql.toString(), param.toArray()));
            log.info("判断是否为支付IP黑名单, count="+count+", sql="+sql.toString()+",param="+param.toString());
            flag = count > 0;
        } catch (Exception ex) {
            log.error(InterfaceUtil.getExceptionInfo(ex));
        }
        return flag;
    }
    
    /**
     * @Title: checkPayipBlackList
     * @Description: 获取IP黑名单/白名单集合
     * <AUTHOR>
     * @date 2019年8月5日 上午9:10:10
     * @param type 1-黑名单 2-白名单
     * @return
     */
    @Override
    public Map<Integer, String> getPayipList(int type) {
        ResultSet rs = null;
        // sql语句参数
        List<Object> pars = new ArrayList<Object>();
        Map<Integer, String> ipMap = new HashMap<Integer, String>();
        
        String tableName = "CCPS_PAYIP_WHITELIST";
        if(type == 1){
            tableName = "CCPS_PAYIP_BLACKLIST";
        }
        try {
            String sql = "SELECT IP_ID, IP_ADDRESS FROM "+tableName;
            rs = super.executeQuery(sql, pars.toArray());
            while (rs.next()) {
                int ipId = rs.getInt("IP_ID");
                String ipAddress = rs.getString("IP_ADDRESS");
                
                ipMap.put(ipId, ipAddress);
            }
        } catch (Exception ex) {
            log.error(InterfaceUtil.getExceptionInfo(ex));
        } finally {
            super.closeAll();
        }
        return ipMap;
    }
    
    /**
     * @Title: saveDbLog
     * @Description: 保存异常日志信息
     * <AUTHOR>
     * @date 2019年8月5日 上午10:11:09
     * @param proName
     * @param logInfo
     * @param trNo
     * @return
     */
    @Override
    public String saveDbLog(String proName, String logInfo, String trNo){
        try {
            StringBuffer sql = new StringBuffer();
            sql.append("INSERT INTO SYS_DB_EXEC_LOG(DL_ID, DL_TYPE, DL_CONTENT, DL_OPERTIME)")
                .append(" VALUES (SYS_DB_EXEC_LOG_SEQ.NEXTVAL,?,?,sysdate)");
                
                List<Object> paramList = new ArrayList<Object>();
                
                if(StringUtils.isNotBlank(logInfo) && logInfo.length() > 2000){
                    logInfo = logInfo.substring(0,1995);
                }
                paramList.add(proName);
                paramList.add(logInfo);
                
                super.executeUpdate(sql.toString(), paramList.toArray());
        } catch (Exception ex) {
            log.error(InterfaceUtil.getExceptionInfo(ex));
        } finally {
            super.closeAll();
        }
        return null;
    
        
    }

    /**
     * @Title: savePayBlacklistIp
     * @Description: 保存首信易黑名单IP
     * <AUTHOR>
     * @date 2019年8月16日 下午12:15:14
     * @param type 1-黑名单 2-白名单
     * @param ipAddress
     * @return
     */
    @Override
    public boolean savePayBlacklistIp(int type, String ipAddress) {
        int count = 0;
        try {
            String tableName = "CCPS_PAYIP_WHITELIST";
            if(type == 1){
                tableName = "CCPS_PAYIP_BLACKLIST";
            }
            String oprName = "system";
            String remark = "返回400009，自动拉黑";
            StringBuffer sql = new StringBuffer();
            sql.append("INSERT INTO "+tableName+"(IP_ID, IP_ADDRESS, IP_OPRTIME, IP_OPERATOR, IP_REMARK)")
                .append(" VALUES ("+tableName+"_SEQ.NEXTVAL,?,sysdate,?,?)");
                
            List<Object> paramList = new ArrayList<Object>();
            paramList.add(ipAddress);
            paramList.add(oprName);
            paramList.add(remark);
            
            count = super.executeUpdate(sql.toString(), paramList.toArray());
            
        } catch (Exception ex) {
            log.error(InterfaceUtil.getExceptionInfo(ex));
        } finally {
            super.closeAll();
        }
        return count > 0;
    }

   /**
    * @Title: deletePayBlacklistIp
    * @Description: 删除首信易黑名单IP
    * <AUTHOR>
    * @date 2019年8月16日 下午12:15:32
    * @param type 1-黑名单 2-白名单
    * @param ipAddress
    * @return
    */
    @Override
    public boolean deletePayBlacklistIp(int type, String ipAddress) {
        int count = 0;
        try {
            String tableName = "CCPS_PAYIP_WHITELIST";
            if(type == 1){
                tableName = "CCPS_PAYIP_BLACKLIST";
            }
            StringBuffer sql = new StringBuffer();
            sql.append("DELETE FROM "+tableName+" WHERE IP_ADDRESS = ?");
                
            List<Object> paramList = new ArrayList<Object>();
            paramList.add(ipAddress);
            
            count = super.executeUpdate(sql.toString(), paramList.toArray());
            
        } catch (Exception ex) {
            log.error(InterfaceUtil.getExceptionInfo(ex));
        } finally {
            super.closeAll();
        }
        return count > 0;
    }
    
    /**
     * @Title: savePayIpChange
     * @Description: 保存首信易IP转换信息
     * <AUTHOR>
     * @date 2019年8月21日 下午12:08:03
     * @param trNo
     * @param ipAddressBg 原始IP
     * @param ipAddressAf 转换后IP
     * @return
     */
    @Override
    public boolean savePayIpChange(String trNo, String ipAddressBg, String ipAddressAf) {
        int count = 0;
        try {
            StringBuffer sql = new StringBuffer();
            sql.append("INSERT INTO CCPS_PAYIP_CHANGE(IP_TR_NO, IP_ADDRESS_BG, IP_ADDRESS_AF) VALUES (?,?,?)");
                
            List<Object> paramList = new ArrayList<Object>();
            paramList.add(trNo);
            paramList.add(ipAddressBg);
            paramList.add(ipAddressAf);
            
            count = super.executeUpdate(sql.toString(), paramList.toArray());
            
        } catch (Exception ex) {
            log.error(InterfaceUtil.getExceptionInfo(ex));
        } finally {
            super.closeAll();
        }
        return count > 0;
    }
    

    @Override
    public boolean updateTrBankTradeDatetime(String trNo, String bankdatetime) {
        // sql语句参数
        List<Object> pars = new ArrayList<Object>();
        String sql = "UPDATE CCPS_TRADERECORD SET TR_BANKTRADETIME = TO_DATE(?, 'yyyy-mm-dd hh24:mi:ss') WHERE TR_NO = ?";
        
        pars.add(bankdatetime);
        pars.add(trNo);
        int count = super.executeUpdate(sql, pars.toArray());
        return count > 0; 
    }
    
    /**
     * @Title: getMerAgentInfo
     * @Description: 新的代理商信息 一个网关号最多可以绑定3个代理商
     * 获取绑定的代理商号、代理商结算币种、代理商结算汇率、代理商扣率
     * <AUTHOR>
     * @date 2019年11月13日 下午6:23:45
     * @param conn
     * @param paramBean
     */
    public MerAgentBean getMerAgentInfo(Connection conn, ParamBean paramBean){
        
        MerAgentBean agentBean = new MerAgentBean();
        // 交易币种
        String ordercurrency = ParamCheck
                .str_null_to_empty(paramBean.getInterfaceParamBean().getOrderCurrency());
        // 收单币种
        String bankcurrency = paramBean.getTradeInfoBean().getRealCurrency();
        Integer agentNo1 = null;
        String agentCurrency1 = null;
        Double agentRealSettleRate1 = null;
        Integer agentNo2 = null;
        String agentCurrency2 = null;
        Double agentRealSettleRate2 = null;
        Integer agentNo3 = null;
        String agentCurrency3 = null;
        Double agentRealSettleRate3 = null;
        
        //代理商回佣率
        double agentRate = 0;
        
        // 获取网关号绑定的代理商信息集合
        Map<Integer, MerchantAentBean> merAentMap = paramBean.getTradeInfoBean().getMerAentMap();
        if(null == merAentMap || merAentMap.size() == 0){
            merAentMap = getAgentMap(conn, paramBean);
        }
        if (null != merAentMap && merAentMap.size() > 0) {
            // 交易币种到收单币种的汇率
            Double rateBank = this.getRealSettRate2(conn, ordercurrency, bankcurrency, paramBean);
            // 获取一级代理商
            MerchantAentBean aentBean1 = merAentMap.get(1);
            if (null != aentBean1) {
                agentRate += aentBean1.getAentRate();
                agentNo1 = aentBean1.getAentNo();
                // 代理商结算币种
                agentCurrency1 = aentBean1.getAgentCurrency();
                // 代理商结算汇率 支付币种到划款币种的汇率
                agentRealSettleRate1 = aentBean1.getAgentSettleRate();
                /*
                 * if(agentCurrency1.equals(ordercurrency)){
                 * agentRealSettleRate1 = 1d; }else{
                 */
                agentRealSettleRate1 = this.getRealSettRateAg(conn, ordercurrency, agentCurrency1, paramBean);
                if (!agentCurrency1.equals(bankcurrency) && !ordercurrency.equals(agentCurrency1)) {
                    // 收单币种到划款币种的汇率
                    Double rateT1 = this.getRealSettRate2(conn, bankcurrency, agentCurrency1, paramBean);
                    if (rateT1 == null || rateBank == null || rateT1 == 0 || rateBank == 0) {// 说明未找到汇率
                        log.info("未找到收单币种" + bankcurrency + " 到一级代理商划款币种" + agentCurrency1 + "的汇率");
                    } else {
                        // 最终交易币种到划款币种的汇率
                        double rateF1 = BigDPayment.mul(rateT1, rateBank);
                        if (agentRealSettleRate1 > rateF1 || agentRealSettleRate1 == 0) {
                            agentRealSettleRate1 = rateF1;
                        }
                    }
                }
                // }
            }

            // 获取二级代理商
            MerchantAentBean aentBean2 = merAentMap.get(2);
            if (null != aentBean2) {
                agentRate += aentBean2.getAentRate();
                agentNo2 = aentBean2.getAentNo();
                // 代理商结算币种
                agentCurrency2 = aentBean2.getAgentCurrency();
                // 代理商结算汇率
                agentRealSettleRate2 = aentBean2.getAgentSettleRate();
                /*
                 * if(agentCurrency2.equals(ordercurrency)){
                 * agentRealSettleRate2 = 1d; }else{
                 */
                agentRealSettleRate2 = this.getRealSettRateAg(conn, ordercurrency, agentCurrency2, paramBean);
                if (!agentCurrency2.equals(bankcurrency) && !ordercurrency.equals(agentCurrency2)) {
                    // 收单到划款币种的汇率
                    Double rateT2 = this.getRealSettRate2(conn, bankcurrency, agentCurrency2, paramBean);
                    if (rateT2 == null || rateBank == null || rateT2 == 0 || rateBank == 0) {// 说明未找到汇率
                        log.info("未找到收单币种" + bankcurrency + " 到二级代理商划款币种" + agentCurrency2 + "的汇率");
                    } else {
                        // 最终交易币种到划款币种的汇率
                        double rateF2 = BigDPayment.mul(rateT2, rateBank);
                        if (agentRealSettleRate2 > rateF2 || agentRealSettleRate2 == 0) {
                            agentRealSettleRate2 = rateF2;
                        }
                    }
                }
            }

            // 获取三级代理商
            MerchantAentBean aentBean3 = merAentMap.get(3);
            if (null != aentBean3) {
                agentRate += aentBean3.getAentRate();
                
                agentNo3 = aentBean3.getAentNo();
                // 代理商结算币种
                agentCurrency3 = aentBean3.getAgentCurrency();
                // 代理商结算汇率
                agentRealSettleRate3 = aentBean3.getAgentSettleRate();
                /*
                 * if(agentCurrency3.equals(ordercurrency)){
                 * agentRealSettleRate3 = 1d; }else{
                 */
                agentRealSettleRate3 = this.getRealSettRateAg(conn, ordercurrency, agentCurrency3, paramBean);
                if (!agentCurrency3.equals(bankcurrency) && !ordercurrency.equals(agentCurrency3)) {
                    // 收单到划款币种的汇率
                    Double rateT3 = this.getRealSettRate2(conn, bankcurrency, agentCurrency3, paramBean);
                    if (rateT3 == null || rateBank == null || rateT3 == 0 || rateBank == 0) {// 说明未找到汇率
                        log.info("未找到收单币种" + bankcurrency + " 到三级代理商划款币种" + agentCurrency3 + "的汇率");
                    } else {
                        // 最终交易币种到划款币种的汇率
                        double rateF3 = BigDPayment.mul(rateT3, rateBank);
                        if (agentRealSettleRate3 > rateF3 || agentRealSettleRate3 == 0) {
                            agentRealSettleRate3 = rateF3;
                        }
                    }
                }
            }
            log.info(paramBean.getTradeInfoBean().getTradeNo()+"获取绑定代理商信息 agentRate="+agentRate);
        }
        
        // 回佣率合计
        if(paramBean.getAgentRate()==0){
            paramBean.setAgentRate(agentRate);
        }
        agentBean.setAgentNo1(agentNo1);
        agentBean.setAgentNo2(agentNo2);
        agentBean.setAgentNo3(agentNo3);
        agentBean.setAgentCurrency1(agentCurrency1);
        agentBean.setAgentCurrency2(agentCurrency2);
        agentBean.setAgentCurrency3(agentCurrency3);
        agentBean.setAgentRealSettleRate1(agentRealSettleRate1);
        agentBean.setAgentRealSettleRate2(agentRealSettleRate2);
        agentBean.setAgentRealSettleRate3(agentRealSettleRate3);
        
        
        return agentBean;
    }
    
    /**
     * @Title: checkFeeFloat
     * @Description: 验证是否满足费用浮动数方案，并重新计算收单金额
     * <AUTHOR>
     * @date 2019年11月13日 下午7:07:16
     * @param paramBean
     * @param agentBean
     */
    public void checkFeeFloat(ParamBean paramBean, MerAgentBean agentBean){
        Map<String, String> sysBusParamMap = paramBean.getSysBusParamMap();
        String feeIsOpen = "";
        String feeMerno = "";
        String feeMdrrStr = "";
        if(null != sysBusParamMap && sysBusParamMap.size() > 0 ){
            // 参数判断是否启用
            feeIsOpen = ParamCheck.str_null_to_empty(sysBusParamMap.get("fee_is_open"));
            // 判断当前商户是否启用该方案
            feeMerno = ParamCheck.str_null_to_empty(sysBusParamMap.get("fee_merno"));
            // 浮动数判断值
            feeMdrrStr = ParamCheck.str_null_to_empty(sysBusParamMap.get("fee_mdrr"));
        }
        boolean isOpen = false;
        
        double feeMdrr = StringUtils.isNotBlank(feeMdrrStr)? Double.parseDouble(feeMdrrStr):0;
        if(StringUtils.isNotBlank(feeIsOpen) && "1".equals(feeIsOpen) && feeMdrr > 0){
            isOpen = true;
            if(StringUtils.isNotBlank(feeMerno) && !"0".equals(feeMerno)){
                if(feeMerno.contains(paramBean.getInterfaceParamBean().getMerNo())){
                    // 该商户不验证
                    isOpen = false;
                }
            }
        }
        if (isOpen) {
            // 计算差点系数 差点数=商户扣率-代理商回佣率-通道扣率
            double mdrr = new BigDecimal(paramBean.getTradeInfoBean().getBankChannel().getTradeRate()
                    - paramBean.getAgentRate() - paramBean.getBankChaRate()).setScale(4, BigDecimal.ROUND_HALF_UP)
                            .doubleValue();
            double brfloat = feeMdrr - mdrr;
            double rate = 0;
            if (mdrr < feeMdrr) {
                // 新交易汇率1=交易汇率 *（1+手续浮动）
                rate = paramBean.getTradeInfoBean().getRealRate();
                rate = BigDPayment.round((rate * (1 + brfloat)),8);
                paramBean.setFeeFloatRate(brfloat);
                paramBean.getTradeInfoBean().setRealRate(rate);
                paramBean.getTradeInfoBean().setRealAmount(BigDPayment
                        .round(Double.valueOf(paramBean.getInterfaceParamBean().getOrderAmount()) * rate, 2));
            }
            log.info(paramBean.getTradeInfoBean().getTradeNo() + "--费用浮点数验证：mdrr = " + mdrr + "--brfloat=" + brfloat + "--rate=" + rate);
        }
    }
    
    @Override
    public boolean updateTr3DsStatus(String trNo, String tr3dsFlag) {
        // sql语句参数
        List<Object> pars = new ArrayList<Object>();
        String sql = "UPDATE CCPS_TRADERECORD SET TR_3DS_FLAG = ? WHERE TR_NO = ?";
        
        pars.add(tr3dsFlag);
        pars.add(trNo);
        int count = super.executeUpdate(sql, pars.toArray());
        return count > 0; 
    }
    
    /**
     * 重新获取代理商信息
     */
    public Map<Integer, MerchantAentBean> getAgentMap(Connection conn, ParamBean paramBean){

        Map<Integer, MerchantAentBean> merAentMap = new HashMap<Integer, MerchantAentBean>();
        try {
            // sql语句参数
            List<Object> aentPars = new ArrayList<Object>();
            // 根据网关接入号获取代理商信息 - 新的代理商绑定关系信息 多级
            aentPars.add(paramBean.getInterfaceParamBean().getMerNo());
            aentPars.add(paramBean.getInterfaceParamBean().getGatewayNo());
            ResultSet rs = super.executeQuery(InterfaceSql.SQL_GET_MERCHANT_AENT_NEW.toString(), aentPars.toArray());
             
            MerchantAentBean merchantAent = null;
            while (rs.next()) {
                merchantAent = new MerchantAentBean();
                merchantAent.setAentNo(rs.getInt("AM_AGENT_NO"));
                merchantAent.setAentRate(rs.getDouble("AM_RATE"));
                merchantAent.setAentFeeCurreny(rs.getString("AM_FEE_CURRENCY"));
                merchantAent.setAentFeeAmount(rs.getDouble("AM_FEE"));
                merchantAent.setAentFeeFail(rs.getInt("AM_FEE_FAIL"));
                merchantAent.setAentFeeSuccess(rs.getInt("AM_FEE_SUCCESS"));
                merchantAent.setAentFeeSuccessAfter(rs.getInt("AM_FEE_SUCCESS_AFTER"));
                merchantAent.setAentIsBack(rs.getInt("AM_IS_BACK"));
                merchantAent.setAentIsBackAfter(rs.getInt("AM_IS_BACK_AFTER"));

                // 代理商结算币种
                // String agentCurrency =
                // rs.getString("AGENT_ACCOUNT_CURRENCY");
                String agentCurrency = rs.getString("aba_bankcurrency");

                merchantAent.setAgentCurrency(agentCurrency);
                // 代理商级别 1 一级 2 二级 3 三级
                int agentLevel = rs.getInt("AM_AGENT_LEVEL");
                merchantAent.setAgentLevel(agentLevel);
                /*
                 * //获取代理商结算的真实汇率 
                 */
                merAentMap.put(agentLevel, merchantAent);
            }
            paramBean.getTradeInfoBean().setMerAentMap(merAentMap);
        }  catch (Exception e) {
            log.error(InterfaceUtil.getExceptionInfo(e));
        }
        return merAentMap;
    }
    
    @Override
    public boolean updateTr3DsStatus(String trNo, String tr3dsFlag, String tr3dsStatus, String tr3dsInfo, int dmFalg) {
        // sql语句参数
        List<Object> pars = new ArrayList<Object>();
        StringBuffer sql = new StringBuffer("UPDATE CCPS_TRADERECORD SET TR_3DS_PROVIDER=0");
        boolean flag = false;
        if(StringUtils.isNotBlank(tr3dsFlag)){
            flag = true;
            sql.append(", TR_3DS_FLAG = ?,TR_3DS_STATUS=?");
            pars.add(tr3dsFlag);
            pars.add(tr3dsStatus);
            if(StringUtils.isNotBlank(tr3dsInfo)){
                sql.append(", TR_3DS_RETURNINFO = ?");
                pars.add(tr3dsInfo);
            }
        }
        if(dmFalg > 0){
            flag = true;
            sql.append(", TR_DM_FLAG = ?");
            pars.add(dmFalg);
        }
        sql.append(" WHERE TR_NO = ?");
        pars.add(trNo);
        if(flag){
            int count = super.executeUpdate(sql.toString(), pars.toArray());
            return count > 0; 
        }else{
            return true;
        }
    }
}
