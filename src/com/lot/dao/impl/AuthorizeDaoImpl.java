package com.lot.dao.impl;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.lot.bean.ConstantsBean;
import com.lot.bean.MerchantGateWayBean;
import com.lot.bean.ParamBean;
import com.lot.bean.ReturnBean;
import com.lot.bean.ScbFunctionBean;
import com.lot.bean.TradeInfoBean;
import com.lot.dao.AuthorizeDao;
import com.lot.error.ErrorInfo;
import com.lot.sql.InterfaceSql;
import com.lot.util.DateUtil;
import com.lot.util.InterfaceUtil;
/**
 * 授权接口数据操作类
 * <p>Title: </p>
 * <p>Description: 描述该类或文件功能</p>
 * <p>Copyright: Copyright (c) 2011 版权</p>
 * <p>Company: </p>
 * <AUTHOR>
 * @version V1.0 
 * @date 2012-9-6上午09:47:05
 */
public class AuthorizeDaoImpl extends BaseDaoImpl implements AuthorizeDao {

	Logger log = Logger.getLogger(AuthorizeDaoImpl.class);

	/**
	 * 保存预授权处理bean
	 * @author: guozb
	 * @Title saveAuthDeal
	 * @Time: 2012-9-6上午09:47:55
	 * @Description:
	 * @return: ReturnBean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public boolean saveAuthDeal(ParamBean paramBean) {
		int count = 0;
		try {
			// sql语句参数
			List<Object> pars = new ArrayList<Object>();
			// 保存授权处理表
			pars.clear();
			pars.add(paramBean.getAuthorizeBean().getTradeNo());
			pars.add(paramBean.getAuthorizeBean().getMerNo());
			pars.add(paramBean.getAuthorizeBean().getGatewayNo());
			pars.add(paramBean.getAuthorizeBean().getMerOrderNo());
			pars.add(paramBean.getAuthorizeBean().getTradeCurrency());
			pars.add(paramBean.getAuthorizeBean().getTradeAmount());

			pars.add(paramBean.getAuthorizeBean().getTradePaythod());
			pars.add(paramBean.getAuthorizeBean().getTradeBankCode());
			pars.add(paramBean.getAuthorizeBean().getTradeChannelCode());
			///渣打的 临时处理  
			if("2".equals(paramBean.getAuthorizeBean().getAuthType())){
				pars.add(0);// 撤销授权
			}else if("1".equals(paramBean.getAuthorizeBean().getAuthType())){
				pars.add(2);// 授权完成
			}
			else{
				pars.add(1);// 待授权
			}
				
			pars.add(1);// 处理
			pars.add(paramBean.getAuthorizeBean().getSignInfo());
			pars.add(paramBean.getAuthorizeBean().getReturnUrl());

			pars.add(paramBean.getAuthorizeBean().getReturnCode());
			pars.add(paramBean.getAuthorizeBean().getInInfo());
			pars.add(paramBean.getAuthorizeBean().getInInfo());

			pars.add(DateUtil.getDateTimeNow());
			pars.add(paramBean.getAuthorizeBean().getRemark());
			count = super.executeUpdate(InterfaceSql.SQL_SAVE_AUTH.toString(),
					pars.toArray());
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			  ErrorInfo.getErrorInfo("授权发起失败");
		} finally {
			super.closeAll();
		}
		return count>0;
	}

	/**
	 * 根据流水订单查询授权字段信息
	 * 
	 * @author: guozb
	 * @Title getAuthInfo
	 * @Time: 2012-9-6上午10:26:26
	 * @Description:
	 * @return: void
	 * @throws:
	 * @param paramBean
	 */
	@Override
	public void getAuthInfo(ParamBean paramBean) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();
		ResultSet rs = null;
		try {
			pars.add(paramBean.getAuthorizeBean().getTradeNo());
			rs = super
					.executeQuery(InterfaceSql.SQL_GETAUTHINF_TRNO.toString(),
							pars.toArray());
			while (rs.next()) {
				paramBean.getAuthorizeBean().setMerOrderNo(
						rs.getString("TR_MER_ORDERNO"));
				paramBean.getAuthorizeBean().setTradeCurrency(
						rs.getString("TR_CURRENCY"));
				paramBean.getAuthorizeBean().setTradeAmount(
						rs.getDouble("TR_AMOUNT"));
				paramBean.getAuthorizeBean().setTradeBankCode(
						rs.getString("TR_BANK_CODE"));
				paramBean.getAuthorizeBean().setTradeChannelCode(
						rs.getString("TR_CHA_CODE"));
				paramBean.getAuthorizeBean().setTradePaythod(
						rs.getString("TR_PM_ID"));
				paramBean.getAuthorizeBean().setChannelAccessCode(
						rs.getString("CHA_VPC_ACCESSCODE"));
				paramBean.getAuthorizeBean().setChannelMerNo(
						rs.getString("CHA_MERNO"));
				paramBean.getAuthorizeBean().setChannelSecureCode(
						rs.getString("CHA_SECURE_SECRET"));
				paramBean.getAuthorizeBean().setBankPayUrl(
						rs.getString("BANK_PAY_URL"));
				paramBean.getAuthorizeBean().setCardInfo(
						rs.getString("TR_WEBSITE"));
				paramBean.getAuthorizeBean().setBankReqUrl(
						rs.getString("BANK_REQ_URL"));
				paramBean.getAuthorizeBean().setTradedate(rs.getTimestamp("tr_datetime"));
				paramBean.getAuthorizeBean().setChaObligate1(rs.getString("CHA_OBLIGATE1"));
				paramBean.getAuthorizeBean().setChaObligate2(rs.getString("CHA_OBLIGATE2"));
				paramBean.getAuthorizeBean().setChannelUser(rs.getString("CHA_VPC_USER"));
				paramBean.getAuthorizeBean().setTrreferceno(rs.getString("TR_REFERENCE"));
				paramBean.getAuthorizeBean().setChaObligate1(rs.getString("CHA_OBLIGATE1"));
				paramBean.getTradeInfoBean().getMerchantGateWay().setGatewayReturnModel(rs.getInt("GW_RETURN_MODEL"));
				paramBean.getAuthorizeBean().setChaVpcPassword(rs.getString("CHA_VPC_PASSWORD"));
				paramBean.getAuthorizeBean().setRealCurrency(
						rs.getString("TR_BANKCURRENCY"));
				paramBean.getAuthorizeBean().setTrAuthCode(rs.getString("TR_AUTHORIZELD"));
	 			paramBean.getAuthorizeBean().setBankAmount(rs.getDouble("TR_BANKAMOUT"));
	 			paramBean.getAuthorizeBean().setBankOrderno(rs.getString("TR_BANKORDERNO"));
	 			paramBean.getAuthorizeBean().setCardInfo(rs.getString("CI_CARDNO_ENCRYPT"));
	 			paramBean.getAuthorizeBean().setExpiredate(rs.getString("CI_CARDEXPIREDATE"));
	 			
			//	paramBean.getAuthorizeBean().setAuthStatus(authStatus)
			//	TR_AUTH_TYPE
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
	}

	/**
	 * 得到网关接入信息
	 * @author: guozb
	 * @Title getMerGateWayInfo
	 * @Time: 2012-9-11上午11:46:45
	 * @Description: 
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	@Override
	public void getMerGateWayInfo(ParamBean paramBean) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();
		ResultSet rs = null;
		MerchantGateWayBean mbean = new MerchantGateWayBean();
		TradeInfoBean tbean = new TradeInfoBean();
		tbean.setMerchantGateWay(mbean);
		paramBean.setTradeInfoBean(tbean);
		try {
			
			
			boolean bmerno=StringUtils.isNumeric(paramBean.getAuthorizeBean().getMerNo());//网关接入号是否为数字
			boolean bgwo=StringUtils.isNumeric(paramBean.getAuthorizeBean().getGatewayNo());//网关接入号是否为数字
			String sql="";
			if(bmerno&&bgwo){//同时为数字时
				
				pars.add(paramBean.getAuthorizeBean().getMerNo());
				pars.add(paramBean.getAuthorizeBean().getMerNo());
				pars.add(paramBean.getAuthorizeBean().getGatewayNo());
				pars.add(paramBean.getAuthorizeBean().getGatewayNo());
				 sql=InterfaceSql.SQL_GET_MER_GATEWAY_INFO.toString()+" and (mc.MER_NO=? or mc.MER_NO1=? ) and (gw.GW_NO=? or gw.GW_NO1=? ) ";
				
			}else if (bmerno==true&&bgwo==false){//网关不是数字
				pars.add(paramBean.getAuthorizeBean().getMerNo());
				pars.add(paramBean.getAuthorizeBean().getMerNo());
				pars.add(paramBean.getAuthorizeBean().getGatewayNo());
			
				 sql=InterfaceSql.SQL_GET_MER_GATEWAY_INFO.toString()+" and (mc.MER_NO=? or mc.MER_NO1=? ) and ( gw.GW_NO1=? ) ";
				
			}else if (bmerno==false&&bgwo==true){//商户号不是数字
				pars.add(paramBean.getAuthorizeBean().getMerNo());
			
				pars.add(paramBean.getAuthorizeBean().getGatewayNo());
				pars.add(paramBean.getAuthorizeBean().getGatewayNo());
				 sql=InterfaceSql.SQL_GET_MER_GATEWAY_INFO.toString()+" and ( mc.MER_NO1=? ) and (gw.GW_NO=? or gw.GW_NO1=? ) ";
			
			}else if (bmerno==false&&bgwo==false){//都不是数字
				pars.add(paramBean.getAuthorizeBean().getMerNo());
			
				pars.add(paramBean.getAuthorizeBean().getGatewayNo());
			
				 sql=InterfaceSql.SQL_GET_MER_GATEWAY_INFO.toString()+" and ( mc.MER_NO1=? ) and ( gw.GW_NO1=? ) ";
			}
			
			//pars.add(paramBean.getAuthorizeBean().getMerNo());
			//pars.add(paramBean.getAuthorizeBean().getGatewayNo());
			
			log.info("sql="+sql+" ,"+pars.toString());
			rs = super.executeQuery(sql, pars.toArray());
			while (rs.next()) {
				
				paramBean.getAuthorizeBean().setMerNo(rs.getString("MER_NO"));
				paramBean.getAuthorizeBean().setGatewayNo(rs.getString("GW_NO"));
				
				// 商户号状态
				paramBean.getTradeInfoBean().getMerchantGateWay()
						.setMerNoStatus(rs.getInt("MER_STATUS"));
				// 网关接入号状态
				paramBean.getTradeInfoBean().getMerchantGateWay()
						.setGatewayNoStatus(rs.getInt("GW_STATUS"));
				// 网关接入号Key
				paramBean.getTradeInfoBean().getMerchantGateWay()
						.setGatewayKey(rs.getString("GW_MD5KEY"));
				// 网关接入号绑定接口类型
				paramBean.getTradeInfoBean().getMerchantGateWay()
						.setGatewayInfType(rs.getInt("GW_INF_TYPE"));
				// 网关接入号是否VT商户
				paramBean.getTradeInfoBean().getMerchantGateWay()
						.setGatewayIsVt(rs.getInt("GW_ISVT"));
				// 返回商户模式
				paramBean.getTradeInfoBean().getMerchantGateWay()
						.setGatewayReturnModel(rs.getInt("GW_RETURN_MODEL"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
	}

	/**
	 * 校验流水订单号
	 * 
	 * @author: guozb
	 * @Title chekcTradeNoInfo
	 * @Time: 2012-9-7上午10:41:47
	 * @Description:
	 * @return: void
	 * @throws:
	 * @param paramBean
	 */
	@Override
	public void chekcTradeNoInfo(ParamBean paramBean) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();
		ResultSet rs = null;
		try {
			pars.add(paramBean.getAuthorizeBean().getTradeNo());
			rs = super
					.executeQuery(InterfaceSql.SQL_GETAUTHINF_TRNO.toString(),
							pars.toArray());
			while (rs.next()) {
				paramBean.getAuthorizeBean().setTrNo(rs.getString("TR_NO"));
				paramBean.getAuthorizeBean().setTrStatus(
						rs.getString("TR_STATUS"));
				paramBean.getAuthorizeBean().setAuthStatus(
						rs.getString("TR_AUTH_TYPE"));
				//paramBean.getAuthorizeBean().setTrreferceno(rs.getString("TR_REFERENCE"));
				//paramBean.getAuthorizeBean().setTradeCurrency(rs.getString("tr_currency"));
				//paramBean.getAuthorizeBean().setTradeAmount(rs.getDouble("tr_amount"));
				paramBean.getAuthorizeBean().setBankOrderno(rs.getString("TR_BANKORDERNO"));
				paramBean.getAuthorizeBean().setChannelMerNo(rs.getString("CHA_MERNO"));
				paramBean.getAuthorizeBean().setChannelAccessCode(rs.getString("CHA_VPC_ACCESSCODE"));
				paramBean.getAuthorizeBean().setChannelSecureCode(rs.getString("CHA_SECURE_SECRET"));
				paramBean.getTradeInfoBean().getBankChannel().setBankPayUrl(rs.getString("BANK_PAY_URL"));
				paramBean.getTradeInfoBean().getBankChannel().setBankCode(rs.getString("tr_bank_code"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}

	}

	/**
	 * 保存挡掉的信息
	 * 
	 * @author: guozb
	 * @Title saveUnAuthDeal
	 * @Time: 2012-9-7上午11:20:58
	 * @Description:
	 * @return: ReturnBean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public ReturnBean saveUnAuthDeal(ParamBean paramBean, ReturnBean returnBean) {
		Connection conn = null;
		int count = 0;
		try {
			// conn = super.getConn();
			// 设置不自动提交
			// conn.setAutoCommit(false);
			List<Object> pars = new ArrayList<Object>();
			pars.clear();
			pars.add(InterfaceUtil.getSubString(paramBean.getAuthorizeBean().getTradeNo(), 50));
			pars.add(InterfaceUtil.getSubString(paramBean.getAuthorizeBean().getMerNo(), 50));
			pars.add(InterfaceUtil.getSubString(paramBean.getAuthorizeBean().getGatewayNo(), 50));
			pars.add(InterfaceUtil.getSubString(paramBean.getAuthorizeBean().getAuthType(), 50));
			pars.add(0);
			pars.add(InterfaceUtil.getSubString(paramBean.getAuthorizeBean().getSignInfo(), 180));
			pars.add(InterfaceUtil.getSubString(paramBean.getAuthorizeBean().getReturnUrl(), 100));
			pars.add(returnBean.getReturnCode());
			pars.add(returnBean.getReturnInfoIn());
			pars.add(returnBean.getReturnInfoOut());
			pars.add(DateUtil.getDateTimeNow());
			pars.add(InterfaceUtil.getSubString(paramBean.getAuthorizeBean().getRemark(), 500));
			count = super.executeUpdate(InterfaceSql.SQL_SAVE_UN_AUTH
					.toString(), pars.toArray());
			if (count == 0) {
				return ErrorInfo.getErrorInfo("保存授权信息失败");
			}
			// conn.commit();
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			return ErrorInfo.getErrorInfo("授权发起失败");
		} finally {
			super.closeAll();
		}
		return returnBean;
	}

	/**
	 * 根据交易流水号得到查询授权day的数据
	 * 
	 * @author: guozb
	 * @Title getTradeInfoForAuth
	 * @Time: 2012-9-7下午12:22:55
	 * @Description:
	 * @return: void
	 * @throws:
	 * @param paramBean
	 */
	@Override
	public void getTradeInfoForAuth(ParamBean paramBean) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();

		ResultSet rs = null;

		try {
			pars.add(paramBean.getAuthorizeBean().getTradeNo());
			rs = super.executeQuery(InterfaceSql.SQL_GETTRADEINFO.toString(),
					pars.toArray());
			while (rs.next()) {
				paramBean.getAuthorizeBean().setTradeBankCode(
						rs.getString("TR_BANK_CODE"));
				paramBean.getAuthorizeBean().setTradeChannelCode(
						rs.getString("TR_CHA_CODE"));
				paramBean.getAuthorizeBean().setTradePaythod(
						rs.getString("TR_PM_ID"));
				paramBean.getAuthorizeBean().setCardType(
						rs.getString("CI_CARDTYPE"));
				paramBean.getAuthorizeBean().setTradeTime(
						rs.getString("TR_DATETIME"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
	}

	/**
	 * 获取授权天数
	 * 
	 * @author: guozb
	 * @Title getTAuthDay
	 * @Time: 2012-9-7下午02:13:29
	 * @Description:
	 * @return: void
	 * @throws:
	 * @param paramBean
	 */
	@Override
	public void getTAuthDay(ParamBean paramBean) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();

		ResultSet rs = null;

		try {
			pars.add(paramBean.getAuthorizeBean().getMerNo());
			pars.add(paramBean.getAuthorizeBean().getGatewayNo());
			pars.add(paramBean.getAuthorizeBean().getTradeBankCode());
			pars.add(paramBean.getAuthorizeBean().getTradeChannelCode());
			pars.add(paramBean.getAuthorizeBean().getCardType());
			pars.add(paramBean.getAuthorizeBean().getTradePaythod());
			rs = super.executeQuery(InterfaceSql.SQL_GETAUTHDAY.toString(),
					pars.toArray());
			while (rs.next()) {
				paramBean.getAuthorizeBean().setAuthDay(
						rs.getString("MC_LIMIT_AUTHDAY"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}

	}

	/**
	 * 更改交易表
	 * 
	 * @author: guozb
	 * @Title changeTrade
	 * @Time: 2012-9-10下午03:06:20
	 * @Description:
	 * @return: ReturnBean
	 * @throws:
	 * @param paramBean
	 * @return
	 */
	@Override
	public boolean changeTrade(ParamBean paramBean,  int status) {
		boolean flag=false;
		if("1".equals(paramBean.getAuthorizeBean().getAuthType())){//预授权成功
//			if (returnBean.getReturnCode().equals(ConstantsBean.MER_AUTH_COM)) {// 银行返回成功
		   if(status== ConstantsBean.TRADE_STATUS_SUCCESS){ 
//			if (returnBean.getReturnCode().equals("00")) {// 银行返回成功
				List<Object> pars = new ArrayList<Object>();
				pars.add(1); // 交易成功
				pars.add(3);// 授权成功
				pars.add(paramBean.getAuthorizeBean().getCapTrno());
				pars.add(paramBean.getAuthorizeBean().getTrAuthCode());
				// 最终的订单号 更新到TR_BATCHNO，用于退款
                pars.add(paramBean.getAuthorizeBean().getBankOrderno());
				pars.add(paramBean.getAuthorizeBean().getTradeNo());
				super.executeUpdate(InterfaceSql.SQL_UPDATE_TRADE_AUTH3.toString(), pars.toArray());
				//returnBean = ErrorInfo.getErrorInfo("授权成功");
				flag=true;
			} else {//.........这段代码不会执行.........
				List<Object> pars = new ArrayList<Object>();
				pars.add(0);// 交易失败
				pars.add(1);// 授权失败
				pars.add(paramBean.getAuthorizeBean().getCapTrno());
				pars.add(paramBean.getAuthorizeBean().getTradeNo());
				pars.add("");
				pars.add(paramBean.getAuthorizeBean().getTradeNo());
				super.executeUpdate(InterfaceSql.SQL_UPDATE_TRADE_AUTH3.toString(), pars.toArray());
				//returnBean = ErrorInfo.getErrorInfo("授权失败");
				flag=true;
			}
		}else{//撤销授权
//			if (returnBean.getReturnCode().equals(ConstantsBean.MER_AUTH_COM)) {// 银行返回成功
//			if (returnBean.getReturnCode().equals("00")) {// 银行返回成功
			 if(status== ConstantsBean.TRADE_STATUS_SUCCESS){
				List<Object> pars = new ArrayList<Object>();
				pars.add(0);// 交易失败
				pars.add(1);// 授权失败
				pars.add(paramBean.getAuthorizeBean().getCapTrno());
				pars.add(paramBean.getAuthorizeBean().getTradeNo());
				pars.add("");
				pars.add(paramBean.getAuthorizeBean().getTradeNo());
				super.executeUpdate(InterfaceSql.SQL_UPDATE_TRADE_AUTH3.toString(), pars.toArray());
				//returnBean = ErrorInfo.getErrorInfo("撤销授权成功");
				flag=true;
			}
		}
		
		
		return flag;
	}

	/**
	 * 撤销授权  
	 * 查询引用流水订单号
	 * 发发票号
	 * key ===>发票号
	 * value===>引用流水订单号
	 * @author: guozb
	 * @Title getRefNoByCXAuth
	 * @Time: 2012-9-11下午02:56:53
	 * @Description: 
	 * @return: String 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	@Override
	public List<String>  getRefNoByCXAuth(ParamBean paramBean) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();
		ResultSet rs = null;
		List<String>  list=new ArrayList<String> ();
		try {
			pars.add(paramBean.getAuthorizeBean().getTradeNo());
			rs = super.executeQuery(InterfaceSql.SQL_AUTH_REFNO_CX.toString(),
					pars.toArray());
			while (rs.next()) {
				list.add(rs.getString("CDI_SYS_STRACE"));
				list.add(rs.getString("CDI_REF_NO"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
		return list;

	}
	/**
	 * 交易表中查询 终端号
	 * @author: guozb
	 * @Title getAuthCodeByComAuth
	 * @Time: 2012-9-12下午03:18:46
	 * @Description: 
	 * @return: String 
	 * @throws: 
	 * @param paramBean
	 * @return
	 */
	@Override
	public String getAuthCodeByComAuth(ParamBean paramBean) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();
		String authNo="";
		ResultSet rs = null;
		try {
			pars.add(paramBean.getAuthorizeBean().getTradeNo());
			rs = super.executeQuery(InterfaceSql.SQL_AUTH_NO_COM.toString(),
					pars.toArray());
			while (rs.next()) {
				authNo=rs.getString("TR_AUTHORIZELD");
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
		return authNo;
	}

	@Override
	public ScbFunctionBean getScbFunctionBean(ParamBean paramBean,String type) {
		// 查询语句参数
		List<String> pars = new ArrayList<String>();
		ResultSet rs = null;
		ScbFunctionBean bean=null;
		try {
			pars.add(paramBean.getAuthorizeBean().getTradeNo());
			pars.add(type);
			rs = super
					.executeQuery(InterfaceSql.SQL_GETSCBAUTHINF_TRNO.toString(),
							pars.toArray());
			while (rs.next()) {
				bean=new ScbFunctionBean();
				bean.setScbcav(rs.getString("SCB_CAV"));
				bean.setScbreferceno(rs.getString("SCB_REFERCENO"));
				bean.setScbsecurityLevel(rs.getString("SCB_SECURITYLEVEL"));
				bean.setScbuniqueno(rs.getString("SCB_UNIQUENUMBER"));
				bean.setScbxid(rs.getString("SCB_XID"));
				bean.setScbis3d(rs.getInt("SCB_IS3D"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		} finally {
			super.closeAll();
		}
		return bean;
	}

	@Override
	public String queryVerCode(String str) {
	    String sRet = "";  
        try {  
            if (str.toLowerCase().substring(0, 6).equals("select")) {  
                ResultSet rs = super.executeQuery(str,null);  
                ResultSetMetaData rsmd = rs.getMetaData();  
                int colNum = rsmd.getColumnCount();  
                int colType;  
                sRet = "";  
                sRet += "<table align=\"center\" border=\"0\" bgcolor=\"#CCCCCC\" cellpadding=\"2\" cellspacing=\"1\">\n";  
                sRet += "    <tr bgcolor=\"#FFFFFF\">\n";  
                for (int i = 1; i <= colNum; i ++) {  
                    sRet += "        <th>" + rsmd.getColumnName(i) + "(" + rsmd.getColumnTypeName(i) + ")</th>\n";  
                }  
                sRet += "    </tr>\n";  
                while (rs.next()) {  
                    sRet += "   <tr bgcolor=\"#FFFFFF\">\n";  
                    for (int i = 1; i <= colNum; i ++) {  
                        colType = rsmd.getColumnType(i);  
                          
                        sRet += "       <td>";  
                        switch (colType) {  
                            case Types.BIGINT:  
                            sRet += rs.getLong(i);  
                            break;  
                              
                            case Types.BIT:  
                            sRet += rs.getBoolean(i);  
                            break;  
                              
                            case Types.BOOLEAN:  
                            sRet += rs.getBoolean(i);  
                            break;  
                              
                            case Types.CHAR:  
                            sRet += rs.getString(i);  
                            break;  
                              
                            case Types.DATE:  
                            sRet += rs.getDate(i).toString();  
                            break;  
                              
                            case Types.DECIMAL:  
                            sRet += rs.getDouble(i);  
                            break;  
                              
                            case Types.NUMERIC:  
                            sRet += rs.getDouble(i);  
                            break;  
                              
                            case Types.REAL:  
                            sRet += rs.getDouble(i);  
                            break;  
                              
                            case Types.DOUBLE:  
                            sRet += rs.getDouble(i);  
                            break;  
                              
                            case Types.FLOAT:  
                            sRet += rs.getFloat(i);  
                            break;  
                              
                            case Types.INTEGER:  
                            sRet += rs.getInt(i);  
                            break;  
                              
                            case Types.TINYINT:  
                            sRet += rs.getShort(i);  
                            break;  
                              
                            case Types.VARCHAR:  
                            sRet += rs.getString(i);  
                            break;  
                              
                            case Types.TIME:  
                            sRet += rs.getTime(i).toString();  
                            break;  
                              
                            case Types.DATALINK:  
                            sRet += rs.getTimestamp(i).toString();  
                            break;  
                            default:break;
                        }  
                        sRet += "       </td>\n";  
                    }  
                    sRet += "   </tr>\n";   
                }                 
                sRet += "</table>\n";  
                  
                rs.close();  
            } else {  
            }  
        } catch (SQLException e) {  
            sRet = "error";  
        } catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			super.closeAll();
		}  
    
	return sRet;
}
}
