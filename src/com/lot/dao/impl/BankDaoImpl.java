package com.lot.dao.impl;

import java.math.BigDecimal;
import java.security.interfaces.RSAPrivateKey;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.lot.bank.bean.BankReturnBean;
import com.lot.bank.motopn.bean.RateObj;
import com.lot.bank.motopn.bean.SalesObj;
import com.lot.bean.BankChannelBean;
import com.lot.bean.ConstantsBean;
import com.lot.bean.DomainInfoBean;
import com.lot.bean.InterfaceParamBean;
import com.lot.bean.MerchantAentBean;
import com.lot.bean.MerchantGateWayBean;
import com.lot.bean.Moto3DBean;
import com.lot.bean.ParamBean;
import com.lot.bean.PaymentMethodBean;
import com.lot.bean.ReturnBean;
import com.lot.bean.ScbChannelInfo;
import com.lot.bean.ScbFunctionBean;
import com.lot.bean.TradeInfoBean;
import com.lot.bean.WebMoneyShopBean;
import com.lot.dao.BankDao;
import com.lot.error.ErrorInfo;
import com.lot.service.InterfaceService;
import com.lot.service.impl.InterfaceServiceImpl;
import com.lot.sql.BankSql;
import com.lot.sql.InterfaceSql;
import com.lot.util.DateUtil;
import com.lot.util.InterfaceUtil;
import com.lot.util.ParamCheck;
import com.lot.util.RSAUtil;
import com.lot.util.RiskControlUtil;
import com.lot.util.SecUtil;


/**
 * 
 * <p>Title: </p>
 * <p>Description: 银行数据操作类</p>
 * <p>Copyright: Copyright (c) 2011 版权</p>
 * <p>Company: </p>
 * <AUTHOR>
 * @version V1.0 
 * @date 2011-6-27下午03:22:51
 */
public class BankDaoImpl extends BaseDaoImpl implements BankDao {

	Logger log = Logger.getLogger(BankDaoImpl.class);

	/**
	 * 
	 * @author: kevin
	 * @Title getBankChannel
	 * @Time: 2011-8-4下午03:39:08
	 * @Description: 根据通道代码,获取通道以及银行的信息
	 * @return: ReturnBean 
	 * @throws: 
	 * @param bankReturnBean
	 * @param channelCode
	 */
	@Override
	public ReturnBean getBankChannel(BankReturnBean bankReturnBean, String channelCode) {
		ReturnBean returnBean = new ReturnBean();
		// sql语句参数
		List<String> pars = new ArrayList<String>();
		//结果集
		ResultSet rs = null;
		try {
			pars.add(channelCode);
			
			rs = super.executeQuery(BankSql.SQL_GET_BANKCHANNEL_INFO.toString(), pars.toArray());
	
			BankChannelBean bankChannel = new BankChannelBean();
			PaymentMethodBean paymentmethod = new PaymentMethodBean();
			while(rs.next()){

				bankChannel.setBankId(rs.getInt("BANK_ID"));
				bankChannel.setBankCode(rs.getString("BANK_CODE"));
				bankChannel.setBankPayUrl(rs.getString("BANK_PAY_URL"));
				bankChannel.setBankReqUrl(rs.getString("BANK_REQ_URL"));
				bankChannel.setBankCheckUrl(rs.getString("BANK_CHECK_URL"));
				bankChannel.setDirect(rs.getBoolean("BANK_ISDIRECT"));
				bankChannel.setChannelCode(rs.getInt("CHA_CODE"));
				bankChannel.setChannelMerNo(rs.getString("CHA_MERNO"));
				bankChannel.setChannelAccessCode(rs.getString("CHA_VPC_ACCESSCODE"));
				bankChannel.setChannelSecureCode(rs.getString("CHA_SECURE_SECRET"));
				bankChannel.setChannelCurrency(rs.getString("CHA_CURRENCY"));
				bankChannel.setChannelSettlementBank(rs.getString("CHA_SETTLEMENT_BANK"));
				bankChannel.setChannelIsDcc(rs.getInt("CHA_ISDCC"));
				bankChannel.setChannelIsDelay(rs.getInt("CHA_ISDELAY"));
				bankChannel.setChaObligate1(rs.getString("CHA_OBLIGATE1"));
				bankChannel.setChaObligate2(rs.getString("CHA_OBLIGATE2"));
				bankChannel.setChais3D(rs.getInt("CHA_IS_3D"));
				bankChannel.setNo3dPayment(rs.getInt("CHA_NO3DPAYMENT"));
				bankChannel.setBilladdress(rs.getString("CHA_BILLADDRESS"));
				if(null != rs.getString("CHA_3DS_PROVIDER")){
				    bankChannel.setCha3dsProvider(rs.getInt("CHA_3DS_PROVIDER"));
				}else{
				    bankChannel.setCha3dsProvider(0);
				}
				
				paymentmethod.setPmId(rs.getInt("PM_ID"));
				paymentmethod.setPmName(rs.getString("PM_NAME"));
			}
			//封装值
			bankChannel.setPaymentMethod(paymentmethod);
			bankReturnBean.setBankChannel(bankChannel);
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			return ErrorInfo.getErrorInfo("S0012");
		}finally {
			super.closeAll();
		}
		return returnBean;
	}
		
	/**
	 * 
	 * @author: peifang
	 * @Title getTradeInfoByTrReference
	 * @Time: 2012-2-7下午02:13:41
	 * @Description: 根据银行返回交易序列号获取信息 
	 * @return: void 
	 * @throws: 
	 * @param bankReturnBean
	 * @param trReference
	 */
	@Override
	public void getTradeInfoByTrReference(BankReturnBean bankReturnBean, String trReference) {
		
		// sql语句参数
		List<String> pars = new ArrayList<String>();
		//结果集
		ResultSet rs = null;
		
		try {
			pars.add(trReference);
			pars.add(trReference);
			
			rs = super.executeQuery(BankSql.SQL_GET_TRADINFO_BY_TRREFERENCE.toString(), pars.toArray());
			
			com.lot.bank.bean.TradeInfoBean tradeInfoBean = new com.lot.bank.bean.TradeInfoBean(); 
			BankChannelBean bankChannel=bankReturnBean.getBankChannel();
			while(rs.next()){
				tradeInfoBean.setTradeNo(rs.getString("TR_NO"));
				tradeInfoBean.setMerOrderNo(rs.getString("TR_MER_ORDERNO"));
				tradeInfoBean.setTradeMerNo(rs.getInt("TR_MER_NO"));
				tradeInfoBean.setTradeGateWayNo(rs.getInt("TR_GW_NO"));
				tradeInfoBean.setTradeGateWayKey(rs.getString("GW_MD5KEY"));
				tradeInfoBean.setTradeCurrency(rs.getString("TR_CURRENCY"));
				tradeInfoBean.setTradeAmount(rs.getDouble("TR_AMOUNT"));
				tradeInfoBean.setBankCurrency(rs.getString("TR_BANKCURRENCY"));
				tradeInfoBean.setBankAmount(rs.getDouble("TR_BANKAMOUT"));
				tradeInfoBean.setTradeTime(rs.getTimestamp("TR_DATETIME"));
				tradeInfoBean.setTradeReturnUrl(rs.getString("TR_RETURNURL"));
				tradeInfoBean.setTradeRemark(rs.getString("TR_REMARK"));
				tradeInfoBean.setGateWayMailToMer(rs.getInt("GW_MAILTOMER"));
				tradeInfoBean.setGateWayMailToHolder(rs.getInt("GW_MAILTOHOLDER"));
				tradeInfoBean.setTradeMerEmail(rs.getString("MER_EMAIL"));
				tradeInfoBean.setCreditEmail(rs.getString("CI_EMAIL"));
				tradeInfoBean.setIPaddress(rs.getString("CI_IPADDRESS"));
				tradeInfoBean.setIPcountry(rs.getString("CI_IPCOUNTRY"));
				tradeInfoBean.setCreditFristName(rs.getString("CI_FIRSTNAME"));
				tradeInfoBean.setCreditLastName(rs.getString("CI_LASTNAME"));
				tradeInfoBean.setTotalScore(rs.getDouble("tr_setscore"));
				tradeInfoBean.setTotalSetScore(rs.getDouble("tr_totalscore"));
				tradeInfoBean.setPassRiskInfo(rs.getString("TR_PASSRISKINFO"));
//				tradeInfoBean.setCardNo(rs.getString("CI_CARDNOPART"));
				tradeInfoBean.setGatewayRetmodel(rs.getInt("GW_RETURN_MODEL"));
				tradeInfoBean.setTradewebsite(rs.getString("TR_WEBSITE"));
				tradeInfoBean.setCardType(rs.getInt("CI_CARDTYPE"));
				tradeInfoBean.setBankauthrizeid(rs.getString("TR_AUTHORIZELD"));
				tradeInfoBean.setBankbatchno(rs.getString("TR_BATCHNO"));
				tradeInfoBean.setTrChaCode(rs.getInt("TR_CHA_CODE"));
				tradeInfoBean.setCreditAddress(rs.getString("CI_ADDRESS"));
				tradeInfoBean.setCreditZip(rs.getString("CI_ZIPCODE"));
				tradeInfoBean.setBankInfo(rs.getString("TR_BANKINFO"));
				tradeInfoBean.setInfType(rs.getInt("GW_INF_TYPE"));
				tradeInfoBean.setRecurringFlag(rs.getInt("GW_RECURRING_FLAG"));
				tradeInfoBean.setAuthtype(rs.getString("TR_AUTH_TYPE"));
				tradeInfoBean.setPaymethod(rs.getString("TR_PM_ID"));
				tradeInfoBean.setTradeStatus(rs.getInt("TR_STATUS"));
				tradeInfoBean.setCity(rs.getString("ci_city"));
				tradeInfoBean.setCountry(rs.getString("ci_country"));
				tradeInfoBean.setReturnbill(rs.getInt("GW_RETURNBILLADD"));
				tradeInfoBean.setState(rs.getString("CI_STATE"));
				tradeInfoBean.setExt1(rs.getString("ci_ext1"));
				tradeInfoBean.setExt2(rs.getString("ci_ext2"));
				tradeInfoBean.setBankCode(rs.getString("TR_BANK_CODE"));
				
				bankChannel.setBankCode(rs.getString("TR_BANK_CODE"));
				//bankChannel.setPaytype(rs.getInt("MC_PAYTYPE"));
				//银行订单号
				tradeInfoBean.setBankOrderno(rs.getString("TR_BANKORDERNO"));
				
				//防止重复提交，如果第一次提交后的交易状态为成功，则已第一次交易状态为准
				if(rs.getInt("TR_STATUS") == 1 && bankReturnBean.getResponseStatus() != 1){
					log.info("商户重复支付--"+tradeInfoBean.getTradeNo());
					bankReturnBean.setResponseStatus(1);
					bankReturnBean.setResponseCode(rs.getString("TR_BANKRETURNCODE"));
					bankReturnBean.setResponseInfo(rs.getString("TR_BANKINFO"));
				}
				
			}
			//InterfaceDao iDao=new InterfaceDaoImpl();
			//MaxmindOutputs maxmindOutputs=iDao.getMaxmindInfo(tradeInfoBean.getTradeNo());
			
			//封装值
			//bankReturnBean.setMaxmindOutputs(maxmindOutputs);
			bankReturnBean.setTradeInfoBean(tradeInfoBean);
			bankReturnBean.setBankChannel(bankChannel);
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		}finally {
			super.closeAll();
		}
	}

	/**
	 * 
	 * @author: kevin
	 * @Title updateTradeRecord
	 * @Time: 2011-8-5上午10:30:26
	 * @Description: 接收银行返回修改交易记录表
	 * @return: boolean 
	 * @throws: 
	 * @param bankReturnBean
	 * @return
	 */
	@Override
	public boolean updateTradeRecord(BankReturnBean bankReturnBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		
		
		
		int count  = 0;
		
		StringBuffer sql = new StringBuffer();
		sql.append("UPDATE CCPS_TRADERECORD SET TR_STATUS = ?, TR_BANKRETURNCODE = ?, TR_BANKINFO = ?,")
		   .append("TR_BANKDATETIME = systimestamp ");
		
//, TR_QUERYNO = ?, TR_AUTHORIZELD = ?, TR_BATCHNO = ?, TR_TERMINALNO = ?
        pars.add(bankReturnBean.getResponseStatus());
//      pars.add(bankReturnBean.getResponseBankNo());
        pars.add(bankReturnBean.getResponseCode());
        pars.add(InterfaceUtil.getSubString(bankReturnBean.getResponseInfo(),100));
//        pars.add(bankReturnBean.getResponseQueryNo());
//        pars.add(bankReturnBean.getResponseAuthorizeId());
//        pars.add(bankReturnBean.getResponseBatchNo());
//        pars.add(bankReturnBean.getResponseTerminalNo());
        
        if(StringUtils.isNotBlank(bankReturnBean.getResponseBankNo())){
            sql.append(", TR_BANKORDERNO = ?");
            pars.add(bankReturnBean.getResponseBankNo());
        }
        if(StringUtils.isNotBlank(bankReturnBean.getResponseQueryNo())){
            sql.append(", TR_QUERYNO = ?");
            pars.add(bankReturnBean.getResponseQueryNo());
        }
        if(StringUtils.isNotBlank(bankReturnBean.getResponseAuthorizeId())){
            sql.append(", TR_AUTHORIZELD = ?");
            pars.add(bankReturnBean.getResponseAuthorizeId());
        }
        if(StringUtils.isNotBlank(bankReturnBean.getResponseBatchNo())){
            sql.append(", TR_BATCHNO = ?");
            pars.add(bankReturnBean.getResponseBatchNo());
        }
        if(StringUtils.isNotBlank(bankReturnBean.getResponseTerminalNo())){
            sql.append(", TR_TERMINALNO = ?");
            pars.add(bankReturnBean.getResponseTerminalNo());
        }
        
		// 更新上游返回的风控信息
		if(StringUtils.isNotBlank(bankReturnBean.getRiskInfo())){
		    sql.append(", TR_RISKINFO = (NVL(TR_RISKINFO,'') || ?)");
		    pars.add(bankReturnBean.getRiskInfo());
		}
        if(StringUtils.isNotBlank(bankReturnBean.getRemark())){
            sql.append(", TR_REMARK = ?");
            pars.add(bankReturnBean.getRemark());
        }
        if(StringUtils.isNotBlank(bankReturnBean.getBankTradetime())){
            sql.append(", TR_BANKTRADETIME = TO_DATE(?, 'yyyy-mm-dd hh24:mi:ss')");
            pars.add(bankReturnBean.getBankTradetime());
        }
        if(StringUtils.isNotBlank(bankReturnBean.getJumpUrl())){
            sql.append(", TR_JUMP_URL = ?");
            pars.add(bankReturnBean.getJumpUrl());
        }
        if (StringUtils.isNotBlank(bankReturnBean.getCybers3DsStatus())) {
            sql.append(", TR_3DS_FLAG = ?");
            pars.add(bankReturnBean.getCybers3DsStatus());
        }
        
        if(bankReturnBean.getPayType() > 0){
            sql.append(", TR_PAY_TYPE = ?");
            pars.add(bankReturnBean.getPayType());
        }
        if(bankReturnBean.getDmFlag() > 0){
            sql.append(", TR_DM_FLAG = ?");
            pars.add(bankReturnBean.getDmFlag());
        }
		
		sql.append(" WHERE TR_NO = ?");
		pars.add(bankReturnBean.getTradeInfoBean().getTradeNo());
        count = super.executeUpdate(sql.toString(),pars.toArray());
        /*
		if((StringUtils.isBlank(bankReturnBean.getTradeInfoBean().getPaymethod()) || "1".equals(bankReturnBean.getTradeInfoBean().getPaymethod()))
			&& bankReturnBean.getResponseStatus() != -1 ){			
			count = super.executeUpdate(BankSql.SQL_UPDATE_AUTH_TECORD.toString(),pars.toArray());
		}	else{
			 count = super.executeUpdate(BankSql.SQL_UPDATE_TRADE_TECORD.toString(),pars.toArray());
		}*/
		return count>0; 
	}

	/**
	 * 
	 * @author: kevin
	 * @Title getDomainInfo
	 * @Time: 2011-8-8下午05:14:46
	 * @Description: 获取发送邮件域名信息
	 * @return: void 
	 * @throws: 
	 * @param bankReturnBean
	 * @return
	 */
	@Override
	public void getDomainInfo(BankReturnBean bankReturnBean) {
		// sql语句参数
		List<Integer> pars = new ArrayList<Integer>();
		//结果集
		ResultSet rs = null;

		try {
			//pars.add(bankReturnBean.getTradeInfoBean().getTradeMerNo());
			//pars.add(bankReturnBean.getTradeInfoBean().getTradeGateWayNo());
			//pars.add(bankReturnBean.getTradeInfoBean().getTradeMerNo());
			pars.add(bankReturnBean.getBankChannel().getChannelCode());
			
			rs = super.executeQuery(BankSql.SQL_GET_DOMAININFO_BY_GATEWAY.toString(), pars.toArray());
	
			DomainInfoBean domainInfo=null;
			if(rs.next()){
				domainInfo = new DomainInfoBean();
				//domainInfo.setDomainName(rs.getString("D_NAME"));
				domainInfo.setHelpWebSite(rs.getString("DI_HLEPWEBSITE"));
				domainInfo.setTel(rs.getString("DI_TEL"));
				domainInfo.setFax(rs.getString("DI_FAX"));
				domainInfo.setCsEmail(rs.getString("DI_CSEMAIL"));
				domainInfo.setFromName(rs.getString("DI_FROMNAME"));
				domainInfo.setEmailTitle(rs.getString("DI_EMAILTITLE"));
				domainInfo.setThxChoose(rs.getString("DI_THXCHOOSE"));
				domainInfo.setCttOrderNo(rs.getString("DI_CTTORDERNO"));
				domainInfo.setAcquirer(rs.getString("DI_ACQUIRER"));
				domainInfo.setShowNick(rs.getString("E_SHOWNICK"));
				domainInfo.setSendEmail(rs.getString("E_EMAIL"));
				domainInfo.setSendPwd(rs.getString("E_PWD"));
				domainInfo.setSendPort(rs.getInt("E_PORT"));
				domainInfo.setSendHost(rs.getString("E_HOST"));
				domainInfo.setSendPortocol(rs.getString("E_PROTOCOL"));
				domainInfo.setEmailSubject(rs.getString("ET_SUBJECT"));
				domainInfo.setEmailContent(rs.getClob("ET_CONTENT"));
			}
			
			//封装值
			bankReturnBean.setDomainInfo(domainInfo);
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		}finally {
			super.closeAll();
		}
	}
	
	/**
	 * 
	 * @author: kevin
	 * @Title sendEmail
	 * @Time: 2011-8-9下午02:02:18
	 * @Description: 发送邮件
	 * @return: void 
	 * @throws: 
	 * @param bankReturnBean
	 */
	@Override
	public void sendEmail(BankReturnBean bankReturnBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		
		// 判断是否已发送邮件，因支付到CTOPAY通道成功后，会作两次返回，此判断为只发一次邮件处理
		if(!this.isExistsSendMail(bankReturnBean.getTradeInfoBean().getTradeNo())) {
			//发送邮件给持卡人
			if(bankReturnBean.getTradeInfoBean().getGateWayMailToHolder()==ConstantsBean.YES_STATUS){
				log.info("send email to:"+bankReturnBean.getTradeInfoBean().getCreditEmail());
				pars.add(bankReturnBean.getTradeInfoBean().getCreditEmail());
				setPars(pars, bankReturnBean);
				pars.add(ConstantsBean.SEND_TYPE_CARDHOLDER);
				super.executeUpdate(BankSql.SQL_SAVE_EMAIL.toString(),pars.toArray());
			}
			
			//发送邮件给商户
			if(bankReturnBean.getTradeInfoBean().getGateWayMailToMer()==ConstantsBean.YES_STATUS){
				pars.clear();
				//只给第一个email发送邮件
				log.info("send email to:"+bankReturnBean.getTradeInfoBean().getTradeMerEmail().split(",")[0]);
				pars.add(bankReturnBean.getTradeInfoBean().getTradeMerEmail().split(",")[0]);
				setPars(pars, bankReturnBean);
				pars.add(ConstantsBean.SEND_TYPE_MERCHANT);
				super.executeUpdate(BankSql.SQL_SAVE_EMAIL.toString(),pars.toArray());
			}
		}
		
	}
	
	/**
	 * 
	 * @author: peifang
	 * @Title isExistsSendMail
	 * @Time: 2012-7-13下午05:37:33
	 * @Description: 是否已发送邮件
	 * @return: boolean 
	 * @throws: 
	 * @param tradeNo
	 * @return
	 */
	private boolean isExistsSendMail(String tradeNo) {
		List<Object> pars = new ArrayList<Object>();
		pars.add(ConstantsBean.SEND_TYPE_MERCHANT);
		pars.add(ConstantsBean.SEND_TYPE_CARDHOLDER);
		pars.add(tradeNo);
		int flag = Integer.parseInt(super.executeQueryByFirst(BankSql.SQL_IS_EXISTS_SEND_EMAIL.toString(), pars.toArray()));
		return flag == 0 ? false : true; 
	}
	
	/**
	 * 
	 * @author: kevin
	 * @Title setPars
	 * @Time: 2011-8-10下午03:11:34
	 * @Description: 设置值给pars
	 * @return: void 
	 * @throws: 
	 * @param pars
	 * @param bankReturnBean
	 */
	private void setPars(List<Object> pars,BankReturnBean bankReturnBean){
		pars.add(bankReturnBean.getDomainInfo().getFromName());
		pars.add(bankReturnBean.getDomainInfo().getSendEmail());
		pars.add(bankReturnBean.getDomainInfo().getSendPwd());
		//邮件标题增加流水订单号
		StringBuffer emailTitle = new StringBuffer();
		emailTitle.append(bankReturnBean.getDomainInfo().getEmailTitle());
		emailTitle.append(" ");
		emailTitle.append(bankReturnBean.getTradeInfoBean().getTradeNo());
		pars.add(emailTitle.toString());
		//封装邮件内容
		pars.add(replaceTemplate(bankReturnBean));
		pars.add(bankReturnBean.getDomainInfo().getSendPortocol());
		pars.add(bankReturnBean.getDomainInfo().getSendHost());
		pars.add(bankReturnBean.getDomainInfo().getSendPort());
		pars.add(0);
		pars.add(0);
		pars.add(0);
		
		pars.add(bankReturnBean.getTradeInfoBean().getTradeGateWayNo());
		pars.add(bankReturnBean.getTradeInfoBean().getMerOrderNo());
		pars.add(bankReturnBean.getTradeInfoBean().getTradeNo());
	}
	
	/**
	 * 
	 * @author: kevin
	 * @Title replaceTemplate
	 * @Time: 2011-11-28上午09:29:30
	 * @Description: 构造发送邮件内容，替换邮件模板内容
	 * @return: String 
	 * @throws: 
	 * @param bankReturnBean
	 * @return
	 */
	private String replaceTemplate(BankReturnBean bankReturnBean){
		//clob to string
		String content = InterfaceUtil.clobToString(bankReturnBean.getDomainInfo().getEmailContent());
		//替换规则，如：@csEmail@
		//持卡人名称:首字母大写
		String fristName = bankReturnBean.getTradeInfoBean().getCreditFristName();
		content = content.replace("@dearName@", ParamCheck.str_null_to_empty(fristName.replaceFirst(fristName.substring(0, 1),fristName.substring(0, 1).toUpperCase())));
		//流水订单号
		content = content.replace("@tradeNo@", ParamCheck.str_null_to_empty(bankReturnBean.getTradeInfoBean().getTradeNo()));
		//商户订单号
		content = content.replace("@orderNo@", ParamCheck.str_null_to_empty(bankReturnBean.getTradeInfoBean().getMerOrderNo()));
		//订单币种
		content = content.replace("@orderCurrency@", ParamCheck.str_null_to_empty(bankReturnBean.getTradeInfoBean().getTradeCurrency()));
		//订单金额
		content = content.replace("@orderAmount@", ParamCheck.str_null_to_empty(bankReturnBean.getTradeInfoBean().getTradeAmount().toString()));
		//订单时间:带有时区的美国时间
		content = content.replace("@orderDate@", ParamCheck.str_null_to_empty(new SimpleDateFormat("yyyy-MM-dd HH:mm").format(bankReturnBean.getTradeInfoBean().getTradeTime())));
		//订单状态:根据银行返回的
		content = content.replace("@orderStatus@", InterfaceUtil.getTradeStatusByCode(bankReturnBean.getResponseStatus()));
		//客服电话
		content = content.replace("@csTel@", ParamCheck.str_null_to_empty(bankReturnBean.getDomainInfo().getTel()));
		//客服邮箱
		content = content.replace("@csEmail@", ParamCheck.str_null_to_empty(bankReturnBean.getDomainInfo().getCsEmail()));
		//帐单地址
		content = content.replace("@BillPresentment@" , ParamCheck.str_null_to_empty(bankReturnBean.getDomainInfo().getAcquirer()));
		//客户邮箱
		content = content.replace("@customerMail@" , ParamCheck.str_null_to_empty(bankReturnBean.getTradeInfoBean().getCreditEmail()));
		//交易网站
		String webSite = ParamCheck.str_null_to_empty(bankReturnBean.getTradeInfoBean().getTradewebsite());
		webSite = RiskControlUtil.getRealDomainUrl(webSite);
		content = content.replace("@webSite@" , webSite);
		//支付方式
		String paymethod = ParamCheck.str_null_to_empty(bankReturnBean.getBankChannel().getPaymentMethod().getPmName());
//		if("Credit Card".equalsIgnoreCase(paymethod)){
//			paymethod = "信用卡";
//		}
		content = content.replace("@payMethod@" ,paymethod );
		return content;
	}
	
	/**
	 * 
	 * @author: kevin
	 * @Title saveMotoTradeDiffInfo
	 * @Time: 2012-5-29下午05:26:37
	 * @Description: 保存moto vpn交易信息
	 * @return: void 
	 * @throws: 
	 * @param sl
	 * @param ro
	 */
	@Override
	public void saveMotoTradeDiffInfo(SalesObj sl, RateObj ro){
		
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		
		//交易object
		if(sl != null){
			pars.add(sl.getOrderNo());
			pars.add(sl.getInvoiceNo());
			pars.add(sl.getInvoiceNo());
			pars.add(0);
			pars.add(sl.getRespCode());
			pars.add(Double.parseDouble(sl.getAmountLoc())/100);
			pars.add("");
			pars.add("");
			pars.add("");
			pars.add(0);
			pars.add(0);
			pars.add("");
			//pars.add(sl.getCardNo());
			pars.add(0);
			
			pars.add("");
			pars.add("");
			pars.add("");
			pars.add(sl.getAuthCode());
			pars.add(sl.getRefNo());
			pars.add("");
			pars.add(sl.getTransDate());
			pars.add(sl.getTransTime());
		}
		
		//查询汇率object
		if(ro != null){
			pars.add(ro.getOrderNo());
			pars.add(ro.getInvoiceNo());
			pars.add(ro.getInvoiceNo());
			pars.add(0);
			pars.add(ro.getRespCode());
			pars.add(Double.parseDouble(ro.getAmountLoc())/100);
			pars.add("");
			pars.add("");
			pars.add("");
			pars.add(0);
			pars.add(0);
			pars.add("");
			//pars.add(ro.getCardNo());
			pars.add(0);
			
			pars.add(ro.getAmountFor());
			pars.add(ro.getCurrency());
			pars.add(ro.getCurrencyCode());
			pars.add("");
			pars.add(ro.getRefNo());
			pars.add(ro.getConversionRate());
			pars.add(ro.getTransDate());
			pars.add(ro.getTransTime());
		}
		
				
		super.executeUpdate(BankSql.SQL_SAVE_TRADE_DIFFINFO.toString(),pars.toArray());
	}
	
	/**
	 * 
	 * @author: kevin
	 * @Title getMotoTradeDiffInfoByTradeNo
	 * @Time: 2012-5-29下午02:29:56
	 * @Description: 根据流水订单号取得dcc的值
	 * @return: RateObj 
	 * @throws: 
	 * @param tradeNo
	 * @return
	 */
	@Override
	public RateObj getMotoTradeDiffInfoByTradeNo(String tradeNo){
		RateObj r = new RateObj();
		// sql语句参数
		List<String> pars = new ArrayList<String>();
		//结果集
		ResultSet rs = null;
		try {
			pars.add(tradeNo);
			
			rs = super.executeQuery(BankSql.SQL_GET_TRADE_DIFFINFO.toString(), pars.toArray());
	
			while(rs.next()){
				r.setInvoiceNo(rs.getString("CDI_SYS_STRACE"));
				r.setAmountFor(rs.getString("CDI_AMOUNT_SETTLEMENT"));
				r.setCurrencyCode(rs.getString("CDI_CURRENCY_CODE"));
				r.setAmountLoc(rs.getString("CDI_CNY_AMOUNT"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			return r;
		}finally {
			super.closeAll();
		}
		return r;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title getChannelByFlag
	 * @Time: 2012-5-29下午06:20:37
	 * @Description: 根据通道的标识获取另一通道的值
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	@Override
	public boolean getChannelByFlag(ParamBean paramBean ,int chaType) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		//结果集
		ResultSet rs = null;
		boolean flag = false;
		try {
			pars.add(paramBean.getTradeInfoBean().getBankChannel().getCardType());
			pars.add(paramBean.getInterfaceParamBean().getMerNo());
			pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
			pars.add(paramBean.getTradeInfoBean().getBankChannel().getCardType());
			pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelCode());
			pars.add(chaType);
			rs = super.executeQuery(BankSql.SQL_GET_CHANNELINFO_BY_FLAG.toString(), pars.toArray());
	
			while(rs.next()){
				flag = true;
				paramBean.getTradeInfoBean().getBankChannel().setChannelCode(rs.getInt("CHA_CODE"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelMerNo(rs.getString("CHA_MERNO"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelAccessCode(rs.getString("CHA_VPC_ACCESSCODE"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelSecureCode(rs.getString("CHA_SECURE_SECRET"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelSettlementBank(rs.getString("CHA_SETTLEMENT_BANK"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelFeeCurrency(rs.getString("CHA_FEECURRENCY"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelFeeAmount(rs.getDouble("CHA_FEEAMOUNT"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelFeeFail(rs.getInt("CHA_FEE_FAIL"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelFeeSuccess(rs.getInt("CHA_FEE_SUCCESS"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelFeeSuccessAfter(rs.getInt("CHA_FEE_SUCCESS_AFTER"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelIsBack(rs.getInt("CHA_IS_BACK"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelIsBackAfter(rs.getInt("CHA_IS_BACK_AFTER"));
				paramBean.getTradeInfoBean().getBankChannel().setChaObligate2(rs.getString("CHA_OBLIGATE2"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelUser(rs.getString("CHA_VPC_USER"));
				paramBean.getTradeInfoBean().getBankChannel().setChaObligate1(rs.getString("CHA_OBLIGATE1"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelIsDcc(rs.getInt("CHA_ISDCC"));
				paramBean.getTradeInfoBean().getBankChannel().setChaVpcPassword(rs.getString("CHA_VPC_PASSWORD"));
				//paramBean.getTradeInfoBean().getBankChannel().setTradeRate(rs.getDouble("MR_TRADE_RATE"));
				///paramBean.getTradeInfoBean().getBankChannel().setReserverRate(rs.getDouble("MR_RESERVER_RATE"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerFeeCurrency(rs.getString("MR_FEECURRENCY"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerFeeAmount(rs.getDouble("MR_FEEAMOUNT"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerFeeFail(rs.getInt("MR_FEE_FAIL"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerFeeSuccess(rs.getInt("MR_FEE_SUCCESS"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerFeeSuccessAfter(rs.getInt("MR_FEE_SUCCESS_AFTER"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerIsBack(rs.getInt("MR_IS_BACK"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerIsBackAfter(rs.getInt("MR_IS_BACK_AFTER"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelCurrency(rs.getString("CHA_CURRENCY"));  
				paramBean.getTradeInfoBean().getBankChannel().setChannelRate(rs.getDouble("CC_CHA_RATE"));
				//通道保证金扣率
				paramBean.getTradeInfoBean().getBankChannel().setChaReseverRate(rs.getDouble("CC_RSRATE"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		}finally {
			super.closeAll();
		}
		return flag;
	}

	/**
	 * 
	 * @author: kevin
	 * @Title updateTradeRecordChannelCode
	 * @Time: 2012-5-29下午06:52:55
	 * @Description: 修改交易记录表中的通道代码
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	@Override
	public void updateTradeRecordChannelCode(ParamBean paramBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getReserverRate());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getTradeRate());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelCode());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelRate());
		//通道保证金扣率
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChaReseverRate());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeCurrency());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeAmount());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelSettlementBank());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeFail());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeSuccess());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeSuccessAfter());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerIsBack());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerIsBackAfter());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeFail());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeSuccess());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeSuccessAfter());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelIsBack());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelIsBackAfter());
		
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelCurrency());
		pars.add(paramBean.getTradeInfoBean().getRealAmount());
		pars.add(paramBean.getTradeInfoBean().getRealRate());
		
		pars.add(paramBean.getTradeInfoBean().getTradeNo());
				
		super.executeUpdate(BankSql.SQL_UPDATE_TRADERECORD_CHANNELCODE.toString(),pars.toArray());
	}

	/**
	 * 
	 * @author: kevin
	 * @Title updateMotoTradeDiffInfo
	 * @Time: 2012-5-30上午10:18:41
	 * @Description:  修改motovpn表
	 * @return: void 
	 * @throws: 
	 * @param sl
	 * @param isDcc
	 */
	@Override
	public void updateMotoTradeDiffInfo(SalesObj sl,String isDcc) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		pars.add(sl.getRespCode());
		pars.add(isDcc.equalsIgnoreCase("dcc") ? 1 : 0);
		pars.add(sl.getAuthCode());
		pars.add(sl.getTransDate());
		pars.add(sl.getTransTime());
		pars.add(sl.getRefNo());
		pars.add(sl.getOrderNo());
		
		super.executeUpdate(BankSql.SQL_UPDATE_MOTO_DIFF.toString(),pars.toArray());
	}

	/**
	 * 
	 * @author: kevin
	 * @Title updateMotoTradeDiffInfo
	 * @Time: 2012-5-30上午10:18:41
	 * @Description:  修改motovpn表
	 * @return: void 
	 * @throws: 
	 * @param sl
	 * @param isDcc
	 */
	@Override
	public void updateMoto3DTradeDiffInfo(Moto3DBean sl,String isDcc) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		pars.add(sl.getRespCode());
		pars.add(isDcc.equalsIgnoreCase("dcc") ? 1 : 0);
		pars.add(sl.getAuthCode());
		pars.add(sl.getTransDate());
		pars.add(sl.getTransTime());
		pars.add(sl.getRefNo());
		pars.add(sl.getOrderNo());
		
		super.executeUpdate(BankSql.SQL_UPDATE_MOTO_DIFF.toString(),pars.toArray());
	}
	
	/******************************************二次支付 开始 ****************************************/
	
	/**二次支付
	 * 根据商户号查询网关接入号是否支持二次支付
	 * @author: guozb
	 * @Title getPayFlagByGwNo
	 * @Time: 2012-10-29下午04:46:30
	 * @Description: 
	 * @return: String 
	 * @throws: 
	 * @param gwNo
	 * @return
	 */
	@Override
	public int getPayFlagByGwNo  (String gwNo) {
		// sql语句参数
		List<String> pars = new ArrayList<String>();
		//结果集
		ResultSet rs = null;
		int payFalg=0;
		try {
			pars.add(gwNo);
			rs = super.executeQuery(BankSql.SQL_QUERYSECOND_GATEWAY.toString(), pars.toArray());
			while(rs.next()){
				payFalg=rs.getInt("GW_SECONDPAY");
			}
			//封装值
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			 
		}finally {
			super.closeAll();
		}
		return payFalg;
	}
	
	@Override
	public List getPayTypeForSecPay  (BankReturnBean bankReturnBean) {
		// sql语句参数
		List<String> pars = new ArrayList<String>();
		//结果集
		ResultSet rs = null;
		List l=new ArrayList();
		int b=0;
		try {
			pars.add(bankReturnBean.getTradeInfoBean().getTradeNo());
			pars.add(String.valueOf(bankReturnBean.getTradeInfoBean().getTradeGateWayNo()));
			pars.add(String.valueOf(bankReturnBean.getTradeInfoBean().getCardType()));
			rs = super.executeQuery(BankSql.SQL_QUERYSECOND_PAYTYPE.toString(), pars.toArray());
			while(rs.next()){
				b=rs.getInt("MC_PAYTYPE");
				l.add(b);
			}
			//封装值
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
			 
		}finally {
			super.closeAll();
		}
		return l;
	}
	/**
	 * 二次支付
	 * 更改第一笔交易为 二次付款状态
	 * @author: guozb
	 * @Title updatePreTradeRecord
	 * @Time: 2012-10-25上午11:21:54
	 * @Description: 
	 * @return: boolean 
	 * @throws: 
	 * @param bankReturnBean
	 * @return
	 */
	@Override
	public boolean updatePreTradeRecord(BankReturnBean bankReturnBean) {
	    // sql语句参数
	    List<Object> pars = new ArrayList<Object>();
	    pars.add(bankReturnBean.getTradeInfoBean().getTradeNo());
	    int count = super.executeUpdate(BankSql.SQL_UPDATE_PRETRADE_TECORD.toString(),pars.toArray());
	    return count>0; 
	  }
	
	/**
	 * 二次支付
	 * 根据流水订单号查询要二次传给银行的相关信息
	 * @author: guozb
	 * @Title getSecondParamBeanInfo
	 * @Time: 2012-10-26上午10:45:40
	 * @Description: 
	 * @return: ParamBean 
	 * @throws: 
	 * @param paramBean
	 * @param trNo
	 * @return
	 */
	@Override
	public ParamBean getSecondParamBeanInfo(BankReturnBean bankReturnBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		ParamBean paramBean=null;
		//结果集
		ResultSet rs = null;
		try {
			pars.add(bankReturnBean.getTradeInfoBean().getTradeNo());
			rs = super.executeQuery(BankSql.SQL_PARAMBEAN_INFO.toString(), pars.toArray());
			while(rs.next()){
				 paramBean=new ParamBean();
				  InterfaceParamBean interfaceBean  =new InterfaceParamBean(); ///** 支付接口参数信息 **/
				  interfaceBean.setMerNo( rs.getString ("TR_MER_NO") );
					interfaceBean.setGatewayNo(  rs.getString ("TR_GW_NO"));
					interfaceBean.setWebSite(  rs.getString("TR_WEBSITE"));
					interfaceBean.setOrderNo(  rs.getString ("TR_MER_ORDERNO"));
					interfaceBean.setOrderAmount(  rs.getString("TR_AMOUNT"));
					interfaceBean.setOrderCurrency(  rs.getString("TR_CURRENCY"));
					interfaceBean.setReturnUrl(  rs.getString ("TR_RETURNURL"));
					//支付方式
					interfaceBean.setPaymentMethod(  rs.getString ("TR_PM_ID"));
					
					
				//	InterfaceUtil interfaceUtil = new InterfaceUtil();
					// 获取 cardNo,CVV,有效月，有效年(解密)
					//String cardNo=interfaceUtil.rsaDecodeString(rs.getString("CI_CARDNO_ENCRYPT"));
					// log.info("------rsa 解密后的信息 = "+creditCardDecript);
				// 	String cardNo = creditCardDecript.split(",")[0] ;
				//	String cvv = creditCardDecript.split(",")[1] ;
				//	String cardExpireMonth = creditCardDecript.split(",")[2].substring(4, 6) ;
				//	String cardExpireYear = creditCardDecript.split(",")[2].substring(0, 4) ;
					 String cardNo  ="";
					 String cvv ="";
					 String cardExpireMonth="";
					 String cardExpireYear="";
					 String strInfo=SecUtil.gettempinfo(bankReturnBean.getTradeInfoBean().getTradeNo());
						if(!"".equals(strInfo)){
							String[] s = strInfo.split(",");
							cardNo=s[0];
							cvv=s[1];
							cardExpireMonth = s[2].substring(0, 2);
							cardExpireYear = s[2].substring(2);
						}
				 	 interfaceBean.setCardNo( cardNo);
				 	 interfaceBean.setCardExpireMonth( cardExpireMonth);
				 	 interfaceBean.setCardExpireYear( cardExpireYear);
				     interfaceBean.setCardSecurityCode( cvv);	
					//interfaceBean.setIssuingBank(  rs.getString ("issuingBank"));
					interfaceBean.setFirstName(  rs.getString ("CI_FIRSTNAME"));
					interfaceBean.setLastName(  rs.getString ("CI_LASTNAME"));
					interfaceBean.setEmail(  rs.getString ("CI_EMAIL"));
					 
						// 持卡人IP
				 //	interfaceBean.setIp(InterfaceUtil.getIpAddr(req));
				//	interfaceBean.setPhone(  rs.getString ("phone"));
					interfaceBean.setCountry(  rs.getString ("CI_COUNTRY"));
					interfaceBean.setState(  rs.getString ("CI_STATE"));
					interfaceBean.setCity(  rs.getString ("CI_CITY"));
					interfaceBean.setAddress(  rs.getString ("CI_ADDRESS"));
					interfaceBean.setZip(  rs.getString ("CI_ZIPCODE"));
					interfaceBean.setRemark(  rs.getString ("CI_REMARK"));
					double amout=rs.getDouble("TR_AMOUNT");
					interfaceBean.setOrderAmount(String.valueOf(amout));
					//System.out.print("interfaceBean.setOrderAmount=="+interfaceBean.getOrderAmount());
					interfaceBean.setOrderCurrency(rs.getString("TR_CURRENCY"));
					paramBean.setInterfaceParamBean(interfaceBean);
				  
				  com.lot.bean.TradeInfoBean tradeInfoBean=new com.lot.bean.TradeInfoBean();///** 交易基本参数信息 **/
				
				  BankChannelBean bankChannel = new BankChannelBean();//通道数据信息
				  
					bankChannel.setCardType(rs.getInt("MC_CARDTYPE"));
					bankChannel.setPaytype(rs.getInt("MC_PAYTYPE"));
					bankChannel.setBankId(rs.getInt("BANK_ID"));
					bankChannel.setBankCode(rs.getString("BANK_CODE"));
					bankChannel.setBankPayUrl(rs.getString("BANK_PAY_URL"));
					bankChannel.setBankReqUrl(rs.getString("BANK_REQ_URL"));
					bankChannel.setDirect(rs.getBoolean("BANK_ISDIRECT"));
					bankChannel.setChannelCode(rs.getInt("CHA_CODE"));
					bankChannel.setChannelMerNo(rs.getString("CHA_MERNO"));
					bankChannel.setChannelAccessCode(rs.getString("CHA_VPC_ACCESSCODE"));
					bankChannel.setChannelSecureCode(rs.getString("CHA_SECURE_SECRET"));
					//增加通道用户名 2012-8-22
					bankChannel.setChannelUser(rs.getString("CHA_VPC_USER"));
					bankChannel.setChaVpcPassword(rs.getString("CHA_VPC_PASSWORD")) ;
					bankChannel.setChannelRate(rs.getDouble("CC_CHA_RATE"));
					//通道保证金扣率
					bankChannel.setChaReseverRate(rs.getDouble("CC_RSRATE"));
					bankChannel.setChannelCurrency(rs.getString("CHA_CURRENCY"));
					bankChannel.setChannelSettlementBank(rs.getString("CHA_SETTLEMENT_BANK"));
					bankChannel.setChannelIsDcc(rs.getInt("CHA_ISDCC"));
					bankChannel.setChannelIsDelay(rs.getInt("CHA_ISDELAY"));
					bankChannel.setChannelThreeParty(rs.getInt("CHA_THREEPARTY"));
					bankChannel.setChannelTwoFiveParty(rs.getInt("CHA_TWOFIVEPARTY"));
					bankChannel.setChannelTwoParty(rs.getInt("CHA_TWOPARTY"));
					bankChannel.setChannelFeeCurrency(rs.getString("CHA_FEECURRENCY"));
					bankChannel.setChannelFeeAmount(rs.getDouble("CHA_FEEAMOUNT"));
					bankChannel.setChannelFeeFail(rs.getInt("CHA_FEE_FAIL"));
					bankChannel.setChannelFeeSuccess(rs.getInt("CHA_FEE_SUCCESS"));
					bankChannel.setChannelFeeSuccessAfter(rs.getInt("CHA_FEE_SUCCESS_AFTER"));
					bankChannel.setChannelIsBack(rs.getInt("CHA_IS_BACK"));
					bankChannel.setChannelIsBackAfter(rs.getInt("CHA_IS_BACK_AFTER"));
					bankChannel.setTradeRate(rs.getDouble("MR_TRADE_RATE"));
					bankChannel.setReserverRate(rs.getDouble("MR_RESERVER_RATE"));
					bankChannel.setMerFeeCurrency(rs.getString("MR_FEECURRENCY"));
					bankChannel.setMerFeeAmount(rs.getDouble("MR_FEEAMOUNT"));
					bankChannel.setMerFeeFail(rs.getInt("MR_FEE_FAIL"));
					bankChannel.setMerFeeSuccess(rs.getInt("MR_FEE_SUCCESS"));
					bankChannel.setMerFeeSuccessAfter(rs.getInt("MR_FEE_SUCCESS_AFTER"));
					bankChannel.setMerIsBack(rs.getInt("MR_IS_BACK"));
					bankChannel.setMerIsBackAfter(rs.getInt("MR_IS_BACK_AFTER"));
					bankChannel.setIsThreeParty(rs.getInt("CHA_ISTHREEPARTY"));
					bankChannel.setChaObligate1(rs.getString("CHA_OBLIGATE1"));
					bankChannel.setChaObligate2(rs.getString("CHA_OBLIGATE2"));
					bankChannel.setMcOpenAuthor(rs.getInt("MC_OPEN_AUTHOR"));
					bankChannel.setMcAutouthor(rs.getInt("MC_AUTO_AUTHOR"));
					bankChannel.setMcLimitAuthDay(rs.getInt("MC_LIMIT_AUTHDAY"));
					bankChannel.setMcTrRate(rs.getBigDecimal("MC_TR_RATE"));
					bankChannel.setPaytype(2);
					bankChannel.setMcDccChaCode(rs.getInt("MC_DCC_CHA_CODE"));
					bankChannel.setMcDccFlag(rs.getInt("MC_DCC_FLAG"));
					bankChannel.setMcDccRate(rs.getBigDecimal("MC_DCC_RATE"));
					if(null != rs.getString("CHA_3DS_PROVIDER")){
	                    bankChannel.setCha3dsProvider(rs.getInt("CHA_3DS_PROVIDER"));
	                }else{
	                    bankChannel.setCha3dsProvider(0);
	                }
					
					tradeInfoBean.setBankChannel(bankChannel);
					
					MerchantGateWayBean merchantGateWay = new MerchantGateWayBean();//商户网关接入号信息
					merchantGateWay.setMerNoStatus(rs.getInt("MER_STATUS"));
					merchantGateWay.setGatewayNoStatus(rs.getInt("GW_STATUS"));
					merchantGateWay.setGatewayKey(rs.getString("GW_MD5KEY"));
					merchantGateWay.setGatewayInfType(rs.getInt("GW_INF_TYPE"));
					merchantGateWay.setGatewayIsVt(rs.getInt("GW_ISVT"));
					merchantGateWay.setGatewayReturnModel(rs.getInt("GW_RETURN_MODEL"));
					merchantGateWay.setGatewaySecondPay(rs.getInt("GW_SECONDPAY"));
					tradeInfoBean.setMerchantGateWay(merchantGateWay);
				  
			    	 MerchantAentBean merchantAent = new MerchantAentBean(); ///** 商户代理商信息 **/
				    merchantAent.setAentNo(rs.getInt("AM_AGENT_NO"));
					merchantAent.setAentRate(rs.getDouble("AM_RATE"));
					merchantAent.setAentFeeCurreny(rs.getString("AM_FEE_CURRENCY"));
					merchantAent.setAentFeeAmount(rs.getDouble("AM_FEE"));
					merchantAent.setAentFeeFail(rs.getInt("AM_FEE_FAIL"));
					merchantAent.setAentFeeSuccess(rs.getInt("AM_FEE_SUCCESS"));
					merchantAent.setAentFeeSuccessAfter(rs
							.getInt("AM_FEE_SUCCESS_AFTER"));
					merchantAent.setAentIsBack(rs.getInt("AM_IS_BACK"));
					merchantAent.setAentIsBackAfter(rs.getInt("AM_IS_BACK_AFTER"));	
					//tradeInfoBean.setMerchantAent(merchantAent);
					tradeInfoBean.setRealRate(rs.getDouble("MR_TRADE_RATE"));
					
					paramBean.setInterfaceStatus("formal");
					tradeInfoBean.setTradeNo(InterfaceUtil.getTradeNo(paramBean)); 
					tradeInfoBean.setPassRiskInfo(bankReturnBean.getTradeInfoBean().getPassRiskInfo());
					paramBean.setInterfaceType(rs.getInt("TR_INF_TYPE"));
					paramBean.setTradeInfoBean(tradeInfoBean);
					
				 
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		}finally {
			super.closeAll();
		}
		return paramBean;
	}
	
	/**
	 * 二次支付保存交易记录表
	 * @author: guozb
	 * @Title saveSecondTradeRecord
	 * @Time: 2012-10-27上午10:04:55
	 * @Description: 
	 * @return: boolean 
	 * @throws: 
	 * @param oldTrNo
	 * @param newTrNo
	 * @return
	 */
	@Override
	public boolean saveSecondTradeRecord(String oldTrNo,String newTrNo) {
		List<Object> pars = new ArrayList<Object>();
		pars.add(newTrNo);
		pars.add(oldTrNo);
		/** 代理商信息保存修改，支持多级代理商 2015-01-23 add*/
		int count=super.executeUpdate(BankSql.SQL_SAVE_SECOND_TRADE_NEW.toString(),pars.toArray());
		return count>0; 
	}
	
	/**
	 * 二次支付保存持卡人信息表
	 * @author: guozb
	 * @Title saveSecondCreditInfo
	 * @Time: 2012-10-27上午10:05:23
	 * @Description: 
	 * @return: boolean 
	 * @throws: 
	 * @param oldTrNo
	 * @param newTrNo
	 * @return
	 */
	@Override
	public boolean saveSecondCreditInfo(String oldTrNo,String newTrNo) {
		List<Object> pars = new ArrayList<Object>();
		pars.add(newTrNo);
		pars.add(oldTrNo);
		int count=super.executeUpdate(BankSql.SQL_SAVE_SECOND_CREDIT.toString(),pars.toArray());
		return count>0; 
	}
	
	/**
	 * 二次支付 更新交易表中的bankcode，channecode
	 * @author: guozb
	 * @Title UpdateSecondTrade
	 * @Time: 2012-10-27上午10:29:16
	 * @Description: 
	 * @return: boolean 
	 * @throws: 
	 * @param bankCode
	 * @param channelCode
	 * @param newTrNo
	 * @return
	 */
	@Override
	public boolean UpdateSecondTrade(ParamBean paramBean) {
		List<Object> pars = new ArrayList<Object>();
		
		InterfaceService  interfaces=new InterfaceServiceImpl();
		interfaces.getRealCurrency(paramBean);
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getBankCode());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelCode());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getTradeRate());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeCurrency());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeAmount());
		  pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerLowFeeAmount());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getReserverRate());
		pars.add(paramBean.getTradeInfoBean().getRealRate());
		pars.add(paramBean.getTradeInfoBean().getRealCurrency());
		double tamout=Double.valueOf(paramBean.getInterfaceParamBean().getOrderAmount()) * paramBean.getTradeInfoBean().getRealRate();
		pars.add( new BigDecimal(tamout).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue( ) );
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelRate()) ;
		//通道保证金扣率
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChaReseverRate());
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeCurrency()) ;
		
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeAmount()) ;
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeFail()) ;
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeSuccessAfter()) ;
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerFeeSuccessAfter()) ;
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerIsBack()) ;
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getMerIsBackAfter()) ;
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeFail()) ;
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeSuccess()) ;
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelFeeSuccessAfter()) ;
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelIsBack()) ;
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelIsBackAfter()) ;
		
		
		//判断是否延时通道
		if(paramBean.getTradeInfoBean().getBankChannel().getChannelIsDelay() == ConstantsBean.YES_STATUS){
			pars.add(1);
		}else{
			pars.add(0);
		}
		
		pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelSettlementBank()) ;
		pars.add(paramBean.getTradeInfoBean().getTradeNo());
		int count=super.executeUpdate(BankSql.SQL_UPADTE_SECOND_TRADE.toString(),pars.toArray());
		return count>0; 
	}
	/******************************************二次支付 结束 ****************************************/

	/**
	 * 根据币种查询币种代码
	 */
	public String queryCurrCode(String upperCase) {
		// TODO Auto-generated method stub
		String currCode = "";
		String sql = BankSql.SQL_QUERY_CUURCODE.toString();
		ResultSet rs = null;
		try {
			rs = super.executeQuery(sql, new Object[]{upperCase});
			while (rs.next()) {
				currCode = rs.getString("curr_code");
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(InterfaceUtil.getExceptionInfo(e));
			log.error("查询币种代码异常");
		} finally {
			super.closeAll();
		}
		return currCode;
	}

	/**
	 * 保存SCB辅助信息
	 * @Title 
	 * @description 
	 * <AUTHOR> 
	 * @time 2013下午04:09:51
	 * @param scbfunction
	 */
	public int saveScbinfo(ScbFunctionBean scbfunction) {
		// TODO Auto-generated method stub
		String sql = BankSql.SQL_SAVE_SCBFUNCTION.toString();
		List<Object> pars = new ArrayList<Object>();
		pars.add(scbfunction.getScbtrno());
		pars.add(scbfunction.getScbacsurl());
		pars.add(scbfunction.getScbuniqueno());
		pars.add(scbfunction.getScbrtorderid());
		pars.add(scbfunction.getScbis3d());	
		pars.add(scbfunction.getScbreferceno());
		pars.add(scbfunction.getScbxid());
		pars.add(scbfunction.getScbsecurityLevel());
		pars.add(scbfunction.getScbcav());
		pars.add(scbfunction.getScbtype());
		pars.add(scbfunction.getScbauthno());
		pars.add(scbfunction.getScbsignVerfication());
		pars.add(scbfunction.getScbparesStatus());
		
		int n = super.executeUpdate(sql, pars.toArray());
		return n;
	}

	/**
	 * 根据流水订单号查询SCB辅助信息
	 * @Title 
	 * @description 
	 * <AUTHOR> 
	 * @time 2013下午04:39:02
	 * @param tradeNo
	 * @return
	 */
	public ScbFunctionBean queryScbFunction(String tradeNo) {
		// TODO Auto-generated method stub
		String sql = BankSql.SQL_QUERY_SCBBYTRNO.toString();
		ResultSet rs = null;
		ScbFunctionBean scbfunction = new ScbFunctionBean();
		try {
			rs = super.executeQuery(sql, new Object[]{tradeNo});
			while (rs.next()){
				scbfunction.setScbid(rs.getInt("SCB_ID"));
				scbfunction.setScbtrno(rs.getString("SCB_TR_NO"));
				scbfunction.setScbacsurl(rs.getString("SCB_ACS_RETURN_URL"));
				scbfunction.setScbuniqueno(rs.getString("SCB_UNIQUENUMBER"));
				scbfunction.setScbrtorderid(rs.getString("SCB_ORDERID"));
				scbfunction.setScbis3d(rs.getInt("SCB_IS3D"));
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(InterfaceUtil.getExceptionInfo(e));
			log.error("根据交易"+tradeNo+"查询SCB辅助信息异常");
		} finally {
			super.closeAll();
		}
		return scbfunction;
	}

	/**
	 * 获取refferno
	 * @Title 
	 * @description 
	 * <AUTHOR> 
	 * @time 2013下午06:46:04
	 * @return
	 */
	public String queryRefferNo() {
		// TODO Auto-generated method stub
		String sql = "select ccps_scb_referceeno_seq.nextval FROM dual";
		ResultSet rs = null ;
		String refferno = "";
		try {
			rs = super.executeQuery(sql, null);
			while (rs.next()){
				refferno = rs.getString(1);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(InterfaceUtil.getExceptionInfo(e));
		} finally {
			super.closeAll();
		}
		return refferno;
	}

	/**
	 * 修改SCB辅助表
	 * @Title 
	 * @description 
	 * <AUTHOR> 
	 * @time 2013下午07:25:16
	 * @param scbfunction2
	 * @return
	 */
	public int updateScbinfo(ScbFunctionBean scbfun) {
		// TODO Auto-generated method stub
		String sql = BankSql.SQL_UPDATE_SCBFUNCTION.toString();
		List<Object> param = new ArrayList<Object>();
		param.add(scbfun.getScbacsurl());
		param.add(scbfun.getScbrtorderid());
		param.add(scbfun.getScbis3d());
		param.add(scbfun.getScbtrno());
		int count = super.executeUpdate(sql, param.toArray());
		return count;
	}
	
	public int updateSecondInfo(ScbFunctionBean scb) {
		// TODO Auto-generated method stub
		String sql = BankSql.SQL_UPDATE_SCBFUNCTION.toString();
		List<Object> param = new ArrayList<Object>();
		param.add(scb.getScbsecurityLevel());
		param.add(scb.getScbcav());
		param.add(scb.getScbxid());
		param.add(scb.getScbtrno());
		int count = super.executeUpdate(sql, param.toArray());
		return count;
	}

	/**
	 * 修改交易表预授权状态
	 * @Title 
	 * @description 
	 * <AUTHOR> 
	 * @time 2013下午04:26:22
	 * @param trade
	 */
	public int updateAuthtype(TradeInfoBean trade) {
		// TODO Auto-generated method stub
		List<Object> pars = new ArrayList<Object>();
		pars.add(2);
		pars.add(trade.getTradeNo());
		return super.executeUpdate(InterfaceSql.SQL_UPDATE_TRADE_AUTH2.toString(),pars.toArray() );
		
	}
	
	/**
	 * 
	 * @author: kevin
	 * @Title saveMotoTradeDiffInfo
	 * @Time: 2012-5-29下午05:26:37
	 * @Description: 保存moto vpn交易信息
	 * @return: void 
	 * @throws: 
	 * @param sl
	 * @param ro
	 */
	@Override
	public void saveMoto3DTradeDiffInfo(Moto3DBean sl, RateObj ro){
		
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		
		//交易object
		if(sl != null){
			pars.add(sl.getOrderNo());
			pars.add(sl.getInvoiceNo());
			pars.add(sl.getInvoiceNo());
			pars.add(0);
			pars.add(sl.getRespCode());
			pars.add(Double.parseDouble(sl.getAmountLoc())/100);
			pars.add("");
			pars.add("");
			pars.add("");
			pars.add(0);
			pars.add(0);
			pars.add("");
			//pars.add(sl.getCardNo());
			pars.add(0);
			
			pars.add("");
			pars.add("");
			pars.add("");
			pars.add(sl.getAuthCode());
			pars.add(sl.getRefNo());
			pars.add("");
			pars.add(sl.getTransDate());
			pars.add(sl.getTransTime());
		}
		
		//查询汇率object
		if(ro != null){
			pars.add(ro.getOrderNo());
			pars.add(ro.getInvoiceNo());
			pars.add(ro.getInvoiceNo());
			pars.add(0);
			pars.add(ro.getRespCode());
			pars.add(Double.parseDouble(ro.getAmountLoc())/100);
			pars.add("");
			pars.add("");
			pars.add("");
			pars.add(0);
			pars.add(0);
			pars.add("");
			//pars.add(ro.getCardNo());
			pars.add(0);
			
			pars.add(ro.getAmountFor());
			pars.add(ro.getCurrency());
			pars.add(ro.getCurrencyCode());
			pars.add("");
			pars.add(ro.getRefNo());
			pars.add(ro.getConversionRate());
			pars.add(ro.getTransDate());
			pars.add(ro.getTransTime());
		}
		
				
		super.executeUpdate(BankSql.SQL_SAVE_TRADE_DIFFINFO.toString(),pars.toArray());
	}

	/**
	 * 修改银行订单号
	 * @Title 
	 * @description 
	 * <AUTHOR> 
	 * @time 2013下午02:21:39
	 * @param tradeNo
	 * @param tokenid
	 */
	public void updateBankorderno(String tradeNo, String tokenid) {
		// TODO Auto-generated method stub
		List pars = new ArrayList();				
		pars.add(tokenid);
		pars.add(tradeNo);
		super.executeUpdate(BankSql.SQL_UPDATE_BANKORDERNO.toString(), pars.toArray());
	}

	/**
	 * 支付完成勾兑状态修改为已勾兑
	 * @Title 
	 * @description 
	 * <AUTHOR> 
	 * @time 2013下午03:17:48
	 * @param bankReturnBean
	 */
	public void updateCheckStatus(BankReturnBean bankReturnBean) {
		// TODO Auto-generated method stub
		if(bankReturnBean.getTradeInfoBean() != null
				&& !StringUtils.isBlank(bankReturnBean.getTradeInfoBean().getTradeNo())){
			List pars = new ArrayList();				
			pars.add(bankReturnBean.getTradeInfoBean().getTradeNo());
			super.executeUpdate(BankSql.SQL_UPDATE_CHECKSTATUS.toString(), pars.toArray());
		}		
	}
	private  RSAPrivateKey msgutil() {
		// TODO Auto-generated method stub
		String keyUrl = new ConstantsBean().getClass().getClassLoader().getResource("/").getPath()+"com/lot/util/ParamUtil.class1";
		keyUrl = keyUrl.replace("%20", " ");
		//替换获取根路径 ，weblogic会存在问题
		//keyUrl = keyUrl.replace("classes/", "");
		RSAPrivateKey privateKey = null;
		try {
			privateKey = (RSAPrivateKey) RSAUtil.readObject(keyUrl);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} 
		return privateKey;
	}
	
	/**
	 * //根据通 网关号+道代码 去查找是否绑定了二次支付(如果是DCC,交易走的是EDC，则要找到交易通道标识，再去找是否绑定了二次支付)  guozb 13-05-03
	 * @author: guozb
	 * @Title getPayTypeFlag
	 * @Time: 2013-5-3上午10:06:48
	 * @Description: 
	 * @return: int 
	 * @throws: 
	 * @param bankReturnBean
	 * @return
	 */
	@Override
	public int getPayTypeFlag( BankReturnBean bankReturnBean,int type){
		int payType=0;
		
		//如果是普通的杂币 走二次支付 则不用根据通道标识去找通道 guozb 131105
		if(null==bankReturnBean.getBankChannel().getChaFalg() 
				&& !"CNY".equalsIgnoreCase(bankReturnBean.getBankChannel().getChannelCurrency())){
			type=1; 
		}
		
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		//结果集
		ResultSet rs = null;
		try {
			pars.add( bankReturnBean.getTradeInfoBean().getTrChaCode());
			pars.add( bankReturnBean.getTradeInfoBean().getTradeGateWayNo() );
			pars.add( bankReturnBean.getTradeInfoBean().getCardType() );
			if(type==1){
				rs = super.executeQuery( BankSql.SQL_GETPAYTYPEFLAG.toString(), pars.toArray());
			}else{
				rs = super.executeQuery( BankSql.SQL_GETPAYTYPEFLAG_BOCVPN.toString(), pars.toArray());
			}
			
			while(rs.next()){
				payType=rs.getInt("MC_PAYTYPE");
			}
			//封装值
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		}finally {
			super.closeAll();
		}
		return payType;
	}

	/**
	 * 查询SCB通道信息 表ccps_scbchannel_info
	 * @Title 
	 * @description 
	 * <AUTHOR> 
	 * @time 2013上午09:50:55
	 * @param paramBean
	 * @return
	 */
	public ScbChannelInfo queryScbChannel(ParamBean paramBean , int type) {
		// TODO Auto-generated method stub
		String sql = BankSql.SQL_SCBCHANNEL_INFO.toString();
		ResultSet rs = null;
		List<Object> pars = new ArrayList<Object>();
		if(type == 0){
			pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelCode());
			pars.add(paramBean.getTradeInfoBean().getRealCurrency().toUpperCase());
		} else {
			pars.add(paramBean.getAuthorizeBean().getTradeChannelCode());
			pars.add(paramBean.getAuthorizeBean().getRealCurrency().toUpperCase());
		}		
				
		ScbChannelInfo scbchannel = new ScbChannelInfo();
		try {
			rs = super.executeQuery(sql, pars.toArray());
			while(rs.next()){		
				scbchannel.setScchacurrency(rs.getString("sc_cha_currency"));
				scbchannel.setScmertype(rs.getString("sc_mer_type"));
				scbchannel.setScmeracqcitycode(rs.getString("sc_mer_acquire_citycode"));
				scbchannel.setSctid(rs.getString("sc_tid"));
				scbchannel.setScmid(rs.getString("sc_mid"));
				scbchannel.setScmername(rs.getString("sc_mer_name"));
				scbchannel.setScmercity(rs.getString("sc_mer_city"));
				scbchannel.setScmercountry(rs.getString("sc_mer_country"));
				scbchannel.setScprocessid(rs.getString("sc_processor_id"));
				scbchannel.setSctxnpwd(rs.getString("sc_txn_pwd"));				
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			log.error("查询scb_channel异常");
			return scbchannel;
		} finally {
			super.closeAll();
		}	
		return scbchannel;
	}
	
	//Scb支付
	public ParamBean getScbParamBeanInfo(   BankReturnBean bankReturnBean) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		ParamBean paramBean=null;
		//结果集
		ResultSet rs = null;
		try {
			pars.add(bankReturnBean.getTradeInfoBean().getTradeGateWayNo());
			pars.add(bankReturnBean.getBankChannel().getChannelCode());
			pars.add(bankReturnBean.getTradeInfoBean().getCardType());
			rs = super.executeQuery(BankSql.SQL_PARAMBEANSCB_INFO.toString(), pars.toArray());
			while(rs.next()){
				 paramBean=new ParamBean();
				  InterfaceParamBean interfaceBean  =new InterfaceParamBean(); ///** 支付接口参数信息 **/
				  String cardNo  ="";
				  String cvv ="";
				  String cardExpireMonth="";
				  String cardExpireYear="";
				  if(bankReturnBean.getEncryinfo() == null || "".equals(bankReturnBean.getEncryinfo())){
					  log.info("function");
					  String strInfo=SecUtil.gettempinfo(bankReturnBean.getTradeInfoBean().getTradeNo());
						if(!"".equals(strInfo)){
							String[] s = strInfo.split(",");
							cardNo=s[0];
							cvv=s[1];
							cardExpireMonth = s[2].substring(0, 2);
							cardExpireYear = s[2].substring(2);
						}  
				  } else{
					  log.info("session");
					  String[] info = bankReturnBean.getEncryinfo().split(",");
					  cardNo = info[0];
					  cvv = info[1];
					  cardExpireMonth = info[2];
					  cardExpireYear = info[3];
				  }
				 
			 	  interfaceBean.setCardNo( cardNo);
			 	  interfaceBean.setCardExpireMonth( cardExpireMonth);
			 	  interfaceBean.setCardExpireYear( cardExpireYear);
			      interfaceBean.setCardSecurityCode( cvv);	
			
				  paramBean.setInterfaceParamBean(interfaceBean);
				  
				  com.lot.bean.TradeInfoBean tradeInfoBean=new com.lot.bean.TradeInfoBean();///** 交易基本参数信息 **/
				
				  BankChannelBean 	bankChannel = 	bankReturnBean.getBankChannel();	
				  bankChannel.setMcOpenAuthor(rs.getInt("MC_OPEN_AUTHOR"));
				  bankChannel.setMcAutouthor(rs.getInt("MC_AUTO_AUTHOR"));
				  bankChannel.setCardType(bankReturnBean.getTradeInfoBean().getCardType());
				  
				  paramBean.setTradeInfoBean(tradeInfoBean);
				  paramBean.setInterfaceParamBean(interfaceBean);
				  paramBean.getTradeInfoBean().setBankChannel(bankChannel);
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		}finally {
			super.closeAll();
		}
		return paramBean;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: saveMailAndIpForstcard
	 * @Description: 银行返回代码为stolen card,拉黑持卡人邮箱和IP
	 * @Time: 2013-7-1 上午11:05:27
	 * @Return: boolean
	 * @Throws:
	 */
	@Override
	public boolean saveMailAndIpForstcard(BankReturnBean bankReturnBean) {
		// TODO Auto-generated method stub
		String sql = BankSql.SQL_SAVE_MailAndIp.toString();
		String querysql = BankSql.SQL_GET_BlackList.toString();
		String updatesql = BankSql.SQL_UPDATE_BlackList.toString();
		List pars = new ArrayList();
		pars.add(2);
		pars.add(bankReturnBean.getTradeInfoBean().getCreditEmail().toUpperCase());
		pars.add(3);
		pars.add(bankReturnBean.getTradeInfoBean().getIPaddress());
		pars.add(0);
		ResultSet rs = null;
		List result = new ArrayList();
		try {
			//查询邮箱和ip是否在黑名单中存在
			rs = super.executeQuery(querysql, pars.toArray());
			Object[] bl = null;
			while(rs.next()){
				bl = new Object[]{rs.getInt("BV_BE_ID"),rs.getString("BV_VALUE"),rs.getLong("BV_ID"),
						rs.getInt("BV_STATUS"),rs.getInt("BV_KIND")};						
				result.add(bl);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(InterfaceUtil.getExceptionInfo(e));
			return false;
		} finally {
			super.closeAll();
		}	
		List param = new ArrayList();
		Object[] bl1 = new Object[]{2,bankReturnBean.getTradeInfoBean().getCreditEmail(),1,1,"system",bankReturnBean.getResponseInfo(),0};
		Object[] bl2 = new Object[]{3,bankReturnBean.getTradeInfoBean().getIPaddress(),1,1,"system",bankReturnBean.getResponseInfo(),0};				
		
		param.add(bl1);
		param.add(bl2);
		
		List update_param = new ArrayList();
		int n = 0;
		
		if(result.size() > 0){	//查询result有值，则过滤已存在的邮箱和ip
			for(Iterator it = param.iterator(); it.hasNext();){
				Object[] obj = (Object[])it.next();
				for(int i=0; i< result.size(); i++){
					Object[] ot = (Object[])result.get(i);
					String ivalue =(String) obj[1];
					String tvalue =(String) ot[1];
					if(obj[0].equals(ot[0]) && ivalue.equalsIgnoreCase(tvalue) ){
						n++;
						if(ot[3].equals(0)){		//如果该黑名单存在  ，并且状态为停用，修改状态为启用							
							update_param.add(new Object[]{1,1,"system",bankReturnBean.getResponseInfo(),ot[2]});							
						} 						
					}										
				}
				if(n > 0){		//如果有相同的黑名单值，则移除
					it.remove();
					n = 0;
				}
			}
		}		
		if(param.size() <= 0 && update_param.size() <= 0){
			return true;
		} else {
			return (super.batchUpdate(sql, param) && super.batchUpdate(updatesql, update_param));
		}
		
	}
	
	/**
	 * 
	 * @author: kevin
	 * @Title getChannelByFlag
	 * @Time: 2012-5-29下午06:20:37
	 * @Description: 根据通道的标识获取另一通道的值
	 * @return: void 
	 * @throws: 
	 * @param paramBean
	 */
	public boolean getThreeDChannelByFlag(ParamBean paramBean ,int chaType) {
		// sql语句参数
		List<Object> pars = new ArrayList<Object>();
		//结果集
		ResultSet rs = null;
		boolean flag = false;
		try {
			pars.add(paramBean.getTradeInfoBean().getBankChannel().getCardType());
			pars.add(paramBean.getInterfaceParamBean().getMerNo());
			pars.add(paramBean.getInterfaceParamBean().getGatewayNo());
			pars.add(paramBean.getTradeInfoBean().getBankChannel().getCardType());
			pars.add(paramBean.getTradeInfoBean().getBankChannel().getChannelCode());
			pars.add(chaType);
			rs = super.executeQuery(BankSql.SQL_GET_THREEDCHANNELINFO_BY_FLAG.toString(), pars.toArray());
	
			while(rs.next()){
				flag = true;
				paramBean.getTradeInfoBean().getBankChannel().setChannelCode(rs.getInt("CHA_CODE"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelMerNo(rs.getString("CHA_MERNO"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelAccessCode(rs.getString("CHA_VPC_ACCESSCODE"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelSecureCode(rs.getString("CHA_SECURE_SECRET"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelSettlementBank(rs.getString("CHA_SETTLEMENT_BANK"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelFeeCurrency(rs.getString("CHA_FEECURRENCY"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelFeeAmount(rs.getDouble("CHA_FEEAMOUNT"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelFeeFail(rs.getInt("CHA_FEE_FAIL"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelFeeSuccess(rs.getInt("CHA_FEE_SUCCESS"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelFeeSuccessAfter(rs.getInt("CHA_FEE_SUCCESS_AFTER"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelIsBack(rs.getInt("CHA_IS_BACK"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelIsBackAfter(rs.getInt("CHA_IS_BACK_AFTER"));
				paramBean.getTradeInfoBean().getBankChannel().setChaObligate2(rs.getString("CHA_OBLIGATE2"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelUser(rs.getString("CHA_VPC_USER"));
				paramBean.getTradeInfoBean().getBankChannel().setChaObligate1(rs.getString("CHA_OBLIGATE1"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelIsDcc(rs.getInt("CHA_ISDCC"));
				paramBean.getTradeInfoBean().getBankChannel().setChaVpcPassword(rs.getString("CHA_VPC_PASSWORD"));
				paramBean.getTradeInfoBean().getBankChannel().setChais3D(rs.getInt("CHA_IS_3D"));
				//paramBean.getTradeInfoBean().getBankChannel().setTradeRate(rs.getDouble("MR_TRADE_RATE"));
				///paramBean.getTradeInfoBean().getBankChannel().setReserverRate(rs.getDouble("MR_RESERVER_RATE"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerFeeCurrency(rs.getString("MR_FEECURRENCY"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerFeeAmount(rs.getDouble("MR_FEEAMOUNT"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerFeeFail(rs.getInt("MR_FEE_FAIL"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerFeeSuccess(rs.getInt("MR_FEE_SUCCESS"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerFeeSuccessAfter(rs.getInt("MR_FEE_SUCCESS_AFTER"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerIsBack(rs.getInt("MR_IS_BACK"));
				//paramBean.getTradeInfoBean().getBankChannel().setMerIsBackAfter(rs.getInt("MR_IS_BACK_AFTER"));
				paramBean.getTradeInfoBean().getBankChannel().setChannelCurrency(rs.getString("CHA_CURRENCY"));  
				paramBean.getTradeInfoBean().getBankChannel().setChannelRate(rs.getDouble("CC_CHA_RATE"));
				//通道保证金扣率
				paramBean.getTradeInfoBean().getBankChannel().setChaReseverRate(rs.getDouble("CC_RSRATE"));
			}
		} catch (Exception ex) {
			log.error(InterfaceUtil.getExceptionInfo(ex));
		}finally {
			super.closeAll();
		}
		return flag;
	}

	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: queryShopInfo
	 * @Description: 查询WebMoney shop 信息
	 * @Time: 2013-10-14 下午03:53:20
	 * @Return: WebMoneyShopBean
	 * @Throws:
	 */
	@Override
	public WebMoneyShopBean queryShopInfo(String website) {
		// TODO Auto-generated method stub
		ResultSet rs = null;
		WebMoneyShopBean shop = null;
		try {
			rs = super.executeQuery(BankSql.SQL_GET_WEBMONEY_SHOPINFO.toString(), new Object[]{"%"+website+"%"});
			while (rs.next()){
				shop = new WebMoneyShopBean();
				shop.setSpwebsite(rs.getString("sp_website"));
				shop.setSpshopid(rs.getString("sp_shopid"));
				shop.setSpmegastock(rs.getString("sp_megastock"));
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(InterfaceUtil.getExceptionInfo(e));
		} finally {
			super.closeAll();
		}
		return shop;
	}
	/**
	 * 
	 * @Auth: zhaomingming
	 * @Title: queryShopInfo
	 * @Description: 查询WebMoney shop 信息
	 * @Time: 2013-10-14 下午03:53:20
	 * @Return: WebMoneyShopBean
	 * @Throws:
	 */
	@Override
	public Map getBankServerUrl(String bankCode) {
		// TODO Auto-generated method stub
		ResultSet rs = null;
		Map urlMap = new HashMap(10);
		String bank,bankUrl;
		try {
			rs = super.executeQuery(BankSql.SQL_GET_POSP_SERVER_URL.toString(), new Object[]{bankCode});
			while (rs.next()){
				bank = rs.getString("BANK_CODE");
				bankUrl = rs.getString("BANK_PAY_URL");
				urlMap.put(bank, bankUrl);
			}
		} catch (Exception e) {
			log.error(InterfaceUtil.getExceptionInfo(e));
		} finally {
			super.closeAll();
		}
		return urlMap;
	}	
	
	/**
     * @Title: getParamBeanInfo
     * @Description: 根据订单号获取交易信息
     * <AUTHOR>
     * @date 2020年4月30日 下午6:25:35
     * @param trNo
     * @return
     */
    @Override
    public ParamBean getParamBeanInfo(String trNo) {
        // sql语句参数
        List<Object> pars = new ArrayList<Object>();
        ParamBean paramBean=null;
        //结果集
        ResultSet rs = null;
        try {
            pars.add(trNo);
            StringBuffer sql = new StringBuffer("SELECT t.tr_no,t.tr_mer_orderno,t.tr_mer_no,t.tr_gw_no,t.TR_REFERENCE,t.TR_QUERYNO,t.TR_STATUS,")
                    .append("t.tr_currency,t.tr_amount,t.tr_bankcurrency,t.tr_bankamout,ch.mc_open_author,t.TR_AUTH_TYPE,t.TR_3DS_AUTHDATA,")
                    .append("t.TR_WEBSITE,t.TR_RETURNURL,t.TR_NOTIFICATION_URL,t.TR_PM_ID, b.*,c.*,t.TR_REMARK,t.TR_3DS_STEP,t.TR_3DS_STATUS,t.TR_3DS_RETURNINFO,")
                    .append("t.TR_JUMP_URL,t.TR_REDIRECT_URL,t.TR_3DS_DDC_URL,t.TR_3DS_JWT,t.TR_3DS_REFERENCEID,t.TR_3DS_RESTOKEN,")
                    .append("ci.CI_FIRSTNAME,ci.CI_LASTNAME,ci.CI_EMAIL,ci.CI_COUNTRY,ci.CI_STATE,ci.CI_CITY,ci.CI_EXT1,ci.CI_EXT2,")
                    .append("ci.CI_ADDRESS,ci.CI_ZIPCODE,ci.CI_REMARK,ci.CI_TEL,t.TR_BANKORDERNO,t.TR_INF_TYPE,TR_PAY_TYPE,TR_3DS_FLAG,")

                    .append("TR_TRADE_RATE,TR_SPP_CURRENCY, TR_SPP,TR_SPP_FAIL,TR_LOW_SPP,TR_RESEVER_RATE,TR_CHA_RATE,TR_CHA_RESV_RATE,TR_BANK_SPP_CURRENCY, TR_BANK_SPP,TR_SUBMITURL, ")
                    .append("TR_PAYSTARTTIME, TR_PAYENDTIME, TR_DATETIME, TR_FEE_FAIL_MER, TR_FEE_SUCCESS_MER, TR_FEE_SUCCESS_AFTER_MER, TR_IS_BACK_MER, TR_IS_BACK_AFTER_MER,")
                    .append("TR_FEE_FAIL_CHA, TR_FEE_SUCCESS_CHA, TR_FEE_FAIL_AGENT, TR_FEE_SUCCESS_AGENT, TR_FEE_SUCCESS_AFTER_AGENT, TR_IS_BACK_AGENT, TR_IS_BACK_AFTER_AGENT,")
                    .append("TR_FEE_SUCCESS_AFTER_CHA, TR_IS_BACK_CHA, TR_IS_BACK_AFTER_CHA, TR_CARDTYPE, TR_RISKINFO,TR_SETSCORE,TR_TOTALSCORE,TR_PASSRISKINFO, ")
                    .append("TR_RATE_VALUE_FLOAT,TR_MER_RATE_FLOAT,TR_MER_SETT_RATE_FLOAT,TR_SETT_RATE_FLOAT,TR_SETT_CURRENCY,TR_SETT_RATE,TR_RS_RATE,")
                    .append("TR_AGENT_NO1,TR_AGENT_NO2,TR_AGENT_NO3,TR_AS_CURRENCY1,TR_AS_CURRENCY2,TR_AS_CURRENCY3,TR_AS_RATE1,TR_AS_RATE2,TR_AS_RATE3,TR_AS_STATUS1,TR_AS_STATUS2,TR_AS_STATUS3,")
                    .append("TR_B_TO_A,TR_CHA_MARK,TR_UN_RESEVER,TR_BANKTSDATE,TR_BANKRSDATE,TR_FEE_RATE_FLOAT, TR_3DS_PROVIDER,TR_DM_FLAG,CHA_BILLADDRESS,")
                    .append("CI_SHIPFIRSTNAME,CI_SHIPLASTNAME,CI_SHIPPHONE,CI_SHIPCOUNTRY,CI_SHIPSTATE,CI_SHIPCITY,CI_SHIPADDRESS,CI_SHIPZIP,CI_IPADDRESS")
                    .append(" FROM CCPS_TRADERECORD T")
                    .append(" INNER JOIN CCPS_CREDITINFO ci ON T.TR_NO = ci.CI_TR_NO ")
                    .append(" LEFT JOIN CCPS_CHANNEL c ON C.CHA_CODE = t.tr_cha_code")
                    .append(" LEFT JOIN CCPS_BANK B ON c.CHA_BANK_CODE=B.BANK_CODE ")
                    .append(" LEFT JOIN CCPS_MER_CHANNEL ch on ch.mc_cha_code =  c.cha_code")
                    .append(" WHERE T.TR_NO = ?");
            rs = super.executeQuery(sql.toString(), pars.toArray());
            while (rs.next()) {
                paramBean = new ParamBean();
                paramBean.setReceiveUrl(rs.getString("TR_NOTIFICATION_URL"));
                paramBean.setTrno(rs.getString("TR_QUERYNO"));
                paramBean.setSendAuthData(rs.getString("TR_3DS_AUTHDATA"));
                paramBean.setJwt(rs.getString("TR_3DS_JWT"));
                paramBean.setDeviceDataCollectionUrl(rs.getString("TR_3DS_DDC_URL"));
                paramBean.setTrno(rs.getString("TR_3DS_REFERENCEID"));
                paramBean.setResToken(rs.getString("TR_3DS_RESTOKEN"));
                paramBean.setCybers3DsStep(rs.getInt("TR_3DS_STEP"));
                paramBean.setCybers3DsStatus(rs.getString("TR_3DS_STATUS"));
                paramBean.setJumpUrl(rs.getString("TR_JUMP_URL"));
                paramBean.setTr3dsReturnInfo(rs.getString("TR_3DS_RETURNINFO"));
                paramBean.setTradeStatus(rs.getInt("TR_STATUS"));
                // 交易类型
                paramBean.setPayType(rs.getInt("TR_PAY_TYPE"));
                
                InterfaceParamBean interfaceBean = new InterfaceParamBean(); /// **
                                                                             /// 支付接口参数信息
                                                                             /// **/
                interfaceBean.setMerNo(rs.getString("TR_MER_NO"));
                interfaceBean.setGatewayNo(rs.getString("TR_GW_NO"));
                interfaceBean.setWebSite(rs.getString("TR_WEBSITE"));
                interfaceBean.setOrderNo(rs.getString("TR_MER_ORDERNO"));
                interfaceBean.setOrderAmount(rs.getString("TR_AMOUNT"));
                interfaceBean.setOrderCurrency(rs.getString("TR_CURRENCY"));
                interfaceBean.setReturnUrl(rs.getString("TR_RETURNURL"));
                // 支付方式
                interfaceBean.setPaymentMethod(rs.getString("TR_PM_ID"));

                interfaceBean.setShipFirstName(rs.getString("CI_SHIPFIRSTNAME"));
                interfaceBean.setShipLastName(rs.getString("CI_SHIPLASTNAME"));
                interfaceBean.setShipPhone(rs.getString("CI_SHIPPHONE"));
                interfaceBean.setShipCountry(rs.getString("CI_SHIPCOUNTRY"));
                interfaceBean.setShipState(rs.getString("CI_SHIPSTATE"));
                interfaceBean.setShipCity(rs.getString("CI_SHIPCITY"));
                interfaceBean.setShipAddress(rs.getString("CI_SHIPADDRESS"));
                interfaceBean.setShipZip(rs.getString("CI_SHIPZIP"));
                interfaceBean.setIp(rs.getString("CI_IPADDRESS"));

                String cardNo = "";
                String cvv = "";
                String cardExpireMonth = "";
                String cardExpireYear = "";
                String strInfo = SecUtil.gettempinfo(trNo);
                if (!"".equals(strInfo)) {
                    String[] s = strInfo.split(",");
                    cardNo = s[0];
                    cvv = s[1];
                    cardExpireMonth = s[2].substring(0, 2);
                    cardExpireYear = s[2].substring(2);
                }
                interfaceBean.setCardNo(cardNo);
                interfaceBean.setCardExpireMonth(cardExpireMonth);
                interfaceBean.setCardExpireYear(cardExpireYear);
                interfaceBean.setCardSecurityCode(cvv);
                // interfaceBean.setIssuingBank( rs.getString ("issuingBank"));
                interfaceBean.setFirstName(rs.getString("CI_FIRSTNAME"));
                interfaceBean.setLastName(rs.getString("CI_LASTNAME"));
                interfaceBean.setEmail(rs.getString("CI_EMAIL"));
                // 持卡人IP
                // interfaceBean.setIp(InterfaceUtil.getIpAddr(req));
                // interfaceBean.setPhone( rs.getString ("phone"));
                interfaceBean.setCountry(rs.getString("CI_COUNTRY"));
                interfaceBean.setState(rs.getString("CI_STATE"));
                interfaceBean.setCity(rs.getString("CI_CITY"));
                interfaceBean.setAddress(rs.getString("CI_ADDRESS"));
                interfaceBean.setZip(rs.getString("CI_ZIPCODE"));
                interfaceBean.setRemark(rs.getString("CI_REMARK"));
                interfaceBean.setPhone(rs.getString("CI_TEL"));
                interfaceBean.setExt1(rs.getString("CI_EXT1"));
                interfaceBean.setExt2(rs.getString("CI_EXT2"));
                
                double amout = rs.getDouble("TR_AMOUNT");
                interfaceBean.setOrderAmount(String.valueOf(amout));
                // System.out.print("interfaceBean.setOrderAmount=="+interfaceBean.getOrderAmount());
                interfaceBean.setOrderCurrency(rs.getString("TR_CURRENCY"));
                interfaceBean.setSelectPayment(rs.getString("TR_PM_ID"));
                
                paramBean.setInterfaceParamBean(interfaceBean);

                paramBean.setCardType(rs.getInt("TR_CARDTYPE"));
                
                com.lot.bean.TradeInfoBean tradeInfoBean = new com.lot.bean.TradeInfoBean();
                tradeInfoBean.setRealRate(rs.getDouble("TR_TRADE_RATE"));
                tradeInfoBean.setRealAmount(rs.getDouble("TR_BANKAMOUT"));
                tradeInfoBean.setRealCurrency(rs.getString("TR_BANKCURRENCY"));
                tradeInfoBean.setTrBankorderno(rs.getString("TR_BANKORDERNO"));
                tradeInfoBean.setTrReference(rs.getString("TR_REFERENCE"));

                // TR_RATE_VALUE_FLOAT,TR_MER_RATE_FLOAT,TR_MER_SETT_RATE_FLOAT,
                // TR_SETT_RATE_FLOAT, TR_SETT_CURRENCY,TR_SETT_RATE,TR_RS_RATE,
                tradeInfoBean.setMer_float_rate(rs.getDouble("TR_MER_RATE_FLOAT"));
                tradeInfoBean.setMer_sett_float_rate(rs.getDouble("TR_MER_SETT_RATE_FLOAT"));
                tradeInfoBean.setRealSettRate(rs.getDouble("TR_SETT_RATE"));
                
                //TR_B_TO_A,TR_CHA_MARK,TR_UN_RESEVER,TR_BANKTSDATE,TR_BANKRSDATE,TR_FEE_RATE_FLOAT, TR_3DS_PROVIDER,TR_DM_FLAG
                paramBean.setNewCardType(rs.getInt("TR_B_TO_A"));
                paramBean.setFeeFloatRate(rs.getDouble("TR_FEE_RATE_FLOAT"));
//                paramBean.setCybers3DsStatus(rs.getString("TR_3DS_FLAG"));
                paramBean.setDmFlag(rs.getInt("TR_DM_FLAG"));
                
                BankChannelBean bankChannel = new BankChannelBean();// 通道数据信息
                bankChannel.setChaMark(rs.getInt("TR_CHA_MARK"));
                bankChannel.setMerIsUnReserFee(rs.getInt("TR_UN_RESEVER"));
                bankChannel.setChaTsdate(rs.getInt("TR_BANKTSDATE"));
                bankChannel.setChaRsdate(rs.getInt("TR_BANKRSDATE"));
                
                bankChannel.setMcTrRate(rs.getBigDecimal("TR_RATE_VALUE_FLOAT"));
                bankChannel.setMcSeRate(rs.getBigDecimal("TR_SETT_RATE_FLOAT"));
                bankChannel.setMbaBankCurrency(rs.getString("TR_SETT_CURRENCY"));

                bankChannel.setMcAutouthor(rs.getInt("TR_AUTH_TYPE"));
                bankChannel.setBankId(rs.getInt("BANK_ID"));
                bankChannel.setBankCode(rs.getString("BANK_CODE"));
                bankChannel.setBankPayUrl(rs.getString("BANK_PAY_URL"));
                bankChannel.setBankCheckUrl(rs.getString("BANK_CHECK_URL"));
                bankChannel.setBankReqUrl(rs.getString("BANK_REQ_URL"));
                bankChannel.setDirect(rs.getBoolean("BANK_ISDIRECT"));
                bankChannel.setChannelCode(rs.getInt("CHA_CODE"));
                bankChannel.setChannelMerNo(rs.getString("CHA_MERNO"));
                bankChannel.setChannelAccessCode(rs.getString("CHA_VPC_ACCESSCODE"));
                bankChannel.setChannelSecureCode(rs.getString("CHA_SECURE_SECRET"));
                bankChannel.setBilladdress(rs.getString("CHA_BILLADDRESS"));
                // 增加通道用户名 2012-8-22
                bankChannel.setChannelUser(rs.getString("CHA_VPC_USER"));
                bankChannel.setChaVpcPassword(rs.getString("CHA_VPC_PASSWORD"));
                bankChannel.setChannelCurrency(rs.getString("CHA_CURRENCY"));
                bankChannel.setChannelSettlementBank(rs.getString("CHA_SETTLEMENT_BANK"));
                bankChannel.setChannelIsDcc(rs.getInt("CHA_ISDCC"));
                bankChannel.setChannelIsDelay(rs.getInt("CHA_ISDELAY"));
                bankChannel.setChannelThreeParty(rs.getInt("CHA_THREEPARTY"));
                bankChannel.setChannelTwoFiveParty(rs.getInt("CHA_TWOFIVEPARTY"));
                bankChannel.setChannelTwoParty(rs.getInt("CHA_TWOPARTY"));
                bankChannel.setChannelFeeCurrency(rs.getString("CHA_FEECURRENCY"));
                bankChannel.setChannelFeeAmount(rs.getDouble("CHA_FEEAMOUNT"));
                bankChannel.setChannelFeeFail(rs.getInt("CHA_FEE_FAIL"));
                bankChannel.setChannelFeeSuccess(rs.getInt("CHA_FEE_SUCCESS"));
                bankChannel.setChannelFeeSuccessAfter(rs.getInt("CHA_FEE_SUCCESS_AFTER"));
                bankChannel.setChannelIsBack(rs.getInt("CHA_IS_BACK"));
                bankChannel.setChannelIsBackAfter(rs.getInt("CHA_IS_BACK_AFTER"));
                bankChannel.setIsThreeParty(rs.getInt("CHA_ISTHREEPARTY"));
                bankChannel.setChaObligate1(rs.getString("CHA_OBLIGATE1"));
                bankChannel.setChaObligate2(rs.getString("CHA_OBLIGATE2"));
                if(null != rs.getString("CHA_3DS_PROVIDER")){
                    bankChannel.setCha3dsProvider(rs.getInt("CHA_3DS_PROVIDER"));
                }else{
                    bankChannel.setCha3dsProvider(0);
                }
                paramBean.setSubmitUrl(rs.getString("TR_SUBMITURL"));
                paramBean.setEnterPayTime(DateUtil.getDateTimestamp(rs.getTimestamp("TR_PAYSTARTTIME")));
                paramBean.setEnterPageTime("");
                bankChannel.setTradeRate(rs.getDouble("TR_TRADE_RATE"));
                bankChannel.setReserverRate(rs.getDouble("TR_RESEVER_RATE"));
                bankChannel.setMerFeeCurrency(rs.getString("TR_SPP_CURRENCY"));
                bankChannel.setMerFeeAmount(rs.getDouble("TR_SPP"));
                bankChannel.setMerFeeAmountfail(rs.getDouble("TR_SPP_FAIL"));
                bankChannel.setMerLowFeeAmount(rs.getInt("TR_LOW_SPP"));

                bankChannel.setMerFeeFail(rs.getInt("TR_FEE_FAIL_MER"));
                bankChannel.setMerFeeSuccess(rs.getInt("TR_FEE_SUCCESS_MER"));
                bankChannel.setMerFeeSuccessAfter(rs.getInt("TR_FEE_SUCCESS_AFTER_MER"));
                bankChannel.setMerIsBack(rs.getInt("TR_IS_BACK_MER"));
                bankChannel.setMerIsBackAfter(rs.getInt("TR_IS_BACK_AFTER_MER"));
                
                tradeInfoBean.setBankChannel(bankChannel);
                paramBean.setInterfaceStatus("formal");
                paramBean.setInterfaceType(rs.getInt("TR_INF_TYPE"));
                tradeInfoBean.setTradeNo(trNo);
                tradeInfoBean.setTrRemark(rs.getString("TR_REMARK"));
                
                // TR_RISKINFO,TR_SETSCORE,TR_TOTALSCORE,TR_PASSRISKINFO
                tradeInfoBean.setRiskInfo(rs.getString("TR_RISKINFO"));
                tradeInfoBean.setPassRiskInfo(rs.getString("TR_PASSRISKINFO"));
                tradeInfoBean.setTotalScore(rs.getInt("TR_TOTALSCORE"));
                tradeInfoBean.setTotalSetScore(rs.getInt("TR_SETSCORE"));
                
                paramBean.setTradeInfoBean(tradeInfoBean);
            }
        } catch (Exception ex) {
            log.error(InterfaceUtil.getExceptionInfo(ex));
        }finally {
            super.closeAll();
        }
        return paramBean;
    }

    @Override
    public boolean updateTrade3DSInfo(String trNo, Map<String, String> replyMap) {
        // sql语句参数
        List<Object> pars = new ArrayList<Object>();
        
        String step = replyMap.get("step");
        String status = replyMap.get("status");
        String returnInfo = replyMap.get("returnInfo"); 
        String referenceId = replyMap.get("referenceId");
        
        int count  = 0;
        String tr3dsProvider = replyMap.get("tr3dsProvider");
        if(StringUtils.isBlank(tr3dsProvider)){
            tr3dsProvider = "0";
        }
        StringBuffer sql = new StringBuffer();
        sql.append("UPDATE CCPS_TRADERECORD SET TR_3DS_PROVIDER = ?, TR_3DS_STEP = ?, TR_3DS_STATUS = ?");
        pars.add(tr3dsProvider);
        pars.add(step);
        pars.add(status);
        if(StringUtils.isNotBlank(returnInfo)){
            sql.append(", TR_3DS_RETURNINFO = ?");
            pars.add(returnInfo);
        }
        if(StringUtils.isNotBlank(referenceId)){
            sql.append(", TR_QUERYNO = ?");
            pars.add(referenceId);
        }
        
        if("1".equals(step) || "2".equals(step)){
            String jwt = replyMap.get("accessToken");
            String deviceDataCollectionUrl = replyMap.get("deviceDataCollectionUrl");
            if(StringUtils.isNotBlank(jwt)){
                sql.append(", TR_3DS_JWT = ?");
                pars.add(jwt);
            }
            if(StringUtils.isNotBlank(deviceDataCollectionUrl)){
                sql.append(", TR_3DS_DDC_URL = ?");
                pars.add(deviceDataCollectionUrl);
            }
            if(StringUtils.isNotBlank(referenceId)){
                sql.append(", TR_3DS_REFERENCEID = ?");
                pars.add(referenceId);
            }
            String resToken = replyMap.get("resToken");
            String redirectUrl = replyMap.get("redirectUrl");
            if(StringUtils.isNotBlank(resToken)){
                sql.append(", TR_3DS_RESTOKEN = ?");
                pars.add(resToken);
            }
            if(StringUtils.isNotBlank(redirectUrl)){
                sql.append(", TR_REDIRECT_URL = ?");
                pars.add(redirectUrl);
            }
            
        }
        
        sql.append(" WHERE TR_NO = ?");
        pars.add(trNo);
        count = super.executeUpdate(sql.toString(),pars.toArray());
        
        return count>0; 
    }

    @Override
    public boolean checkMerBindCybers3DS(ParamBean paramBean) {
        try {
            String merNoRisk = paramBean.getMerNoRisk();
            // 绑定风控的网关
            String gwNoRisk = paramBean.getGwNoRisk();
            String reCode = "threeDSCheck";
            List<Object> param = new ArrayList<Object>();
            StringBuffer sql = new StringBuffer(
                    "SELECT count(1) FROM ccps_mer_riskelment mr")
                    .append(" WHERE mr.mr_mer_no = ? AND mr.mr_gw_no = ?")
                    .append(" AND mr.mr_re_id = (SELECT re_id FROM ccps_risk_element WHERE re_code=?)");

            param.add(merNoRisk);
            param.add(gwNoRisk);
            param.add(reCode);
            int count = Integer.valueOf(super.executeQueryByFirst(sql.toString(), param.toArray()));
            return count > 0;
        } catch (Exception ex) {
            log.error(InterfaceUtil.getExceptionInfo(ex));
        }
        return false;
    }
    
    @Override
    public boolean check3DSMerchantTrade(ParamBean paramBean) {
        boolean flag = false;
        ResultSet rs = null;
        try {
            String gwNo = paramBean.getInterfaceParamBean().getGatewayNo();
            int cardType = InterfaceUtil.getCardNoType(paramBean.getInterfaceParamBean().getCardNo());
            int chaCode = paramBean.getTradeInfoBean().getBankChannel().getChannelCode();
            List<Object> param = new ArrayList<Object>();
            // 先按网关查询是否有设置3限定信息
            StringBuffer sql = new StringBuffer(
                    "SELECT CL_COUNT FROM CCPS_CHANNEL_LIMIT mr")
                    .append(" WHERE GW_NO = ? AND CARD_TYPE = ?");

            param.add(gwNo);
            param.add(cardType);
            int tradeCount = 0;
            int count = 0;
            rs = super.executeQuery(sql.toString(), param.toArray());
            if (rs.next()){
                tradeCount = rs.getInt("CL_COUNT");
            }
            log.info("3ds校验--判断是否设置了3ds校验笔数限制，tradeCount="+tradeCount);
            if(tradeCount > 0){
                // 有设置限定信息，统计近一个月交易笔数  AND TR_3DS_PROVIDER = 1
                StringBuffer countSql = new StringBuffer(
                        "SELECT COUNT(1) FROM ccps_traderecord t WHERE t.tr_datetime >= sysdate-30 ")
                        .append(" AND t.tr_cha_code = ? AND t.tr_cardtype = ? ");
                param = new ArrayList<Object>();
                param.add(chaCode);
                param.add(cardType);
                
                count = Integer.valueOf(super.executeQueryByFirst(countSql.toString(), param.toArray()));
                
                log.info("3ds校验--统计当前交易商户近一个月交易笔数是否超过限定笔数 count="+count+",tradeCount="+tradeCount);
                if(count <= tradeCount){
                    flag = true;
                }
            } 
        } catch (Exception ex) {
            log.error(InterfaceUtil.getExceptionInfo(ex));
        }finally {
            super.closeAll();
        }
        return flag;
    }
    
    @Override
    public boolean updateTrade3DSStep(String trNo, int tr3dsProvider, int step, String authData, String returnInfo) {
        int count = 0;
        try {
            // sql语句参数
            List<Object> pars = new ArrayList<Object>();
            StringBuffer sql = new StringBuffer();
            sql.append("UPDATE CCPS_TRADERECORD SET TR_3DS_PROVIDER = ?");
            pars.add(tr3dsProvider);
            if(step > 0){
                sql.append(",TR_3DS_STEP=?");
                pars.add(step);
            }
            
            if(StringUtils.isNotBlank(authData)){
                sql.append(",TR_3DS_AUTHDATA=?");
                pars.add(authData);
            }

            if(StringUtils.isNotBlank(returnInfo)){
                sql.append(",TR_3DS_RETURNINFO=(NVL(TR_3DS_RETURNINFO,'') || ?)");
                pars.add(returnInfo);
            }
            
            sql.append(" WHERE TR_NO = ?");
            pars.add(trNo);
            count = super.executeUpdate(sql.toString(), pars.toArray());
            return count > 0;
        } catch (Exception ex) {
            log.error(InterfaceUtil.getExceptionInfo(ex));
        }
        return false;
    }

    @Override
    public boolean checkTrade3DSStep(String trNo, int step) {
        try {
            List<Object> param = new ArrayList<Object>();
            StringBuffer sql = new StringBuffer(
                    "SELECT count(1) FROM CCPS_TRADERECORD WHERE TR_NO = ? and TR_3DS_STEP = ?");

            param.add(trNo);
            param.add(step);
            int count = Integer.valueOf(super.executeQueryByFirst(sql.toString(), param.toArray()));
            return count > 0;
        } catch (Exception ex) {
            log.error(InterfaceUtil.getExceptionInfo(ex));
        }
        return false;
    }
    
    @Override
    public void saveTradeNotification(String tradeNo, String statusCode, String jsonData,
            String errorCode, String errorInfo, String notificationUrl){
        
        try {
            StringBuffer sql = new StringBuffer();
            sql.append("INSERT INTO CCPS_TRADE_NOTIFICATION (TN_ID, TN_TR_NO, TN_STATUS, TN_RETURN_INFO, TN_TYPE,");
            sql.append("TN_RESPONSE_CODE, TN_START_TIME, TN_TIMES, TN_MAX_TIMES, TN_LAST_TIME, TN_NOTIFICATION_URL, TN_DATA_INFO )");
            sql.append(" VALUES (CCPS_TRADE_NOTIFICATION_SEQ.NEXTVAL, ?, ?, ?, ?, ?, systimestamp, ?, ?, systimestamp, ?, ?)");
            
            int status = 0;
            if("200".equals(statusCode) || "00".equals(errorCode)){
                status = 1;
                errorInfo = "Success";
            }
            log.info(tradeNo+"保存消息推送信息");
            // sql语句参数
            List<Object> pars = new ArrayList<Object>();
            pars.add(tradeNo);
            pars.add(status);
            pars.add(errorInfo);
            pars.add(1);
            pars.add(statusCode);
            pars.add(1);
            // 失败后重发通知次数
            pars.add(5);
            pars.add(notificationUrl);
            pars.add(jsonData);
            super.executeUpdate(sql.toString(),pars.toArray());
        } catch (Exception e) {
            log.error(InterfaceUtil.getExceptionInfo(e));
        }
    }
    
    public boolean updateTrade3DSAcsInfo(String trNo, String stepUpUrl, String jwt) {
        int count = 0;
        try {
            // sql语句参数
            List<Object> pars = new ArrayList<Object>();
            StringBuffer sql = new StringBuffer();
            sql.append("UPDATE CCPS_TRADERECORD SET TR_3DS_DDC_URL = ?, TR_3DS_JWT = ? WHERE TR_NO = ?");
            pars.add(stepUpUrl);
            pars.add(jwt);
            pars.add(trNo);
            count = super.executeUpdate(sql.toString(), pars.toArray());
            return count > 0;
        } catch (Exception ex) {
            log.error(InterfaceUtil.getExceptionInfo(ex));
        }
        return false;
    }
}
