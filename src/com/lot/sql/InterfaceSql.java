package com.lot.sql;

/**
 * 
 * <p>Title: </p>
 * <p>Description: 保存接口sql语句</p>
 * <p>Copyright: Copyright (c) 2011 版权</p>
 * <p>Company: </p>
 * <AUTHOR>
 * @version V1.0 
 * @date 2011-7-7下午05:27:04
 */
public class InterfaceSql {
	
	//获取卡种
	public static final StringBuffer SQL_GET_CARD_TYPE	= new StringBuffer();
	
	//获取币种
	public static final StringBuffer SQL_GET_CURRENCY_INFO = new StringBuffer();

	// 获取商户网关接入号信息
	public static final StringBuffer SQL_GET_MER_GATEWAY_INFO = new StringBuffer();
	
	//获取网关接入号绑定的附加元素信息
	public static final StringBuffer SQL_GET_EXTRA_ELEMENT = new StringBuffer();
	
	// 保存异常交易记录
 	public static final StringBuffer SQL_SAVE_UNTRADE_RECORD = new StringBuffer();
	
	// 保存非正式交易记录
	public static final StringBuffer SQL_SAVE_INFORMAL_RECORD = new StringBuffer();
	
	// 保存持卡人记录信息
 	public static final StringBuffer SQL_SAVE_CREDIT_INFO = new StringBuffer();
	
	//修改异常交易记录
 	public static final StringBuffer SQL_UPDATE_UNTRADE_RECORD_BYWEBSITE = new StringBuffer();
	
	//修改异常交易记录
	public static final StringBuffer SQL_UPDATE_UNTRADE_RECORD  = new StringBuffer();
	
	//修改非正式交易记录
	public static final StringBuffer SQL_UPDATE_INFORMAL_RECORD  = new StringBuffer();
	
	//正常状态:判断商户订单号是否存在
	public static final StringBuffer SQL_CHECK_FORMAL_ORDERNO  = new StringBuffer();
	
	//测试状态:判断商户订单号是否存在
	public static final StringBuffer SQL_CHECK_TEST_ORDERNO  = new StringBuffer();
	
	//判断流水订单号是否存在
	public static final StringBuffer SQL_CHECK_TRADENO  = new StringBuffer();
	
	//检测是否绑定通道
	public static final StringBuffer SQL_CHECK_GATEWAY_CHANNEL = new StringBuffer();
	
	//获取网关接入号绑定的通道信息
	 public static final StringBuffer SQL_GET_GATEWAY_CHANNEL = new StringBuffer();
	
	//获取网关接入号绑定的代理商
	public static final StringBuffer SQL_GET_MERCHANT_AENT = new StringBuffer();
	public static final StringBuffer SQL_GET_MERCHANT_AENT_NEW = new StringBuffer();
	
	//根据原始币种 目标币种查询汇率
	public static final StringBuffer SQL_GET_CURRENCY_RATE = new StringBuffer();
	
	//更新测试交易记录
	public static final StringBuffer SQL_UPDATE_TEST_RECORD = new StringBuffer();

	//更新测试交易记录
	public static final StringBuffer SQL_DELETE_UNTRADE_RECORD = new StringBuffer();
	
	// 更新是否重复支付标志
	public static final StringBuilder SQL_UPDATE_TR_IS_REPAY = new StringBuilder();

	//保存交易记录表
	public static final StringBuffer SQL_SAVE_TRADE_RECORD = new StringBuffer();
	/** 代理商信息保存修改，支持多级代理商 2015-01-23 add*/
	public static final StringBuffer SQL_SAVE_TRADE_RECORD_NEW = new StringBuffer();
	
	//获取网关接入号绑定的域名信息
	public static final StringBuffer SQL_GET_DOMAIN = new StringBuffer();
	
	//判断是否测试卡
	public static final StringBuffer SQL_IS_TEST_CARD = new StringBuffer();
	
	//发送银行前更新交易序列号(提交到银行的唯一值 )如：YESPAYMENTS为12位 CHINAPAY为16位
	public static final StringBuffer SQL_UPDATE_TR_REFERENCE = new StringBuffer();
	//更新提交到银行的订单号、查询号和批次号/有效期
	public static final StringBuffer SQL_UPDATE_TR_REF_AND_QUERYNO = new StringBuffer();

	//与银行多次交互时保存银行交易码及发送银行交易序列号(提交到银行的唯一值 )
	public static final StringBuffer SQL_UPDATE_TR_REFERENCE_AND_TR_BANKORDERNO = new StringBuffer();
	
	//2方判断是否通过服务器IP限定
	public static final StringBuffer SQL_CHECK_SERVERIP = new StringBuffer();
	
	//插入持卡人唯一标识对应的实体
	public static final StringBuffer SQL_SAVE_CREDITINTO_RECURRING = new StringBuffer();
	
	//查询持卡人唯一标识对应的实体
	public static final StringBuffer SQL_GET_CREDITINTO_RECURRING = new StringBuffer();
	
	//根据支付方式名称获取支付方式信息
	public static final StringBuffer SQL_GET_PAYMENT_METHOD_BY_NAME = new StringBuffer();
	
	
	
	
	//根流水订单号获取要保存的预授权信息字段
	public static final StringBuffer SQL_GETAUTHINF_TRNO = new StringBuffer();
	
	//保存预授权信息 数据异常
	public static final StringBuffer SQL_SAVE_UN_AUTH = new StringBuffer();
	
	//保存预授权信息
	 public static final StringBuffer SQL_SAVE_AUTH = new StringBuffer();
	//更新交易记录表预授权状态
	public static final StringBuffer SQL_UPDATE_TRADE_AUTH = new StringBuffer();
	//更新交易记录表预授权状态
	 public static final StringBuffer SQL_UPDATE_TRADE_AUTH2 = new StringBuffer();
	 public static final StringBuffer SQL_UPDATE_TRADE_AUTH3 = new StringBuffer();
	//查询授权天数
	public static final StringBuffer SQL_GETTRADEINFO = new StringBuffer();
	public static final StringBuffer SQL_GETAUTHDAY = new StringBuffer();
	//撤销授权 查询Ref_No
	public static final StringBuffer SQL_AUTH_REFNO_CX = new StringBuffer();
	//授权成功查询号码
	public static final StringBuffer SQL_AUTH_NO_COM = new StringBuffer();
	
	//更改异常交易表中的进入网关时间
	public static final StringBuffer SQL_UPDATE_UTR_PAYTIME = new StringBuffer();
	
	public static final StringBuffer SQL_UPDATE_CREADIT=new StringBuffer();
	
	//根据流水订单号查询持卡人
	public static final StringBuffer SQL_QUERY_CREADIT_BYTRNO=new StringBuffer();
	public static final StringBuffer SQL_QUERY_CREADIT_TEMP_BYTRNO=new StringBuffer();
	
	public static final StringBuffer SQL_QUERY_SYSSET = new StringBuffer();
	//查询业务参数
	public static final StringBuffer SQL_QUERY_BUSINESS_PARAM = new StringBuffer();
	//根流水订单号获取要保存的预授权信息字段 渣打
	public static final StringBuffer SQL_GETSCBAUTHINF_TRNO = new StringBuffer();

	public static final StringBuffer SQL_QUERY_REPAY = new StringBuffer();

	public static final StringBuffer SQL_TELESIGN_SAVE =  new StringBuffer();

	public static final StringBuffer SQL_TELESIGN_QUERY = new StringBuffer();

	public static final StringBuffer SQL_SAVE_PAGEINFO = new StringBuffer();

	public static final StringBuffer SQL_GET_UNTRADERECORD = new StringBuffer();

	public static final StringBuffer SQL_GET_TRADERECORD = new StringBuffer();

	public static final StringBuffer SQL_GET_TRADE_ONEMINITE = new StringBuffer();
	
	public static final StringBuffer SQL_GET_TESTTRADE_ONEMINITE = new StringBuffer();

	//保存终端信息
	public static final StringBuffer SQL_SAVE_TERMINAL = new StringBuffer();
	
	//DCC汇率查询
	public static final StringBuffer SQL_GET_DCCCHA_INFO = new StringBuffer();
	
	public static final StringBuffer SQL_UPDATE_DCCCHA_TRADE = new StringBuffer(); 
	
	public static final StringBuffer SQL_UPDATE_DCCCHA_CRIDITE = new StringBuffer(); 
	
	public static final StringBuffer SQL_GET_INTERFACE_PARAM = new StringBuffer();
	
	//退款接口
	public static final StringBuffer SQL_SAVE_REFUND = new StringBuffer();
	//查询网关信息
	public static final StringBuffer SQL_GET_GATEWAY = new StringBuffer();
	//查询订单信息
	public static final StringBuffer SQL_GET_TRADE_REFUND =  new StringBuffer();
	//退款批次号
	public static final StringBuffer SQL_GET_BATCHNO = new StringBuffer();
	//查询异常交易表
	public static final StringBuffer SQL_GET_PROCESSTOTAL = new StringBuffer();
	//查询划款状态
	public static final StringBuffer SQL_GET_TRADESETTLEMENT = new StringBuffer();
	public static final StringBuffer SQL_PROCESS_SETTLE = new StringBuffer();
	public static final StringBuffer SQL_GET_AGENTSETTLEMENT = new StringBuffer();
	 //scb 
    public static final StringBuffer SLQ_SCB_INSERT_DETAIL = new StringBuffer();
    public static final StringBuffer SLQ_SCB_INSERT_FUNCTION = new StringBuffer();
    //商户勾兑IP
    public static final StringBuffer SQL_CHECK_IP = new StringBuffer();
    //获取网关需要验证的卡种
    public static final StringBuffer SQL_GET_TEL_VALIDATION_CARDTYPE = new StringBuffer();
    
    //获取系统参数值
    public static final StringBuffer SQL_GET_SYS_PARAM_VALUE = new StringBuffer();
    
    //获取汇率浮动值
    public static final StringBuffer SQL_GET_FLOAT_VALUE = new StringBuffer();
    public static final StringBuffer SQL_GET_SETT_FLOAT_VALUE = new StringBuffer();
    public static final StringBuffer SQL_GET_MER_FLOAT_VALUE = new StringBuffer();
	static {
		SQL_GET_MER_FLOAT_VALUE.append("SELECT A.* FROM (SELECT FR_M, FR.FR_H1, FR.FR_QH, FR.FR_FLOAT_RATE, FR.FR_XN, FR.FR_K, MF.MFR_FR_ID, MF.MFR_MER_NO,MF.MFR_GW_NO, MF.MFR_CARDTYPE,MF.MFR_TYPE" +
				"  FROM CCPS_MER_FLOAT_RATE MF LEFT JOIN CCPS_FLOAT_RATE_SET FR ON FR.FR_ID = MF.MFR_FR_ID" +
				"  WHERE MF.MFR_TYPE = ? AND ((MF.MFR_MER_NO = ? AND MF.MFR_GW_NO = 0) OR (MF.MFR_MER_NO = ? AND MF.MFR_GW_NO = ?))" +
				"  AND FR.FR_MIN_AMOUNT <= ? AND FR.FR_MAX_AMOUNT > ? AND FR.FR_CURRENCY = ? AND FR.FR_BANKCURRENCY = ? AND (MF.MFR_CARDTYPE = 0 OR MF.MFR_CARDTYPE = ?)");
		
		SQL_GET_FLOAT_VALUE.append(" select fr_M,fr.FR_H1,fr.FR_QH,fr.fr_float_rate,fr.fr_xn,fr.fr_k from CCPS_FLOAT_RATE_SET fr left join CCPS_MER_FLOAT_RATE mf on fr.fr_id=mf.mfr_fr_id where mf.mfr_type=1 and (( mf.mfr_mer_no=? and mf.mfr_gw_no=0  ) or ( mf.mfr_mer_no=? and mf.mfr_gw_no=?  )) and   ?>=fr.fr_min_amount and ?<fr.fr_max_amount and fr.fr_currency=? and fr.FR_BANKCURRENCY=?  ");
		SQL_GET_SETT_FLOAT_VALUE.append(" select fr_M,fr.FR_H1,fr.FR_QH,fr.fr_float_rate,fr.fr_xn,fr.fr_k from CCPS_FLOAT_RATE_SET fr left join CCPS_MER_FLOAT_RATE mf on fr.fr_id=mf.mfr_fr_id where mf.mfr_type=2 and (( mf.mfr_mer_no=? and mf.mfr_gw_no=0  ) or ( mf.mfr_mer_no=? and mf.mfr_gw_no=?  )) and   ?>=fr.fr_min_amount and ?<fr.fr_max_amount and fr.fr_currency=? and fr.FR_BANKCURRENCY=?  ");
		
		SQL_GET_CARD_TYPE.append("SELECT ct.CT_ID, ct.CT_NAME, ct.CT_VALUE FROM CCPS_CARDTYPE ct");
		
		SQL_GET_CURRENCY_INFO.append("SELECT CURR_ID, CURR_NAME, CURR_VALUE, CURR_CODE FROM CCPS_CURRENCY");
		
		SQL_GET_MER_GATEWAY_INFO.append("SELECT gw.gw_cardbin,gw.gw_show_risk,mc.MER_NO,gw.GW_NO,mc.MER_NO1,gw.GW_NO1,gw.gw_checkurl_flag,gw.GW_GROUPWHITE,gw.GW_GROUPBLACK,gw.GW_SECONDWHITE,gw.GW_GROUP,gw.GW_GROUP2,gw.GW_GROUP3," +
				"GW.gw_sett_rate,gw.GW_TRADE_RATE,gw.GW_RISK_METHOD,gw.GW_CLIENTID_ISREQ,gw.gw_returnbilladd,mc.MER_STATUS,gw.GW_STATUS,gw.GW_MD5KEY,gw.GW_INF_TYPE,gw.GW_ISVT,gw.GW_RETURN_MODEL,gw.GW_SECONDPAY,GW.GW_LOWESTAMOUNT,GW.GW_LOWESTAMOUNTCUR,gw.GW_RECURRING_FLAG,gw.GW_PAYTIMES, gw.GW_CHECKCVV_FLAG  ");
		SQL_GET_MER_GATEWAY_INFO.append("FROM CCPS_GATEWAY gw LEFT JOIN CCPS_MERCHANT mc on gw.GW_MER_NO=mc.MER_NO ");
		SQL_GET_MER_GATEWAY_INFO.append("WHERE 1=1 ");
		
		SQL_GET_EXTRA_ELEMENT.append("SELECT el.EE_NAME_CN,el.EE_NAME_EN,el.EE_VALUE,el.EE_LENGTH,gr.GR_STATUS FROM CCPS_EXTRA_ELEMENT el ");
		SQL_GET_EXTRA_ELEMENT.append("LEFT JOIN CCPS_EXTRA_ROLE_ELE ere ON el.EE_ID=ere.EE_ID LEFT JOIN CCPS_EXTRA_ROLE  er ON ere.ER_ID=er.ER_ID ");
		SQL_GET_EXTRA_ELEMENT.append("LEFT JOIN CCPS_GATEWAY_ROLE gr ON er.ER_ID=gr.GR_ER_ID WHERE gr.GR_STATUS=1 and gr.GR_MER_NO=? and gr.GR_GW_NO=?");
		
		SQL_SAVE_UNTRADE_RECORD.append("INSERT INTO CCPS_UNNORMAL_TRADERECORD (UTR_ID, UTR_NO, UTR_MER_NO,");
		SQL_SAVE_UNTRADE_RECORD.append("UTR_GW_NO, UTR_MER_ORDERNO, UTR_CURRENCY, UTR_AMOUNT, UTR_BANK_CODE, UTR_CHA_CODE, ");
		SQL_SAVE_UNTRADE_RECORD.append("UTR_RETURNURL, UTR_WEBSITE, UTR_SUBMITURL,UTR_PAYSTARTTIME, UTR_PAYTIME, UTR_PAYENDTIME,");
		SQL_SAVE_UNTRADE_RECORD.append("UTR_ERRORCODE, UTR_ERRORREASON_OUT, UTR_ERRORREASON_IN, UTR_MER_REMARK,UTR_NOTIFICATION_URL) ");
		SQL_SAVE_UNTRADE_RECORD.append("VALUES (CCPS_UNNORMAL_TRADERECORD_SEQ.nextval, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, ?, ?, ?, ?, ?, ?,?)");
		
		SQL_SAVE_INFORMAL_RECORD.append("INSERT INTO CCPS_INFORMAL_TRADERECORD (TTR_ID, TTR_NO, TTR_DATETIME,");
		SQL_SAVE_INFORMAL_RECORD.append("TTR_MER_NO, TTR_GW_NO, TTR_MER_ORDERNO,TTR_CURRENCY, TTR_AMOUNT, TTR_STATUS,");
		SQL_SAVE_INFORMAL_RECORD.append("TTR_MR_TRADE_RATE, TTR_MR_RESEVER_RATE, TTR_RATE_VALUE,TTR_BW_BANKCURRENCY, TTR_BW_BANKAMOUT, TTR_BANK_CODE,");
		SQL_SAVE_INFORMAL_RECORD.append("TTR_CHA_CODE, TTR_BANKDATETIME, TTR_BANKCODE,TTR_BANKINFO_IN, TTR_BANKINFO_OUT, TTR_RETURNURL,TTR_WEBSITE,");
		SQL_SAVE_INFORMAL_RECORD.append("TTR_SUBMITURL, TTR_COOKIEID, TTR_EMAIL, TTR_IPADDRESS, TTR_IPCOUNTRY, TTR_REMARK)");
		SQL_SAVE_INFORMAL_RECORD.append("VALUES ( CCPS_INFORMAL_TRADERECORD_SEQ.nextval, ?, systimestamp, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
		
		SQL_SAVE_CREDIT_INFO.append("INSERT INTO CCPS_CREDITINFO (CI_ID, CI_TR_NO, CI_EMAIL, ");
		SQL_SAVE_CREDIT_INFO.append("CI_TEL, CI_FIRSTNAME, CI_LASTNAME, CI_COUNTRY, CI_STATE, CI_CITY, ");
		SQL_SAVE_CREDIT_INFO.append("CI_ZIPCODE, CI_CELLPNONE, CI_ADDRESS, CI_IPADDRESS, CI_IPCOUNTRY, CI_MER_IPADDRESS, CI_ISSUINGBANK, ");
		SQL_SAVE_CREDIT_INFO.append("CI_ISSUINGBANKTEL, CI_CARDCOUNTRY, CI_FROMNAME,CI_ACQUIRER, CI_ISSENDEMAIL, CI_OS,");
		SQL_SAVE_CREDIT_INFO.append("CI_BROWER, CI_LANG, CI_TIMEZONE,CI_RESOLUTION, CI_COOKIEID, CI_OLD_COOKIE, ");
		SQL_SAVE_CREDIT_INFO.append("CI_COOKIE_FLAG, CI_SHA256, CI_CARDNOPART, CI_CARDTYPE, CI_REMARK, CI_PAYMENT_METHOD, CI_EBANX_NAME, ");
		SQL_SAVE_CREDIT_INFO.append("CI_EBANX_EMAIL, CI_EBANX_TYPE, CI_QIWI_USER, CI_PPRO_ACCOUNT_NUMBER, CI_PPRO_BANKCODE,CI_CARDNO_ENCRYPT ,");
		SQL_SAVE_CREDIT_INFO.append("CI_QIWI_COUNTRYCODE, CI_INTERFACE_INFO, CI_INTERFACE_VERSION ,CI_EBANXCPF, CI_ISMOBILE, ");
		SQL_SAVE_CREDIT_INFO.append(" CI_DATETIME");
		SQL_SAVE_CREDIT_INFO.append(",CI_CRETYPE,CI_CRENUMPART,CI_CRENUMENCRYPT,CI_CARDEXPIREDATE,CI_UNIONPAY_BANKCODE,CI_UNIONPAY_PAYTYPE,CI_CLIENT_ID," +
				"CI_SHIPFIRSTNAME,CI_SHIPLASTNAME,CI_SHIPCOUNTRY,CI_SHIPSTATE,CI_SHIPCITY,CI_SHIPADDRESS,CI_SHIPZIP,CI_PRODUCT_NAME,CI_PRODUCT_NUM,CI_PRODUCT_DESC,CI_EXT1,CI_EXT2,CI_TEMP) ");
		SQL_SAVE_CREDIT_INFO.append("VALUES (CCPS_CREDITINFO_SEQ.nextval, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,?,?,systimestamp,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		
	  	 SQL_UPDATE_UNTRADE_RECORD_BYWEBSITE.append("UPDATE CCPS_UNNORMAL_TRADERECORD SET UTR_CARDNO_ENCRYPT = ?  WHERE UTR_NO=?");
		
		SQL_UPDATE_UNTRADE_RECORD.append("UPDATE CCPS_UNNORMAL_TRADERECORD SET UTR_PM_ID=?, UTR_BANK_CODE=?, UTR_CHA_CODE=?,UTR_PAYENDTIME = systimestamp, UTR_ERRORCODE = ?, ");
		SQL_UPDATE_UNTRADE_RECORD.append("UTR_ERRORREASON_OUT = ?, UTR_ERRORREASON_IN  = ?,UTR_RISKINFO=?,UTR_SETSCORE=?,UTR_TOTALSCORE=?,UTR_PASSRISKINFO=?,UTR_RISK_ID=? WHERE UTR_NO = ? ");
		
		SQL_UPDATE_INFORMAL_RECORD.append("UPDATE CCPS_INFORMAL_TRADERECORD SET TTR_PM_ID=?,TTR_STATUS = ?, TTR_BANKDATETIME = systimestamp,");
		SQL_UPDATE_INFORMAL_RECORD.append("TTR_BANKCODE = ?, TTR_BANKINFO_IN = ?, TTR_BANKINFO_OUT = ?,TTR_RISKINFO=?,TTR_SETSCORE=?,TTR_TOTALSCORE=?,TTR_PASSRISKINFO=? WHERE TTR_NO = ?");
		
		SQL_CHECK_TEST_ORDERNO.append("SELECT COUNT(*) FROM CCPS_INFORMAL_TRADERECORD trade ");
		SQL_CHECK_TEST_ORDERNO.append("WHERE trade.TTR_MER_NO=? AND trade.TTR_GW_NO=? ");
		SQL_CHECK_TEST_ORDERNO.append("AND trade.TTR_MER_ORDERNO=? AND trade.TTR_STATUS<>? AND trade.TTR_NO<>?");
		
//		SQL_CHECK_FORMAL_ORDERNO.append("SELECT COUNT(*) FROM CCPS_TRADERECORD trade ");
//		SQL_CHECK_FORMAL_ORDERNO.append("WHERE trade.TR_MER_NO=? AND trade.TR_GW_NO=? ");
//		SQL_CHECK_FORMAL_ORDERNO.append("AND trade.TR_MER_ORDERNO=? AND trade.TR_STATUS<>?");

		SQL_CHECK_FORMAL_ORDERNO .append("SELECT /*+rule*/ ")
							     .append("COUNT(case when trade.TR_STATUS <> ? then 1 end) st_no_fail,")
							     .append("COUNT(case when trade.TR_STATUS = ? then 1 end) st_fail, ")
							     .append("COUNT(case when trade.TR_STATUS = ? then 1 end) st_pending ")
							     .append("FROM CCPS_TRADERECORD trade ")
							     .append("WHERE trade.TR_MER_NO=? AND trade.TR_GW_NO=? ")
							     .append("AND trade.TR_MER_ORDERNO=? ")
							     .append(" AND trade.TR_DATETIME >  (sysdate - ?) ")
							     ;
		
		SQL_CHECK_TRADENO.append("SELECT COUNT(*) FROM CCPS_TRADERECORD trade WHERE trade.TR_NO=?");
		
		SQL_CHECK_GATEWAY_CHANNEL.append("SELECT COUNT(*) FROM CCPS_MER_CHANNEL mercha LEFT JOIN CCPS_CHANNEL cha ON cha.CHA_CODE=mercha.MC_CHA_CODE ");
		SQL_CHECK_GATEWAY_CHANNEL.append("WHERE mercha.MC_STATUS =1 AND cha.CHA_STAUTS=1 AND mercha.MC_MER_NO=? AND mercha.MC_GW_NO=? and mercha.MC_PAYTYPE=1 ");
		
		SQL_GET_GATEWAY_CHANNEL.append("SELECT mercha.MC_CARDTYPE,merbc.MBA_BANKCURRENCY,mercha.MC_TR_RATE,mercha.MC_SE_RATE,mercha.MC_PAYTYPE,mercha.MC_DCC_FLAG ,mercha.MC_DCC_RATE ,mercha.MC_DCC_CHA_CODE ,bank.BANK_ID,bank.BANK_CODE,bank.BANK_PAY_URL,bank.BANK_REQ_URL,bank.BANK_ISDIRECT,");
		SQL_GET_GATEWAY_CHANNEL.append("method.PM_ID,method.PM_NAME,PM_LOGO,PM_URL,PM_CHECK_URL, cha.CHA_MARK,card.CC_TSDATE,card.CC_RSDATE,cha.CHA_3DS_PROVIDER,cha.CHA_MCC,");
		SQL_GET_GATEWAY_CHANNEL.append(" cha.CHA_AE_MERNAME,cha.CHA_FALG,cha.CHA_CODE,cha.CHA_MERNO,cha.CHA_VPC_ACCESSCODE,cha.CHA_VPC_USER, cha.CHA_SECURE_SECRET,cha.CHA_LEAST_AMOUNT,cha.CHA_LEAST_AMOUNT_CURR, card.CC_CHA_RATE, ");
		SQL_GET_GATEWAY_CHANNEL.append("cha.CHA_CURRENCY, cha.CHA_SETTLEMENT_BANK, cha.CHA_ISDCC, cha.CHA_ISDELAY, cha.CHA_THREEPARTY,cha.CHA_ISTHREEPARTY, cha.CHA_PRE_AUTHOR,");
		SQL_GET_GATEWAY_CHANNEL.append("cha.CHA_TWOFIVEPARTY,cha.CHA_VPC_PASSWORD, cha.CHA_TWOPARTY, cha.CHA_FEECURRENCY, cha.CHA_FEEAMOUNT, cha.CHA_FEE_FAIL, cha.CHA_BILLADDRESS, ");
		SQL_GET_GATEWAY_CHANNEL.append("cha.CHA_FEE_SUCCESS, cha.CHA_FEE_SUCCESS_AFTER, cha.CHA_IS_BACK, cha.CHA_IS_BACK_AFTER,cha.CHA_OBLIGATE1, cha.CHA_OBLIGATE2, cha.CHA_IS_3D, rate.MR_TRADE_RATE,");
		SQL_GET_GATEWAY_CHANNEL.append("rate.MR_RESERVER_RATE, rate.MR_FEECURRENCY, rate.MR_FEEAMOUNT,rate.MR_FEEAMOUNT_fail, rate.MR_LOW_FEEAMOUNT, rate.MR_FEE_FAIL, rate.MR_FEE_SUCCESS,");
		SQL_GET_GATEWAY_CHANNEL.append("rate.MR_FEE_SUCCESS_AFTER, rate.MR_IS_BACK, rate.MR_IS_BACK_AFTER,mercha.MC_OPEN_AUTHOR,mercha.MC_AUTO_AUTHOR,mercha.MC_LIMIT_AUTHDAY,cha.CHA_NO3DPAYMENT,card.CC_RSRATE ");
		SQL_GET_GATEWAY_CHANNEL.append("FROM CCPS_MER_CHANNEL mercha LEFT JOIN CCPS_CHANNEL cha ON cha.CHA_CODE=mercha.MC_CHA_CODE ");
		SQL_GET_GATEWAY_CHANNEL.append("LEFT JOIN CCPS_BANK bank  ON cha.CHA_BANK_CODE=bank.BANK_CODE ");
		SQL_GET_GATEWAY_CHANNEL.append("LEFT JOIN CCPS_PAYMENT_METHOD method  ON bank.BANK_PM_ID=method.PM_ID ");
		SQL_GET_GATEWAY_CHANNEL.append("LEFT JOIN CCPS_CHA_CARDTYPE card ON cha.CHA_CODE=card.CC_CHA_CODE ");
		SQL_GET_GATEWAY_CHANNEL.append("LEFT JOIN CCPS_MER_RATE rate ON cha.CHA_CODE=rate.MR_CHA_CODE ");
		SQL_GET_GATEWAY_CHANNEL.append("LEFT JOIN CCPS_MER_BANKACCOUNT merbc ON merbc.MBA_MER_NO=rate.MR_MER_NO  ");
		SQL_GET_GATEWAY_CHANNEL.append("WHERE mercha.MC_CARDTYPE=card.CC_CARDTYPE AND mercha.MC_CARDTYPE=rate.MR_CARDTYPE AND mercha.MC_STATUS =1  AND mercha.MC_PAYTYPE=1" ) ;
		SQL_GET_GATEWAY_CHANNEL.append("AND cha.CHA_STAUTS=1 AND merbc.MBA_ISDEFAULT=1  AND mercha.MC_MER_NO=? AND mercha.MC_GW_NO=? AND rate.MR_MER_NO=? AND rate.MR_GW_NO=? ");
		
		SQL_GET_MERCHANT_AENT.append("SELECT aent.AM_AGENT_NO, aent.AM_RATE, aent.AM_FEE_CURRENCY, aent.AM_FEE, aent.AM_FEE_FAIL,");
		SQL_GET_MERCHANT_AENT.append("aent.AM_FEE_SUCCESS, aent.AM_FEE_SUCCESS_AFTER, aent.AM_IS_BACK, aent.AM_IS_BACK_AFTER ");
		SQL_GET_MERCHANT_AENT.append("FROM CCPS_AGENT_MER aent WHERE aent.AM_MER_NO=? AND aent.AM_GW_NO=?");
	/*	
		SQL_GET_MERCHANT_AENT_NEW.append("SELECT aent.AM_AGENT_NO, aent.AM_RATE, aent.AM_FEE_CURRENCY, aent.AM_FEE, aent.AM_FEE_FAIL,");
		SQL_GET_MERCHANT_AENT_NEW.append("aent.AM_FEE_SUCCESS, aent.AM_FEE_SUCCESS_AFTER, aent.AM_IS_BACK, aent.AM_IS_BACK_AFTER, aent.AM_AGENT_LEVEL, an.AGENT_ACCOUNT_CURRENCY ");
		SQL_GET_MERCHANT_AENT_NEW.append(" FROM CCPS_AGENT_MER_NEW aent,CCPS_AGENT_NEW an WHERE aent.AM_AGENT_NO = an.AGENT_NO AND aent.AM_MER_NO=? AND aent.AM_GW_NO=?");
		*/
		
		SQL_GET_MERCHANT_AENT_NEW.append(" SELECT aent.AM_AGENT_NO, aent.AM_RATE, aent.AM_FEE_CURRENCY, aent.AM_FEE, aent.AM_FEE_FAIL,");
		SQL_GET_MERCHANT_AENT_NEW.append("aent.AM_FEE_SUCCESS, aent.AM_FEE_SUCCESS_AFTER, aent.AM_IS_BACK, aent.AM_IS_BACK_AFTER, aent.AM_AGENT_LEVEL, ab.aba_bankcurrency ");
		SQL_GET_MERCHANT_AENT_NEW.append(" FROM CCPS_AGENT_MER_NEW aent left join CCPS_AGENT_NEW an  on aent.am_agent_no=an.agent_no " +
				"   left join CCPS_AGENT_BANKACCOUNT ab on ab.aba_agent_no=an.agent_no  WHERE  ab.aba_status=1 and ab.aba_isdefault=1  and aent.AM_MER_NO=? AND aent.AM_GW_NO=?");
		
		
		SQL_GET_CURRENCY_RATE.append("SELECT rate.RATE_VALUE,rate.RATE_VALUE1,rate.RATE_VALUE2 FROM CCPS_RATE rate WHERE rate.RATE_ORIGINAL_CURRENCY=? AND rate.RATE_TARGET_CURRENCY=? AND rate.RATE_TYPE=?");
		
		SQL_UPDATE_TEST_RECORD.append("UPDATE CCPS_INFORMAL_TRADERECORD SET TTR_STATUS = ?,TTR_PM_ID=?, TTR_MR_TRADE_RATE = ?, ");
		SQL_UPDATE_TEST_RECORD.append("TTR_MR_RESEVER_RATE = ?,TTR_RATE_VALUE = ?,TTR_BW_BANKCURRENCY = ?,TTR_BW_BANKAMOUT = ?,");
		SQL_UPDATE_TEST_RECORD.append("TTR_BANK_CODE = ?,TTR_CHA_CODE = ?,TTR_RISKINFO=?,TTR_SETSCORE=?,TTR_TOTALSCORE=?,TTR_PASSRISKINFO=?,TTR_BANKDATETIME = systimestamp WHERE TTR_NO = ?");
		
		SQL_DELETE_UNTRADE_RECORD.append("DELETE CCPS_UNNORMAL_TRADERECORD un WHERE un.UTR_NO=?");
		
		SQL_UPDATE_TR_IS_REPAY.append("UPDATE CCPS_TRADERECORD T SET T.TR_IS_REPAY = ? ")
					   		  .append("WHERE T.TR_MER_NO = ? AND T.TR_GW_NO = ? AND T.TR_MER_ORDERNO = ?");
		
		SQL_SAVE_TRADE_RECORD.append("INSERT INTO CCPS_TRADERECORD (TR_ID, TR_NO, TR_MER_ORDERNO, ");
		SQL_SAVE_TRADE_RECORD.append("TR_MER_NO, TR_GW_NO, TR_CURRENCY, TR_AMOUNT, TR_STATUS, TR_TRADE_RATE, ");
		SQL_SAVE_TRADE_RECORD.append("TR_SPP_CURRENCY, TR_SPP,TR_LOW_SPP, TR_AGENT_NO, TR_AGENT_RATE, TR_AGENT_SPP_CURRENCY, TR_AGENT_SPP, TR_RESEVER_RATE, TR_RATE_VALUE, TR_BANKCURRENCY, ");
		SQL_SAVE_TRADE_RECORD.append("TR_BANKAMOUT, TR_BANK_CODE, TR_CHA_CODE, TR_ISDELAY, TR_CHA_RATE,TR_CHA_RESV_RATE, TR_BANK_SPP_CURRENCY, TR_BANK_SPP,");
		SQL_SAVE_TRADE_RECORD.append("TR_CHA_SETT_BANK, TR_PAYSTARTTIME, TR_PAYENDTIME, TR_DATETIME, TR_RETURNURL, ");
		SQL_SAVE_TRADE_RECORD.append("TR_WEBSITE, TR_SUBMITURL, TR_ISDCC, TR_INF_TYPE, TR_FEE_FAIL_MER, TR_FEE_SUCCESS_MER, TR_FEE_SUCCESS_AFTER_MER, TR_IS_BACK_MER, TR_IS_BACK_AFTER_MER,");
		SQL_SAVE_TRADE_RECORD.append("TR_FEE_FAIL_AGENT, TR_FEE_SUCCESS_AGENT, TR_FEE_SUCCESS_AFTER_AGENT, TR_IS_BACK_AGENT, TR_IS_BACK_AFTER_AGENT, TR_FEE_FAIL_CHA, TR_FEE_SUCCESS_CHA, ");
		SQL_SAVE_TRADE_RECORD.append("TR_FEE_SUCCESS_AFTER_CHA, TR_IS_BACK_CHA, TR_IS_BACK_AFTER_CHA, TR_CARDTYPE, TR_REMARK,TR_RISKINFO,TR_SETSCORE,TR_TOTALSCORE,TR_PASSRISKINFO,TR_PM_ID,");
		SQL_SAVE_TRADE_RECORD.append("TR_AUTH_TYPE,TR_NOTIFICATION_URL,TR_RATE_VALUE_FLOAT,TR_SETT_RATE_FLOAT,TR_SETT_CURRENCY,TR_SETT_RATE,TR_RS_RATE) ");
		SQL_SAVE_TRADE_RECORD.append("VALUES (CCPS_TRADERECORD_SEQ.nextval, ?,");
		SQL_SAVE_TRADE_RECORD.append("?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?, ?, ?, ?, ?, ?, ?,?, ?, ?, ?, ?, ?, TO_TIMESTAMP(?,'YYYY-fmMMfm-fmDDfm fmHH24fm:MI:SS.FF'), TO_TIMESTAMP(?,'YYYY-fmMMfm-fmDDfm fmHH24fm:MI:SS.FF'),");
		SQL_SAVE_TRADE_RECORD.append("systimestamp, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,?,?,?,?,?,?,?,?)");
		
		
		SQL_SAVE_TRADE_RECORD_NEW.append("INSERT INTO CCPS_TRADERECORD (TR_ID, TR_NO, TR_MER_ORDERNO, ");
		SQL_SAVE_TRADE_RECORD_NEW.append("TR_MER_NO, TR_GW_NO, TR_CURRENCY, TR_AMOUNT, TR_STATUS, TR_TRADE_RATE, ");
		// 原代理商信息不再保存  TR_AGENT_NO, TR_AGENT_RATE, TR_AGENT_SPP_CURRENCY, TR_AGENT_SPP, 
		SQL_SAVE_TRADE_RECORD_NEW.append("TR_SPP_CURRENCY, TR_SPP,TR_SPP_fail,TR_LOW_SPP, TR_AGENT_NO, TR_AGENT_RATE, TR_AGENT_SPP_CURRENCY, TR_AGENT_SPP, TR_RESEVER_RATE, TR_RATE_VALUE, TR_BANKCURRENCY, ");
		SQL_SAVE_TRADE_RECORD_NEW.append("TR_BANKAMOUT, TR_BANK_CODE, TR_CHA_CODE, TR_ISDELAY, TR_CHA_RATE,TR_CHA_RESV_RATE, TR_BANK_SPP_CURRENCY, TR_BANK_SPP,");
		SQL_SAVE_TRADE_RECORD_NEW.append("TR_CHA_SETT_BANK, TR_PAYSTARTTIME, TR_PAYENDTIME, TR_DATETIME, TR_RETURNURL, ");
		SQL_SAVE_TRADE_RECORD_NEW.append("TR_WEBSITE, TR_SUBMITURL, TR_ISDCC, TR_INF_TYPE, TR_FEE_FAIL_MER, TR_FEE_SUCCESS_MER, TR_FEE_SUCCESS_AFTER_MER, TR_IS_BACK_MER, TR_IS_BACK_AFTER_MER,");
		// 原代理商信息不再保存  TR_FEE_FAIL_AGENT, TR_FEE_SUCCESS_AGENT, TR_FEE_SUCCESS_AFTER_AGENT, TR_IS_BACK_AGENT, TR_IS_BACK_AFTER_AGENT,
		SQL_SAVE_TRADE_RECORD_NEW.append("TR_FEE_FAIL_CHA, TR_FEE_SUCCESS_CHA, TR_FEE_FAIL_AGENT, TR_FEE_SUCCESS_AGENT, TR_FEE_SUCCESS_AFTER_AGENT, TR_IS_BACK_AGENT, TR_IS_BACK_AFTER_AGENT,");
		SQL_SAVE_TRADE_RECORD_NEW.append("TR_FEE_SUCCESS_AFTER_CHA, TR_IS_BACK_CHA, TR_IS_BACK_AFTER_CHA, TR_CARDTYPE, TR_REMARK,TR_RISKINFO,TR_SETSCORE,TR_TOTALSCORE,TR_PASSRISKINFO,TR_PM_ID,");
		SQL_SAVE_TRADE_RECORD_NEW.append("TR_AUTH_TYPE,TR_NOTIFICATION_URL,TR_RATE_VALUE_FLOAT,tr_mer_rate_float,TR_MER_SETT_RATE_FLOAT,TR_SETT_RATE_FLOAT,TR_SETT_CURRENCY,TR_SETT_RATE,TR_RS_RATE,");
		/** 添加新的代理商信息 */
		SQL_SAVE_TRADE_RECORD_NEW.append("TR_AGENT_NO1,TR_AGENT_NO2,TR_AGENT_NO3,TR_AS_CURRENCY1,TR_AS_CURRENCY2,TR_AS_CURRENCY3,TR_AS_RATE1,TR_AS_RATE2,TR_AS_RATE3,TR_AS_STATUS1,TR_AS_STATUS2,TR_AS_STATUS3,TR_B_TO_A,TR_CHA_MARK,TR_UN_RESEVER,TR_BANKTSDATE,TR_BANKRSDATE,TR_FEE_RATE_FLOAT");
		SQL_SAVE_TRADE_RECORD_NEW.append(",TR_3DS_FLAG,TR_DM_FLAG) VALUES (CCPS_TRADERECORD_SEQ.nextval, ?,");
		SQL_SAVE_TRADE_RECORD_NEW.append("?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?, ?, ?, ?, ?, ?,?,?,?,?,?, ?, TO_TIMESTAMP(?,'YYYY-fmMMfm-fmDDfm fmHH24fm:MI:SS.FF'), TO_TIMESTAMP(?,'YYYY-fmMMfm-fmDDfm fmHH24fm:MI:SS.FF'),");
		SQL_SAVE_TRADE_RECORD_NEW.append("systimestamp, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?, ?,?, ?, ?,?,0,0,0,?,?,?,?,?,?,?,?)");
		
		
		SQL_GET_DOMAIN.append("SELECT domain.D_NAME,domain.D_URL,domain.D_IP,mer.MD_ISMOBILE FROM CCPS_DOMAIN domain ");
		SQL_GET_DOMAIN.append("LEFT JOIN CCPS_MER_DOMAIN mer ON mer.MD_D_ID=domain.D_ID WHERE (mer.MD_MER_NO=? AND mer.MD_GW_NO=?) ");
		SQL_GET_DOMAIN.append("OR (mer.MD_MER_NO=? AND mer.MD_GW_NO=0) OR (mer.MD_MER_NO=0 AND mer.MD_GW_NO=0) ORDER BY mer.MD_GW_NO DESC, mer.MD_MER_NO DESC");
		
		SQL_IS_TEST_CARD.append("SELECT COUNT(*) FROM CCPS_TEST_CARD card WHERE card.TC_MER_NO=? AND card.TC_GW_NO=? AND card.TC_CARDNO=?");
		
		SQL_UPDATE_TR_REFERENCE.append("UPDATE CCPS_TRADERECORD SET TR_REFERENCE = ? WHERE TR_NO = ?");
		SQL_UPDATE_TR_REF_AND_QUERYNO.append("UPDATE CCPS_TRADERECORD SET TR_REFERENCE = ?, TR_QUERYNO = ?, TR_BATCHNO = ?, TR_SF_DATA = ? WHERE TR_NO = ?");

		SQL_UPDATE_TR_REFERENCE_AND_TR_BANKORDERNO.append("UPDATE CCPS_TRADERECORD SET TR_REFERENCE = ?, TR_BANKORDERNO = ? WHERE TR_NO = ?");
		
		SQL_CHECK_SERVERIP.append("SELECT COUNT(*) FROM CCPS_MER_CHECKIP ip WHERE ip.MC_TYPE=2 AND ip.MC_IP=? ");
		SQL_CHECK_SERVERIP.append("AND ((ip.MC_MER_NO=? AND ip.MC_GW_NO=?) OR (ip.MC_MER_NO=? AND ip.MC_GW_NO=0))");
		
		  //插入持卡人唯一标识对应的实体
	    SQL_SAVE_CREDITINTO_RECURRING.append("INSERT INTO CCPS_CREDITINFO_RECURRING CCR (CCR.CR_ID,CCR.CR_MER_NO,CCR.CR_CUSTOMER_ID,");
	    SQL_SAVE_CREDITINTO_RECURRING.append("CCR.CR_CREDIT_CARD_ENCRIPT,CCR.CR_SYS_DATE,CCR.CR_FIRST_NAME,CCR.CR_LAST_NAME,CCR.CR_ADDRESS,");    
	    SQL_SAVE_CREDITINTO_RECURRING.append("CCR.CR_CITY,CCR.CR_STATE,CCR.CR_COUNTRY,CCR.CR_ZIPCODE,CCR.CR_BANK,CCR.CR_EMAIL,CCR.CR_PHONE,");        
	    SQL_SAVE_CREDITINTO_RECURRING.append("CCR.CR_INTERFACE_TYPE,CCR.CR_GATE_NO,CCR.CR_TRADE_NO ) VALUES(CCPS_CREDITINFO_RECURRING_SEQ.nextVal,?,?,?,SYSDATE,?,?,?,?,?,?,?,?,?,?,?,?,?)");  
	     
	    //查询持卡人唯一标识对应的实体 
	    SQL_GET_CREDITINTO_RECURRING.append("SELECT CCR.CR_ID,CCR.CR_MER_NO,CCR.CR_CUSTOMER_ID,CCR.CR_CREDIT_CARD_ENCRIPT,CCR.CR_SYS_DATE,");  
	    SQL_GET_CREDITINTO_RECURRING.append("CCR.CR_FIRST_NAME,CCR.CR_LAST_NAME,CCR.CR_ADDRESS,CCR.CR_CITY,CCR.CR_STATE,CCR.CR_COUNTRY,");      
	    SQL_GET_CREDITINTO_RECURRING.append("CCR.CR_ZIPCODE,CCR.CR_BANK,CCR.CR_EMAIL,CCR.CR_PHONE,CCR.CR_INTERFACE_TYPE,CCR.CR_TRADE_NO FROM  CCPS_CREDITINFO_RECURRING CCR");         
	    SQL_GET_CREDITINTO_RECURRING.append(" WHERE CCR.CR_CUSTOMER_ID=? AND CR_GATE_NO=? AND CR_INTERFACE_TYPE=?  ");  
		
		SQL_GET_PAYMENT_METHOD_BY_NAME.append("SELECT p.PM_ID, p.PM_NAME, p.PM_CHECK_URL FROM CCPS_PAYMENT_METHOD p WHERE upper(p.PM_NAME)=upper(?) ");

		//预授权用的的sql
		SQL_GETAUTHINF_TRNO.append("select t.TR_NO, t.tr_mer_orderno, t.TR_BANKAMOUT ,t.tr_datetime,t.TR_BANKCURRENCY,t.TR_BANKORDERNO,t.TR_AUTHORIZELD, t.tr_currency,t.TR_REFERENCE,t.TR_STATUS,t.TR_AUTH_TYPE, t.TR_WEBSITE,t.tr_amount,t.TR_BANKAMOUT,t.tr_bank_code,t.tr_cha_code,t.tr_pm_id ,t.TR_AUTH_TYPE,c.*,b.BANK_REQ_URL,b.BANK_PAY_URL,g.GW_RETURN_MODEL,cr.CI_CARDNO_ENCRYPT,cr.CI_CARDNO_ENCRYPT,cr.CI_CARDEXPIREDATE ");
		SQL_GETAUTHINF_TRNO.append("  FROM CCPS_TRADERECORD T, ccps_creditinfo cr, ccps_channel c ,ccps_bank b   , ccps_gateway g WHERE  1=1 and t.tr_no=cr.CI_TR_NO and t.tr_cha_code=c.cha_code and b.bank_code=t.tr_bank_code and t.tr_gw_no=g.gw_no and t.tr_no= ?");
		SQL_SAVE_AUTH.append("insert into CCPS_AUTH_DEAL(AD_ID,   AD_TR_NO,  AD_MER_NO,  AD_GW_NO,  AD_MERORDER, AD_CURRENCY,  AD_AMOUNT, AD_PAYMETHOD, AD_BANKCODE, AD_CHANNELCODE,  AD_TYPE, AD_FLAG, AD_SINGINFO,  AD_RETURNURL,  AD_INFO_CODE, AD_IN_INFO, AD_OUT_INFO, AD_OPTIME, AD_REMREK)");
		SQL_SAVE_AUTH.append("    values(CCPS_AUTH_DEAL_SEQ.nextval,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?, TO_TIMESTAMP(?,'YYYY-fmMMfm-fmDDfm fmHH24fm:MI:SS.FF'),? )");
		SQL_UPDATE_TRADE_AUTH.append("   UPDATE CCPS_TRADERECORD SET TR_STATUS=?, TR_AUTH_TYPE=?,TR_BANKDATETIME=systimestamp WHERE TR_NO=? ");
		SQL_UPDATE_TRADE_AUTH2.append("   UPDATE CCPS_TRADERECORD SET TR_AUTH_TYPE=?   WHERE TR_NO=? "); 
		SQL_UPDATE_TRADE_AUTH3.append("   UPDATE CCPS_TRADERECORD SET TR_STATUS=?, TR_AUTH_TYPE=?, TR_QUERYNO=?, TR_AUTH_OP_TIME=systimestamp,TR_AUTHORIZELD=?,TR_BATCHNO=? WHERE TR_NO=? ");
		SQL_SAVE_UN_AUTH.append("INSERT INTO CCPS_AUTH_DEAL (AD_ID,AD_TR_NO,AD_MER_NO,AD_GW_NO,AD_TYPE,AD_FLAG,AD_SINGINFO,AD_RETURNURL,AD_INFO_CODE,AD_IN_INFO,AD_OUT_INFO,AD_OPTIME,AD_REMREK)");
		SQL_SAVE_UN_AUTH.append(" VALUES (CCPS_AUTH_DEAL_SEQ.nextval,?,?,?,?,?,?,?,?,?,?,TO_TIMESTAMP(?,'YYYY-fmMMfm-fmDDfm fmHH24fm:MI:SS.FF'),?)");
		SQL_GETTRADEINFO.append(" SELECT TR_BANK_CODE ,TR_CHA_CODE ,TR_PM_ID,C.CI_CARDTYPE,to_char(t.tr_datetime,'yyyy-mm-dd hh24:mi:ss') TR_DATETIME FROM CCPS_TRADERECORD T,CCPS_CREDITINFO C ");
		SQL_GETTRADEINFO.append(" WHERE T.TR_NO=C.CI_TR_NO AND T.TR_NO=? ");
		SQL_GETAUTHDAY.append(" SELECT  MC_LIMIT_AUTHDAY  from ccps_mer_channel where MC_MER_NO= ? and MC_GW_NO =? and MC_BANK_CODE =? and MC_CHA_CODE=? and MC_CARDTYPE =? and MC_PM_ID=?  "); 
		SQL_AUTH_REFNO_CX.append("SELECT  CDI_SYS_STRACE,CDI_REF_NO FROM CCPS_CHANNEL_DIFFINFO WHERE CDI_TR_NO =?");
		SQL_AUTH_NO_COM.append("SELECT  TR_AUTHORIZELD FROM CCPS_TRADERECORD WHERE TR_NO =?");
		
		
		SQL_UPDATE_UTR_PAYTIME.append("UPDATE CCPS_UNNORMAL_TRADERECORD SET UTR_PAYTIME = systimestamp WHERE UTR_NO = ?");
		
		SQL_UPDATE_CREADIT.append("UPDATE CCPS_CREDITINFO SET CI_EMAIL=?,CI_TEL=?,CI_FIRSTNAME=?,CI_LASTNAME=?,CI_COUNTRY=?,CI_STATE=?,CI_CITY=?,CI_ZIPCODE=?,CI_CELLPNONE=?,CI_ADDRESS=?,CI_IPADDRESS=?,");
		SQL_UPDATE_CREADIT.append("CI_IPCOUNTRY=?,CI_MER_IPADDRESS=?,CI_ISSUINGBANK=?,CI_ISSUINGBANKTEL=?,CI_CARDCOUNTRY=?,CI_FROMNAME=?,CI_ACQUIRER=?,CI_ISSENDEMAIL=?,CI_OS=?,CI_BROWER=?,");
		SQL_UPDATE_CREADIT.append("CI_LANG=?,CI_TIMEZONE=?,CI_RESOLUTION=?,CI_COOKIEID=?,CI_OLD_COOKIE=?,CI_COOKIE_FLAG=?,CI_SHA256=?,CI_CARDNOPART=?,CI_CARDTYPE=?,CI_REMARK=?,CI_PAYMENT_METHOD=?,");
		SQL_UPDATE_CREADIT.append("CI_EBANX_NAME=?,CI_EBANX_EMAIL=?,CI_EBANX_TYPE=?,CI_EBANXCPF = ?,CI_QIWI_USER=?,CI_PPRO_ACCOUNT_NUMBER=?,CI_PPRO_BANKCODE=?,CI_CARDNO_ENCRYPT=? , CI_QIWI_COUNTRYCODE =? ,CI_CARDEXPIREDATE=? ,CI_CLIENT_ID=? " +
				",CI_SHIPFIRSTNAME=?,CI_SHIPLASTNAME=?,CI_SHIPCOUNTRY=?,CI_SHIPSTATE=?,CI_SHIPCITY=?,CI_SHIPADDRESS=?,CI_SHIPZIP=?,CI_PRODUCT_NAME=?,CI_PRODUCT_NUM=?,CI_PRODUCT_DESC=?,CI_EXT1=?,CI_EXT2=?,ci_TEMP=? WHERE CI_TR_NO=?");
		SQL_QUERY_CREADIT_BYTRNO.append(" SELECT * FROM  ccps_creditinfo WHERE 1=1 AND  CI_TR_NO= ? ");
		
		SQL_QUERY_CREADIT_TEMP_BYTRNO.append(" SELECT CI_TEMP FROM  ccps_creditinfo WHERE 1=1 AND  CI_TR_NO= ? ");
		
		SQL_QUERY_SYSSET.append(" select s.ss_para_name, s.ss_para_value from sys_sysset s");
		SQL_QUERY_BUSINESS_PARAM.append(" select t.sb_para_code, t.sb_para_value from sys_business_param t ");
		
		//渣打预授权
		SQL_GETSCBAUTHINF_TRNO.append("select  SCB_UNIQUENUMBER ,SCB_ORDERID,SCB_REFERCENO, SCB_XID ,SCB_IS3D,SCB_SECURITYLEVEL ,SCB_CAV ");
		SQL_GETSCBAUTHINF_TRNO.append("  FROM  ccps_scb_function f WHERE  1=1 and f.scb_tr_no= ? and f.scb_type=? ");
		
		SQL_QUERY_REPAY.append("select c.tr_no from ccps_traderecord c left join ccps_creditinfo cf on c.tr_no = cf.ci_tr_no  where c.tr_status = 1 and c.tr_gw_no = ? and c.tr_amount = ? and c.tr_currency = ? ");
		SQL_QUERY_REPAY.append(" and c.tr_datetime > (sysdate -?/24) and (cf.ci_ipaddress = ? or cf.ci_email =?)");
	
		SQL_TELESIGN_SAVE.append("insert into ccps_telesign (tel_id,tel_tradeno,tel_datetime,tel_type,tel_checkcode,tel_phone,tel_country_telcode,");
		SQL_TELESIGN_SAVE.append(" tel_country_code,tel_return_status,tel_refferid,tel_description,tel_errorcode,tel_errorinfo,tel_lang,tel_extension,tel_remark)");
		SQL_TELESIGN_SAVE.append(" values(ccps_telesign_seq.nextval, ?,sysdate,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		
		SQL_TELESIGN_QUERY.append(" select tel.tel_tradeno, tel.tel_type, tel.tel_checkcode from ccps_telesign tel where tel.tel_tradeno = ? and tel.tel_type = ?");
	
		SQL_SAVE_PAGEINFO.append("insert into ccps_paypage_info(page_id, page_tr_no, page_datetime, page_starttime, page_endtime) values(ccps_paypageinfo_seq.nextval, ? ,sysdate, TO_TIMESTAMP(?,'YYYY-fmMMfm-fmDDfm fmHH24fm:MI:SS.FF') , TO_TIMESTAMP(?,'YYYY-fmMMfm-fmDDfm fmHH24fm:MI:SS.FF'))");
	
		SQL_GET_UNTRADERECORD.append("select un.utr_id,un.utr_no,un.utr_mer_no,un.utr_gw_no,un.utr_mer_orderno,un.utr_currency,un.utr_amount,un.utr_returnurl,un.utr_website,un.utr_submiturl,un.utr_paystarttime ");
		SQL_GET_UNTRADERECORD.append(" from ccps_unnormal_traderecord un where un.utr_no = ?");
		
		SQL_GET_TRADERECORD.append("select t.tr_no,t.tr_status,t.tr_mer_orderno,t.tr_currency,t.tr_amount, t.tr_mer_no, t.tr_gw_no ,t.tr_returnurl from ccps_traderecord t where t.tr_no = ?");
	
		SQL_GET_TRADE_ONEMINITE.append("SELECT COUNT(1) TRPAYTIMES FROM ( SELECT T.TR_NO  FROM CCPS_TRADERECORD T WHERE T.TR_MER_NO = ? AND T.TR_GW_NO = ? AND T.TR_DATETIME > SYSDATE - ?/(24*60)");
		SQL_GET_TRADE_ONEMINITE.append(" UNION ALL SELECT UN.UTR_NO FROM CCPS_UNNORMAL_TRADERECORD UN WHERE UN.UTR_MER_NO = ? AND UN.UTR_GW_NO = ? AND UN.UTR_PAYSTARTTIME > SYSDATE - ?/(24*60))");
	
		SQL_GET_TESTTRADE_ONEMINITE.append("SELECT COUNT(IRT.TTR_NO) TESTPAYTIMES FROM CCPS_INFORMAL_TRADERECORD IRT WHERE IRT.TTR_MER_NO = ? AND IRT.TTR_GW_NO = ? AND IRT.TTR_DATETIME > SYSDATE - ?/(24*60) ");
		
		SQL_SAVE_TERMINAL.append("insert into ccps_terminal (tl_id, tl_tradeno, tl_datetme, tl_resolution, tl_version, tl_os, tl_tmf, tl_language, tl_dpi, tl_cookievalue, tl_remark, TL_IP)");
		SQL_SAVE_TERMINAL.append(" values(CCPS_TERMINAL_SEQ.Nextval, ?, sysdate,?, ?, ?, ?, ?, ?, ?,?,?)");
		
		SQL_GET_DCCCHA_INFO.append("select CHA_MERNO,CHA_VPC_ACCESSCODE,CHA_SECURE_SECRET,B.BANK_PAY_URL from ccps_channel cl LEFT JOIN CCPS_BANK B  ON CL.CHA_BANK_CODE=B.BANK_CODE where cl.cha_code= ?");
		SQL_UPDATE_DCCCHA_TRADE.append("  UPDATE CCPS_TRADERECORD  SET   TR_RATE_VALUE=? ,TR_BANKAMOUT=?,TR_DCCRATE_FLAG=? where TR_NO=? ");
		SQL_UPDATE_DCCCHA_CRIDITE.append(" UPDATE CCPS_CREDITINFO C SET  C.CI_DCC_CURRENCY=? WHERE C.CI_TR_NO=? ");

		//查询使用的参数名称
		SQL_GET_INTERFACE_PARAM.append("select i.* from ccps_interface_paramname i where i.ipn_merno = ?");
		
		//保存退款信息
		SQL_SAVE_REFUND.append(" insert into ccps_unnormal_process (up_id, up_tr_no,up_submitby,up_type,up_currency,up_amount,up_reason,up_remark,up_status,up_login_name,up_oprtime,UP_ISFIRST,UP_ISFIRST_AGENT)");
		SQL_SAVE_REFUND.append(" values(?,?, ?,?,?, ?, ?,?, ?, ? ,sysdate ,?,?)");
		//查询网关信息
		SQL_GET_GATEWAY.append("select gt.gw_mer_no, gt.gw_no, gt.gw_md5key, gt.gw_status from ccps_gateway gt where gt.gw_no = ?");
		//查询订单信息
		SQL_GET_TRADE_REFUND.append("select t.tr_no,t.tr_mer_no,t.tr_gw_no,t.tr_datetime,t.tr_amount,t.tr_currency,t.tr_status,t.tr_congeal,t.tr_rs_status,t.tr_ts_status,t.tr_ts_id, t.tr_as_id,t.tr_as_status,t.tr_checked,t.TR_REFUNDMENT_AMOUNT,");
		SQL_GET_TRADE_REFUND.append("t.TR_PROTEST_AMOUNT,t.TR_CONGEAL_AMOUNT,c.cha_part,t.TR_BANK_CODE from ccps_traderecord t ");
		SQL_GET_TRADE_REFUND.append(" left join ccps_channel c on t.tr_cha_code = c.cha_code where t.tr_no = ? and t.tr_mer_no = ? and t.tr_gw_no = ? ");
		//退款批次号
		SQL_GET_BATCHNO.append("SELECT CCPS_UNNORMAL_PROCESS_SEQ.NEXTVAL FROM DUAL");
		//查询异常交易表
		SQL_GET_PROCESSTOTAL.append("select a.UP_CURRENCY, sum(a.UP_AMOUNT) as amount from CCPS_UNNORMAL_PROCESS  a where (a.UP_STATUS=0 or a.UP_STATUS=1) and a.UP_TYPE in(1,2,3,5,6)  and a.UP_TR_NO=?");
		SQL_GET_PROCESSTOTAL.append(" group by a.UP_CURRENCY ");
		//查询划款状态
		SQL_GET_TRADESETTLEMENT.append("SELECT a.TS_STATUS FROM CCPS_TRAN_SETTLEMENT a WHERE a.TS_ID=? ");
		SQL_PROCESS_SETTLE.append("SELECT a.UP_TS_ID,a.UP_SETTLEMENT_STATUS,a.UP_AS_ID,a.UP_AGENT_STATUS FROM CCPS_UNNORMAL_PROCESS a WHERE a.UP_TR_NO=?");
		SQL_GET_AGENTSETTLEMENT.append("SELECT a.AS_STATUS FROM CCPS_AGENT_SETTLEMENT a WHERE a.AS_ID=? ");
		//插入退款详情表
	    SLQ_SCB_INSERT_DETAIL.append("insert into ccps_refund_detail (re_id, re_merno, re_gwno, re_merorderno, re_trno, re_bankcurrency, ");
	    SLQ_SCB_INSERT_DETAIL.append("re_bankamount, re_amountcurrency, re_tramount, re_paybank, re_trtime, re_refundtime, re_refundamount_tr, re_refundamount_rp, ");
	    SLQ_SCB_INSERT_DETAIL.append("re_rate, re_refundstatus, re_bankinfo, re_refundreason, re_operator, re_terminalno, re_accessno, re_bankorderno, re_bankmerno, re_remark, ");
	    SLQ_SCB_INSERT_DETAIL.append("re_up_id, re_bank_status) values(ccps_refund_detail_seq.nextval, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, ?, ?, ?, ?, ?, ?, 'system', '', '', ?, '', ?, ?, '')");
	    
	    SLQ_SCB_INSERT_FUNCTION.append("insert into ccps_scb_function (scb_id, scb_tr_no, scb_acs_return_url, scb_uniquenumber, scb_orderid, scb_is3d, scb_referceno, scb_xid, scb_securitylevel,");
	    SLQ_SCB_INSERT_FUNCTION.append("scb_cav, scb_time, scb_type, scb_authno, scb_sigverstatus, scb_paresstatus) values (ccps_scbfunction_seq.nextval, ?, '', ?, '', ?, ?, '', '', '', systimestamp, 5, ?, '', '')");
	    //商户勾兑IP
	    SQL_CHECK_IP.append("SELECT COUNT(*) FROM CCPS_MER_CHECKIP ip WHERE ip.MC_TYPE=1 AND ip.MC_IP=? ");
		SQL_CHECK_IP.append("AND ((ip.MC_MER_NO=? AND ip.MC_GW_NO=?) OR (ip.MC_MER_NO=? AND ip.MC_GW_NO=0))");
		
		SQL_GET_TEL_VALIDATION_CARDTYPE.append(" select t.tv_cardtype from ccps_mer_tel_validation t where t.tv_validated = 1 and t.tv_gw_no = ? ");
		
		SQL_GET_SYS_PARAM_VALUE.append(" select s.ss_para_value from sys_sysset s where s.ss_para_name = ? ");

	}
}
