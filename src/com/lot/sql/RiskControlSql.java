package com.lot.sql;

/**
 * 
 * <p>Title: </p>
 * <p>Description: 保存风控sql语句</p>
 * <p>Copyright: Copyright (c) 2011 版权</p>
 * <p>Company: </p>
 * <AUTHOR>
 * @version V1.0 
 * @date 2011-7-7下午05:27:04
 */
public class RiskControlSql {
	
	//根据网关接入号获取风控信息
	public static final StringBuffer SQL_GET_MER_RISKLIST = new StringBuffer();
	
	//查询商户号是否绑定来源网址
	public static final StringBuffer SQL_GET_COUNT_BY_MER_WEBSIT = new StringBuffer();
	
	//根据卡号查询卡BIN
	public static final StringBuffer SQL_GET_CARDNOBIN_BY_CARD = new StringBuffer();
	
	public static final StringBuffer SQL_GET_CARDNOBIN_BY_CARD_RISK = new StringBuffer();
	
	//获取黑名单的值
	public static final StringBuffer SQL_GET_BACKLIST = new StringBuffer();
	
	//获取特殊黑名单的值
	public static final StringBuffer SQL_GET_SPECIAL_BACKLIST = new StringBuffer();
	public static final StringBuffer SQL_GET_SPECIAL_BACKLIST_AES = new StringBuffer();
	//必过黑名单
	public static final StringBuffer SQL_GET_MUST_BACKLIST = new StringBuffer();
	//必过黑名单 查询17 电话关键字 18地址关键字
	public static final StringBuffer SQL_GET_MUST_BACKLIST1 = new StringBuffer();
	
	public static final StringBuffer SQL_GET_MER_BACKLIST = new StringBuffer();
	
	public static final StringBuffer SQL_GET_MER_BACKLIST_AES = new StringBuffer();
	
	public static final StringBuffer SQL_GET_MER_BACKLIST1 = new StringBuffer();
	public static final StringBuffer SQL_GET_SPECIAL_BACKLIST3 = new StringBuffer();
	
	public static final StringBuffer SQL_GET_COOKIE = new StringBuffer();

	//获取maxmind的设置
	public static final StringBuffer SQL_GET_MAXMIND_CONTROL = new StringBuffer();
	
	//获取币种限定的设置
	public static final StringBuffer SQL_GET_TRADE_CURRENCY = new StringBuffer();
	
	
	//获取IPcity限定的设置
	public static final StringBuffer SQL_GET_IPCITY= new StringBuffer();
	
	//保存maxmind的输出
	public static final StringBuffer SQL_SAVE_MAXMIND_OUTPUTS = new StringBuffer();
	
	//获取对网关接入号的金额限定
	public static final StringBuffer SQL_GET_AMOUNTLIMIT = new StringBuffer();
	
	//获取所有的币种的交易汇率
	public static final StringBuffer SQL_GET_ALL_TRADE_RATE = new StringBuffer();
	
	//根据周期获取金额
	public static final StringBuffer SQL_GET_AMOUNT_BY_CYCLE = new StringBuffer();
	
	//根据 IP值获取IP所在国家
	 public static final StringBuffer SQL_GET_IPCOUNTRY_BY_IPVALUE = new StringBuffer();
	
	//根据商户号获取IP白名单设置
	public static final StringBuffer SQL_GET_IPWHITE_LIST = new StringBuffer();
	
	//根据商户号获取收件人国家白名单设置
	public static final StringBuffer SQL_GET_SHIPCOUNTRYWHITE_LIST = new StringBuffer();
	
	//根据商户号获取收件人国家白名单设置网关接入号为0
	public static final StringBuffer SQL_GET_SHIPCOUNTRYWHITE_LIST_ALL = new StringBuffer();
	
	
	//根据商户号获取IP白名单设置网关接入号为0
	public static final StringBuffer SQL_GET_IPWHITE_LIST_ALL = new StringBuffer();
	
	//根据IP所在国家判断是否在
	public static final StringBuffer SQL_ISBLACKLIST_BYIPCOUNTRY = new StringBuffer();
	
	public static final StringBuffer SQL_ISBLACKLIST_BYIPCOUNTRY_CARDTYPE = new StringBuffer();
	
	//根据收件人国家判断是否在
	public static final StringBuffer SQL_ISBLACKLIST_BYSHIPCOUNTRY = new StringBuffer();
	
	
	//判断是否有国家限定的黑名单的设置
	public static final StringBuffer SQL_ISBLACKLIST_BYIPCOUNTRY_SET = new StringBuffer();
	
	//根据IP判断是否在IP段黑名单中
	public static final StringBuffer SQL_ISBLACKPART_BYIP = new StringBuffer();
	
	//根据商户网关接入号获取绑定的阻止规则（支付次数）
	public static final StringBuffer SQL_GET_PAYNUM = new StringBuffer();
	public static final StringBuffer SQL_GET_PAYNUM1 = new StringBuffer();
	//根据商户网关接入号获取绑定的阻止规则获取元素
	public static final StringBuffer SQL_GET_ELEMENT_BY_PAYNUM = new StringBuffer();
	
	//根据阻止规则获取阻止支付次数
	public static final StringBuffer SQL_GET_PAYNUM_BY_EMELENT = new StringBuffer();
	
	//获取设定的发卡行国家限定
	public static final StringBuffer SQL_GET_BINCOUNTRY = new StringBuffer();
	

	//获取maxmind返回信息表
	public static final StringBuffer SQL_GET_CCPS_MAXMIND_OUTPUTS = new StringBuffer();
	
	//Cybersource风控处理
	//查询接口附加参数
	public static final StringBuffer SQL_GET_EXTRA_TRADERECORD = new StringBuffer();
	
	
	/**
	 * 風控修改 增加14條風控分數設置查詢語句
	 */
	//查詢商户风控总分数
	public static final StringBuffer SQL_GET_TOTALSCORE = new StringBuffer();
	
	
	
	//查询商户号注册时间信息
	public static final StringBuffer SQL_GET_REGISTERDAY = new StringBuffer();
	
	//
	public static final StringBuffer SQL_INSERT_CREDITINFOLOG = new StringBuffer();
	
	
	//获取到cardbin信息
	public static final StringBuffer SQL_QUERY_MER_RISK_CARDBIN = new StringBuffer();
	
	//查询是否是黑名单
	public static final StringBuffer SQL_QUERY_BALCKLIST =new StringBuffer();
	public static final StringBuffer SQL_QUERY_MERBALCKLIST =new StringBuffer();
	
	//根据 IP值获取IP所在城市
	 public static final StringBuffer SQL_GET_IPCITY_BY_IPVALUE = new StringBuffer();
	 
	 //根据商户传过来的信息判断是否活跃玩家
	 public static final StringBuffer SQL_GET_IS_ACTIVE_CHECK = new StringBuffer();
	 
	 public static final StringBuffer SQL_QUERY_CARDNO = new StringBuffer();
	 public static final StringBuffer SQL_QUERY_WHITE_COUNT = new StringBuffer();
	 
	 public static final StringBuffer SQL_QUERY_BLACKLIST = new StringBuffer();
	
	 public static final StringBuffer SQL_QUERY_CITYBLACKLIST = new StringBuffer();

	 public static final StringBuffer SQL_SAVE_BLACKLIST = new StringBuffer();

	public static final StringBuffer SQL_QUERY_TELESIGN = new StringBuffer();
	
	public static final StringBuffer SQL_QUERY_TEL_VALIDATION = new StringBuffer();

	public static final StringBuffer SQL_GET_MAIL_MANYCARD = new StringBuffer();

	public static final StringBuffer SQL_GET_HIGHRISKBLACK_LIST = new StringBuffer();
	
	
	public static final StringBuffer SQL_GET_IPCOUNTRYCHANNLE = new StringBuffer();
	
	public static final StringBuffer SQL_GET_CARDBINCHANNLE = new StringBuffer();
	
	public static final StringBuffer SQL_GET_CHA_RATE = new StringBuffer();
	
	
	public static final StringBuffer SQL_SAVE_THREATMETRIX = new StringBuffer();
	
	//获取持卡人交易通道限定
	public static final StringBuffer SQL_GET_CHANNELLIMIT = new StringBuffer();
	//获取持卡人支付次数限定
	public static final StringBuffer SQL_GET_CIPAYNUMLIMIT = new StringBuffer();
	
	
	public static final StringBuffer SQL_GET_COUNTY_LIMIT_NEW =new StringBuffer();
	public static final StringBuffer SQL_GET_COUNTY_LIMIT_NEW1 =new StringBuffer();
	
	public static final StringBuffer SQL_GET_GROUP_BLACKLIST =new StringBuffer();
	
	public static final StringBuffer SQL_GET_BUSSNIESS_BLACKLIST =new StringBuffer();
	static {
		
		SQL_QUERY_MERBALCKLIST.append("select count(*)  as cnt from ccps_mer_blacklist mb where mb.mb_type=1 and mb.mb_key=20 and mb.mb_kind=0 and (( mb.mb_merno=0 and mb.mb_gwno=0 and mb.card_type=0) or ( mb.mb_merno=0 and mb.mb_gwno=0 and mb.card_type=?)" +
				"   or ( mb.mb_merno=? and mb.mb_gwno=0 and mb.card_type=0) or ( mb.mb_merno=? and mb.mb_gwno=? and mb.card_type=0) or ( mb.mb_merno=? and mb.mb_gwno=0 and mb.card_type=?) or ( mb.mb_merno=? and mb.mb_gwno=? and mb.card_type=?) )  and ( upper(mb.mb_value)=? or upper(mb.mb_value)=? ) ");
		
		SQL_GET_GROUP_BLACKLIST.append("select gb.gb_type,gb.gb_group,gb.gb_key from CCPS_GROUP_BLACKLIST gb left join ccps_gateway g on g.gw_group=gb.gb_group " +
				" where  g.gw_group is not null  and gb.gb_type=2   and g.gw_no=? and (gb.gb_card_type=0 or gb.gb_card_type=?)" +
				" and ( (gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?) or (gb.gb_key=? and gb.gb_value=?) or (gb.gb_key=? and gb.gb_value=?) or (gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?) " +
				" or ( gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?)  )");
		
		SQL_GET_BUSSNIESS_BLACKLIST.append("select gb.gb_type,gb.gb_group,gb.gb_key from CCPS_GROUP_BLACKLIST gb left join ccps_gateway g on g.gw_group2=gb.gb_group " +
				" where  g.gw_group2 is not null  and gb.gb_type=1   and g.gw_no=? and (gb.gb_card_type=0 or gb.gb_card_type=?)" +
				" and ( (gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?) or (gb.gb_key=? and gb.gb_value=?) or (gb.gb_key=? and gb.gb_value=?) or (gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?)  " +
				"  or ( gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?) or ( gb.gb_key=? and gb.gb_value=?) )");
		
		SQL_GET_CARDBINCHANNLE.append(" SELECT B.BANK_CODE,B.BANK_PAY_URL,B.BANK_REQ_URL,B.BANK_ISDIRECT,C.CHA_CODE, IC.IC_CARDTYPE, c.CHA_CURRENCY," +
				"IC.IC_RATE,B.BANK_ID,C.CHA_MERNO,C.CHA_VPC_ACCESSCODE,C.CHA_SECURE_SECRET,C.CHA_VPC_USER,C.CHA_VPC_PASSWORD, c.cha_ae_mername,c.cha_billaddress " +
				" FROM CCPS_IPCOUNTRY_CHANNLE IC left join CCPS_CHANNEL C ON C.CHA_CODE=IC.IC_CHACODE " +
				" LEFT JOIN CCPS_BANK B ON B.BANK_CODE = IC.IC_BANKCODE" +
				" WHERE IC.IC_MERNO=? AND IC.IC_GWNO=?  AND IC.IC_CARDTYPE = ? and IC.IC_COUNTRY_TYPE=? and IC.IC_STATUS=1 ");
		
		SQL_GET_IPCOUNTRYCHANNLE.append(" SELECT B.BANK_CODE,B.BANK_PAY_URL,B.BANK_REQ_URL,B.BANK_ISDIRECT,C.CHA_CODE, IC.IC_CARDTYPE, c.CHA_CURRENCY,c.CHA_MARK," +
				"IC.IC_RATE,B.BANK_ID,C.CHA_MERNO,C.CHA_VPC_ACCESSCODE,C.CHA_SECURE_SECRET,C.CHA_VPC_USER,C.CHA_VPC_PASSWORD, c.cha_ae_mername,c.cha_billaddress " +
				" FROM CCPS_IPCOUNTRY_CHANNLE IC left join CCPS_CHANNEL C ON C.CHA_CODE=IC.IC_CHACODE " +
				" LEFT JOIN CCPS_BANK B ON B.BANK_CODE = IC.IC_BANKCODE" +
				" WHERE IC.IC_MERNO=? AND IC.IC_GWNO=? AND IC.IC_CARDTYPE = ?  and IC.IC_COUNTRY_TYPE=? and IC.IC_STATUS=1 ");
		
		SQL_GET_TOTALSCORE.append("select MT_SCORE,MT_IS_AUTHORIZE,MT_AUTHORIZE_SCORE  from CCPS_MER_TOTALSCORE  where ((MT_MER_NO =? and MT_GW_NO=?) or (MT_MER_NO =? and MT_GW_NO=0 )) ORDER BY MT_MER_NO DESC, MT_GW_NO DESC ");
	/*	SQL_GET_MAXMINDSCORE.append("select MS_MAXMIND_SCORE,MS_SCORE  from CCPS_MER_MAXMINDSCORE  where MS_MER_NO =? and MS_GW_NO=? ");
		SQL_GET_IPSCORE.append("select MI_SCORE  from CCPS_MER_IPSCORE  where MI_MER_NO =? and MI_GW_NO=? ");
		SQL_GET_COOKIESCORE.append("select MC_SCORE  from CCPS_MER_COOKIESCORE  where MC_MER_NO =? and MC_GW_NO=? ");
		SQL_GET_BINSCORE.append("select MBS_SCORE  from CCPS_MER_BINSCORE  where MBS_MER_NO =? and MBS_GW_NO=? ");
		SQL_GET_BLACKSCORE.append("select MB_SCORE  from CCPS_MER_BLACKSCORE  where MB_MER_NO =? and MB_GW_NO=? ");
		SQL_GET_PROXYSCORE.append("select MP_SCORE  from CCPS_MER_PROXYSCORE  where MP_MER_NO =? and MP_GW_NO=? ");
		SQL_GET_USESCORE.append("select MU_SCORE  from CCPS_MER_USESCORE  where MU_MER_NO =? and MU_GW_NO=? ");
		SQL_GET_COUSCORE.append("select MCS_DISTANCE,MCS_SCORE  from CCPS_MER_COUSCORE  where MCS_MER_NO =? and MCS_GW_NO=? ");
		SQL_GET_PAYSCORE.append("select MPS_HOUR,MPS_MINSECOND,MPS_SUC_COUNT,MPS_SCORE  from CCPS_MER_PAYSCORE  where MPS_MER_NO =? and MPS_GW_NO=? ");
		SQL_GET_BANKCOUSCORE.append("select MBC_SCORE  from CCPS_MER_BANKCOUSCORE  where MBC_MER_NO =? and MBC_GW_NO=? ");
		SQL_GET_IPCOUSCORE.append("select MIC_SCORE  from CCPS_MER_IPCOUSCORE  where MIC_MER_NO =? and MIC_GW_NO=? ");
		SQL_GET_WEBSITESCORE.append("select MWS_SCORE from CCPS_MER_WEBSITESCORE  where MWS_MER_NO =? and MWS_GW_NO=? ");
		SQL_GET_MONEYSCORE.append("select MMS_SCORE from CCPS_MER_MONEYSCORE  where MMS_MER_NO =? and MMS_GW_NO=? ");
		*/
		
		SQL_GET_MER_RISKLIST.append("SELECT risk.RE_ELEMENT_NAME_F,risk.RE_ELEMENT_NAME, risk.RE_METHOD_NAME, risk.RE_URL, risk.RE_POSITION,risk.RE_CODE,mer.MR_ISPASS,mer.MR_SCORE FROM CCPS_MER_RISKELMENT mer ");
		SQL_GET_MER_RISKLIST.append("LEFT JOIN CCPS_RISK_ELEMENT risk ON mer.MR_RE_ID = risk.RE_ID WHERE mer.MR_MER_NO=? AND mer.MR_GW_NO=? ORDER BY risk.RE_ORDER");
		
		SQL_GET_COUNT_BY_MER_WEBSIT.append("SELECT web.WL_WEBSIT FROM CCPS_MER_WEBSIT_LIMIT web ");
		SQL_GET_COUNT_BY_MER_WEBSIT.append("WHERE web.WL_STATUS=2 AND ((web.WL_MER_NO=? AND web.WL_GW_NO=?) ");	
		SQL_GET_COUNT_BY_MER_WEBSIT.append("OR (web.WL_MER_NO=? AND web.WL_GW_NO='0'))");
		
		SQL_GET_CARDNOBIN_BY_CARD.append("SELECT bin.CB_ID FROM CCPS_CARDNO_BIN bin WHERE bin.CB_CARDTYPE=? AND substr(bin.CB_START_CARDNO, 1,11)<=? AND substr(bin.CB_END_CARDNO, 1,11)>=? and cb_bintype=1 ");
		
		SQL_GET_CARDNOBIN_BY_CARD_RISK.append("SELECT bin.CB_ID FROM CCPS_CARDNO_BIN bin WHERE  bin.CB_CARDTYPE=? AND substr(bin.CB_START_CARDNO, 1,11)<=? AND substr(bin.CB_END_CARDNO, 1,11)>=? and cb_bintype=2 ");
		
		SQL_GET_BACKLIST.append("SELECT black.BV_BE_ID FROM CCPS_BLACKLIST black WHERE black.BV_STATUS=1 AND black.BV_KIND = ? AND ((upper(black.BV_VALUE)=? and black.bv_be_id=?)  OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) ");
		SQL_GET_BACKLIST.append(" OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) )");

		SQL_GET_SPECIAL_BACKLIST3.append(" SELECT black.BV_BE_ID,black.bv_sendsms  FROM CCPS_BLACKLIST black WHERE black.BV_STATUS=1  AND ( black.BV_VALUE=? or upper(black.BV_VALUE)=?) and black.bv_be_id=? ");
		
		SQL_GET_COOKIE.append(" SELECT black.BV_BE_ID,black.bv_sendsms  FROM CCPS_BLACKLIST black WHERE black.BV_STATUS=1  AND ( upper(black.BV_VALUE)=? or black.BV_VALUE=? ) and black.bv_be_id=? ");
		
		SQL_GET_SPECIAL_BACKLIST.append("SELECT black.bv_relationship,black.bv_bank_code, black.bv_bintype,black.bv_id,black.BV_BE_ID,black.bv_sendsms,black.BV_VALUE,black.BV_REMARK,black.BV_ISREPENDING FROM CCPS_BLACKLIST black WHERE black.BV_STATUS=1 AND black.BV_KIND = ? AND ((upper(black.BV_VALUE)=? and black.bv_be_id=?)  OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) ");
		SQL_GET_SPECIAL_BACKLIST.append(" OR (upper(black.BV_VALUE)=? and black.bv_be_id=?)  OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?)  OR (upper(black.BV_VALUE)=? and black.bv_be_id=?)   " +
				//"  OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?)" +
				//" OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) 
				")  and bv_is_enc=? ");
		
		
		
		SQL_GET_SPECIAL_BACKLIST_AES.append("SELECT black.bv_relationship,black.bv_bank_code, black.bv_bintype,black.bv_id,black.BV_BE_ID,black.bv_sendsms,black.BV_VALUE,black.BV_REMARK,black.BV_ISREPENDING FROM CCPS_BLACKLIST black WHERE black.BV_STATUS=1 AND black.BV_KIND = ? AND ((upper(black.BV_VALUE)=? and black.bv_be_id=?)  OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) ");
		SQL_GET_SPECIAL_BACKLIST_AES.append(" OR (upper(black.BV_VALUE)=? and black.bv_be_id=?)  OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?)  OR (upper(black.BV_VALUE)=? and black.bv_be_id=?)   " +
				"  OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?)" +
				" OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) )  ");
		
		
		SQL_GET_MAXMIND_CONTROL.append("SELECT * FROM CCPS_MAXMIND_CONTROL maxmind WHERE maxmind.MC_RISK_METHOD=? and ((maxmind.MC_MER_NO=? AND maxmind.MC_GW_NO=?)");
		SQL_GET_MAXMIND_CONTROL.append("OR (maxmind.MC_MER_NO=? AND maxmind.MC_GW_NO=0) OR (maxmind.MC_MER_NO=0 AND maxmind.MC_GW_NO=0)) ORDER BY maxmind.MC_GW_NO desc, maxmind.MC_MER_NO desc");
		
		
		SQL_GET_TRADE_CURRENCY.append("SELECT * FROM CCPS_CURRENCY_LIMMIT c WHERE (c.CL_MERNO=? AND c.CL_GWNO=?) ");
		SQL_GET_TRADE_CURRENCY.append(" OR (c.CL_MERNO=? AND c.CL_GWNO=0) OR (c.CL_MERNO=0 AND c.CL_GWNO=0) ORDER BY c.CL_GWNO desc, c.CL_MERNO desc");
		
		SQL_GET_IPCITY.append("SELECT * FROM CCPS_IPCITY_LIMMIT c WHERE (c.IPL_MRENO=? AND c.IPL_GATEWAYNO=?) ");
		SQL_GET_IPCITY.append(" OR (c.IPL_MRENO=? AND c.IPL_GATEWAYNO=0) OR (c.IPL_MRENO=0 AND c.IPL_GATEWAYNO=0) ORDER BY c.IPL_GATEWAYNO desc, c.IPL_MRENO desc");
		
		
		SQL_GET_CCPS_MAXMIND_OUTPUTS.append(" SELECT * FROM CCPS_MAXMIND_OUTPUTS where MOP_TR_NO=? ");
		
		SQL_SAVE_MAXMIND_OUTPUTS.append("INSERT INTO CCPS_MAXMIND_OUTPUTS (MOP_ID, MOP_TR_NO, MOP_COUNTRYMATCH, MOP_COUNTRYCODE, MOP_HIGHRISKCOUNTRY, MOP_DISTANCE, ");
		SQL_SAVE_MAXMIND_OUTPUTS.append("MOP_IP_REGION, MOP_IP_CITY, MOP_IP_LATITUDE, MOP_IP_LONGITUDE, MOP_IP_ISP, MOP_IP_ORG, MOP_ANONYMOUSPROXY, MOP_PROXY_SCORE,");
		SQL_SAVE_MAXMIND_OUTPUTS.append("MOP_TRANS_PROXY, MOP_FREEMAIL, MOP_CARDEREMAIL, MOP_HIGHRISKUSERNAME, MOP_HIGHRISKPASSWORD, MOP_BINMATCH, MOP_BINGCOUNTRY, ");
		SQL_SAVE_MAXMIND_OUTPUTS.append("MOP_BINNAMEMATCH, MOP_BINNAME, MOP_BINPHONEMATCH, MOP_BINPHONE, MOP_PHONEBILLS, MOP_SHIPFORWARD, MOP_CITYPOSTALMATCH,");
		SQL_SAVE_MAXMIND_OUTPUTS.append("MOP_SHIPCITYPMATCH, MOP_SCORE, MOP_QUERIESREMAINING, MOP_MAXMINDID, MOP_ERROR,");
		SQL_SAVE_MAXMIND_OUTPUTS.append("MOP_IP_ACCURACYRADIUS,MOP_IP_REGIONNAME,MOP_IP_POSTALCODE,MOP_IP_METROCODE,MOP_IP_AREACODE,MOP_IP_COUNTRYNAME,MOP_IP_CONTINENTCOD, ");
		SQL_SAVE_MAXMIND_OUTPUTS.append("MOP_IP_TIMEZONE,MOP_IP_ASNUM,MOP_IP_USERTYPE,MOP_IP_NETSPEEDCELL,MOP_IP_DOMAIN,MOP_IP_CITYCONF,MOP_IP_REGIONCONF,MOP_IP_POSTALCONF,MOP_IP_COUNTRYCONF,MOP_DATETIME)");
		SQL_SAVE_MAXMIND_OUTPUTS.append(" VALUES (CCPS_MAXMIND_OUTPUTS_SEQ.nextval, ?, ?,");
		SQL_SAVE_MAXMIND_OUTPUTS.append("?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,");
		SQL_SAVE_MAXMIND_OUTPUTS.append("?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp)");
		
		SQL_GET_AMOUNTLIMIT.append("SELECT amount.AL_CURRENCY, amount.AL_SINGLE_AMOUNT,amount.AL_DAY_AMOUNT, amount.AL_WEEK_AMOUNT, amount.AL_MONTH_AMOUNT ");
		SQL_GET_AMOUNTLIMIT.append("FROM CCPS_AMOUNT_LIMIT amount WHERE amount.AL_CARD_TYPE=? AND ((amount.AL_MER_NO=? AND amount.AL_GW_NO=?) ");
		SQL_GET_AMOUNTLIMIT.append("OR (amount.AL_MER_NO=? AND amount.AL_GW_NO=0) OR (amount.AL_MER_NO=0 AND amount.AL_GW_NO=0)) ");
		SQL_GET_AMOUNTLIMIT.append("ORDER BY amount.AL_GW_NO DESC, amount.AL_MER_NO DESC ");
		
		//CL_TYPE 类型 1、跨通道约束 2 单通道约束 
		SQL_GET_CHANNELLIMIT.append("SELECT cl.MER_NO, cl.GW_NO, cl.CARD_TYPE, cl.LIMIT_TIME FROM CCPS_CHANNEL_LIMIT cl ");
		SQL_GET_CHANNELLIMIT.append(" WHERE cl.CARD_TYPE=? AND cl.cl_type = 1 AND ((cl.MER_NO=? AND cl.GW_NO=?)");
		SQL_GET_CHANNELLIMIT.append(" OR (cl.MER_NO=? AND cl.GW_NO=0) OR (cl.MER_NO=0 AND cl.GW_NO=0))");
		SQL_GET_CHANNELLIMIT.append(" ORDER BY cl.GW_NO DESC, cl.MER_NO DESC");
		
		SQL_GET_CIPAYNUMLIMIT.append("SELECT cpl.cpl_black_country,cpl.cpl_white_country,cpl.CPL_ID, cpl.CPL_MER_NO, cpl.CPL_GW_NO, cpl.CPL_TYPE, cpl.CPL_HOUR, cpl.CPL_SUC_COUNT, cpl.CPL_TOTAL_COUNT, cpl.CPL_FAIL_COUNT, cpl.CPL_CARD_TYPE");
		SQL_GET_CIPAYNUMLIMIT.append(" FROM CCPS_CI_PAYNUM_LIMIT cpl WHERE ((cpl.CPL_MER_NO=? AND cpl.CPL_GW_NO=?)");
		SQL_GET_CIPAYNUMLIMIT.append(" OR (cpl.CPL_MER_NO=? AND cpl.CPL_GW_NO=0) OR (cpl.CPL_MER_NO=0 AND cpl.CPL_GW_NO=0))");
		SQL_GET_CIPAYNUMLIMIT.append(" ORDER BY cpl.CPL_GW_NO DESC, cpl.CPL_MER_NO DESC, cpl.cpl_hour");
		
		SQL_GET_ALL_TRADE_RATE.append("SELECT  rate.RATE_ORIGINAL_CURRENCY, rate.RATE_TARGET_CURRENCY, rate.RATE_VALUE FROM CCPS_RATE rate WHERE rate.RATE_TYPE=1");
		
		SQL_GET_AMOUNT_BY_CYCLE.append("SELECT tr.TR_CURRENCY, SUM(tr.TR_AMOUNT) AS TR_AMOUNT FROM CCPS_TRADERECORD tr WHERE  tr.TR_MER_NO=? ");
		SQL_GET_AMOUNT_BY_CYCLE.append("AND tr.TR_GW_NO=? AND tr.TR_STATUS=? AND tr.TR_CARDTYPE=? AND tr.TR_DATETIME >= TO_DATE(?,'YYYY-MM-DD') GROUP BY tr.TR_CURRENCY");
		
		SQL_GET_IPCOUNTRY_BY_IPVALUE.append("SELECT ip.IP_COUNTRY,ip.IP_STATE FROM CCPS_IP_CODE ip WHERE ip.IPNUM_FROM<=? AND ip.IPNUM_TO>=?");
		
		SQL_GET_IPWHITE_LIST.append("SELECT cou.CL_CODE FROM CCPS_COUNTY_LIMIT cou WHERE cou.CL_MER_NO=? AND cou.CL_GW_NO=? AND cou.CL_NAME=? AND cou.CL_TYPE=?");
		
		
		SQL_GET_IPWHITE_LIST_ALL.append("SELECT cou.CL_CODE FROM CCPS_COUNTY_LIMIT cou WHERE cou.CL_MER_NO=? AND cou.CL_GW_NO=0  AND cou.CL_NAME=? AND cou.CL_TYPE=?");
		
		SQL_ISBLACKLIST_BYIPCOUNTRY.append("SELECT CL_ID FROM CCPS_COUNTY_LIMIT cou WHERE cou.CL_CODE=? AND cou.CL_NAME=? AND cou.CL_TYPE=? and cou.CL_CARDTYPE=0 ");
		SQL_ISBLACKLIST_BYIPCOUNTRY.append("AND ((cou.CL_MER_NO=? AND cou.CL_GW_NO=?) OR (cou.CL_MER_NO=? AND cou.CL_GW_NO=0) OR (cou.CL_MER_NO=0 AND cou.CL_GW_NO=0)) order by CL_GW_NO desc ");
		
		
		
		
		SQL_GET_COUNTY_LIMIT_NEW.append("SELECT CL_ID FROM CCPS_COUNTY_LIMIT cou WHERE ");
		SQL_GET_COUNTY_LIMIT_NEW.append("cou.CL_NAME=? AND cou.CL_TYPE=? ");
		SQL_GET_COUNTY_LIMIT_NEW.append("and ");
		SQL_GET_COUNTY_LIMIT_NEW.append("cou.CL_CODE=?");
		SQL_GET_COUNTY_LIMIT_NEW.append("and ");
	   SQL_GET_COUNTY_LIMIT_NEW.append("((cou.CL_MER_NO=? AND cou.CL_GW_NO=0 and cou.cl_cardtype=0)");
		SQL_GET_COUNTY_LIMIT_NEW.append("or ");
		SQL_GET_COUNTY_LIMIT_NEW.append("(cou.CL_MER_NO=? AND cou.CL_GW_NO=0 and cou.cl_cardtype=?)");
		SQL_GET_COUNTY_LIMIT_NEW.append("or");
		SQL_GET_COUNTY_LIMIT_NEW.append("(cou.CL_MER_NO=? AND cou.CL_GW_NO=? and cou.cl_cardtype=0 )");
		SQL_GET_COUNTY_LIMIT_NEW.append("or");
		SQL_GET_COUNTY_LIMIT_NEW.append("(cou.CL_MER_NO=? AND cou.CL_GW_NO=? and cou.cl_cardtype=?)");
		SQL_GET_COUNTY_LIMIT_NEW.append(")");
		
		
		SQL_GET_COUNTY_LIMIT_NEW1.append("SELECT CL_ID FROM CCPS_COUNTY_LIMIT cou WHERE ");
		SQL_GET_COUNTY_LIMIT_NEW1.append("cou.CL_NAME=? AND cou.CL_TYPE=? ");
		//SQL_GET_COUNTY_LIMIT_NEW1.append("and ");
		//SQL_GET_COUNTY_LIMIT_NEW1.append("cou.CL_CODE=?");
		SQL_GET_COUNTY_LIMIT_NEW1.append("and ");
	    SQL_GET_COUNTY_LIMIT_NEW1.append("((cou.CL_MER_NO=? AND cou.CL_GW_NO=0 and cou.cl_cardtype=0)");
		SQL_GET_COUNTY_LIMIT_NEW1.append("or ");
		SQL_GET_COUNTY_LIMIT_NEW1.append("(cou.CL_MER_NO=? AND cou.CL_GW_NO=0 and cou.cl_cardtype=?)");
		SQL_GET_COUNTY_LIMIT_NEW1.append("or");
		SQL_GET_COUNTY_LIMIT_NEW1.append("(cou.CL_MER_NO=? AND cou.CL_GW_NO=? and cou.cl_cardtype=0 )");
		SQL_GET_COUNTY_LIMIT_NEW1.append("or");
		SQL_GET_COUNTY_LIMIT_NEW1.append("(cou.CL_MER_NO=? AND cou.CL_GW_NO=? and cou.cl_cardtype=?)");
		SQL_GET_COUNTY_LIMIT_NEW1.append(")");

		SQL_ISBLACKLIST_BYIPCOUNTRY_CARDTYPE.append("SELECT CL_ID FROM CCPS_COUNTY_LIMIT cou WHERE cou.CL_CODE=? AND cou.CL_NAME=? AND cou.CL_TYPE=?  and cou.CL_CARDTYPE=? ");
		SQL_ISBLACKLIST_BYIPCOUNTRY_CARDTYPE.append("AND ((cou.CL_MER_NO=? AND cou.CL_GW_NO=?) OR (cou.CL_MER_NO=? AND cou.CL_GW_NO=0) OR (cou.CL_MER_NO=0 AND cou.CL_GW_NO=0)) order by CL_GW_NO desc ");
		
		
		SQL_ISBLACKLIST_BYIPCOUNTRY_SET.append("SELECT COUNT(*) FROM CCPS_COUNTY_LIMIT cou WHERE  cou.CL_NAME=? AND cou.CL_TYPE=? and cou.CL_CARDTYPE=0 ");
		SQL_ISBLACKLIST_BYIPCOUNTRY_SET.append("AND ((cou.CL_MER_NO=? AND cou.CL_GW_NO=?) OR (cou.CL_MER_NO=? AND cou.CL_GW_NO=0) OR (cou.CL_MER_NO=0 AND cou.CL_GW_NO=0)) ");
		
		SQL_ISBLACKPART_BYIP.append("SELECT COUNT(*) FROM CCPS_MER_IP_LIMIT ip WHERE ip.IL_START_IP_VALUE <= ? AND ip.IL_END_IP_VALUE >= ? ");
		SQL_ISBLACKPART_BYIP.append("AND ((ip.IL_MER_NO=? AND ip.IL_GW_NO=?) OR (ip.IL_MER_NO=? AND ip.IL_GW_NO=0) OR (ip.IL_MER_NO=0 AND ip.IL_GW_NO=0))");
		
		SQL_GET_PAYNUM.append("SELECT p.PL_ID, p.PL_MER_NO,p.PL_GW_NO,p.PL_CARD_TYPE,p.PL_MINSECOND,p.PL_SUC_COUNT,p.PL_TOTAL_COUNT,p.PL_FAIL_COUNT,p.PL_BE_ID, p.PL_TYPE FROM CCPS_PAYNUM_LIMIT p ");
		SQL_GET_PAYNUM.append(" WHERE PL_MINAMOUNT=0 and PL_MAXAMOUNT=0 and ((p.PL_MER_NO=? AND p.PL_GW_NO=?) OR (p.PL_MER_NO=? AND p.PL_GW_NO=0) OR (p.PL_MER_NO=0 AND p.PL_GW_NO=0)) AND (p.PL_CARD_TYPE = 0 OR p.PL_CARD_TYPE = ?) ORDER BY p.PL_CARD_TYPE DESC,p.PL_GW_NO DESC, p.PL_MER_NO DESC");
		
		SQL_GET_PAYNUM1.append("SELECT PL_MINAMOUNT,PL_MAXAMOUNT,p.PL_ID, p.PL_MER_NO,p.PL_GW_NO,p.PL_CARD_TYPE,p.PL_MINSECOND,p.PL_SUC_COUNT,p.PL_TOTAL_COUNT,p.PL_FAIL_COUNT,p.PL_BE_ID, p.PL_TYPE FROM CCPS_PAYNUM_LIMIT p ");
		SQL_GET_PAYNUM1.append(" WHERE ?>=PL_MINAMOUNT and ?<PL_MAXAMOUNT and ((p.PL_MER_NO=? AND p.PL_GW_NO=?) OR (p.PL_MER_NO=? AND p.PL_GW_NO=0)) AND (p.PL_CARD_TYPE = 0 OR p.PL_CARD_TYPE = ?) ORDER BY p.PL_CARD_TYPE DESC,p.PL_GW_NO DESC, p.PL_MER_NO DESC");
		
		
		SQL_GET_ELEMENT_BY_PAYNUM.append("SELECT b.BE_ID,b.BE_NAME,b.BE_CODE FROM CCPS_PAYNUM_LIMIT_ELEMENT l ");
		SQL_GET_ELEMENT_BY_PAYNUM.append("LEFT JOIN CCPS_BLACK_ELEMENT b ON l.PLE_BE_ID = b.BE_ID WHERE l.PLE_PL_ID=? AND l.PLE_BE_ID=?");
		
		SQL_GET_PAYNUM_BY_EMELENT.append("SELECT COUNT(*) totCount,SUM(CASE WHEN trade.TR_STATUS=1 THEN 1 ELSE 0 END) sucCount FROM CCPS_TRADERECORD trade ");
		SQL_GET_PAYNUM_BY_EMELENT.append(" JOIN CCPS_CREDITINFO credit ON trade.TR_NO = credit.CI_TR_NO WHERE trade.TR_DATETIME >= TO_DATE(?,'YYYY-MM-DD HH24:MI:SS')");
		
		SQL_GET_BINCOUNTRY.append("SELECT c.CL_TYPE FROM CCPS_COUNTY_LIMIT c ");
		SQL_GET_BINCOUNTRY.append("WHERE c.CL_CODE=? AND c.CL_NAME=2 AND ((c.CL_MER_NO=? AND c.CL_GW_NO=?) ");
		SQL_GET_BINCOUNTRY.append("OR (c.CL_MER_NO=? AND c.CL_GW_NO=0) OR (c.CL_MER_NO=0 AND c.CL_GW_NO=0))");
		
		//Cybersource风控处理
		//查询接口附加参数
		SQL_GET_EXTRA_TRADERECORD.append("select ET_USERNAME,ET_PASSWORD,ET_REGISTERDATE,ET_COUPONS,ET_ISCOPY,ET_FINGERPRINT ");
		SQL_GET_EXTRA_TRADERECORD.append(" from ccps_extra_traderecord where et_tr_no=?"); //et_tr_no(varchar2类型)
		
		
		SQL_GET_REGISTERDAY.append("SELECT TL_TYPE,TL_DATE FROM CCPS_REGISTERTIME_LIMIT RL ");
		SQL_GET_REGISTERDAY.append("WHERE  ((RL.TL_MER_NO=? AND RL.TL_GW_NO=?) ");	
		SQL_GET_REGISTERDAY.append("OR (RL.TL_MER_NO=? AND RL.TL_GW_NO=0)) ORDER BY RL.TL_GW_NO DESC ");
		
		SQL_INSERT_CREDITINFOLOG.append("INSERT INTO CCPS_CREDITINFO_LOG (CRL_ID,CRL_TRADENO,CRL_TIME,CRL_MERNO,");
		SQL_INSERT_CREDITINFOLOG.append("CRL_GW_NO,CRL_CAUSE,CRL_REMARK,CRL_OS,CRL_BROWER,CRL_LANG,CRL_TIMEZONE,CRL_RESOLUTION,CRL_IPADDRESS,CRL_ENTERPAGE,CRL_COMLOADPAGE,CRL_ENTRYCARD)");
		SQL_INSERT_CREDITINFOLOG.append("VALUES(CCPS_CREDITINFOLOG_SEQ.nextval,?,sysdate,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		
		SQL_QUERY_MER_RISK_CARDBIN.append("SELECT COUNT(*) FROM CCPS_MER_RISK_BIN WHERE MRB_STATUS=1 AND MRB_CRADBIN=? ");
		
		SQL_QUERY_BALCKLIST.append(" select * from ccps_blacklist   where  bv_be_id=4 and ( instr(?,upper(bv_value))>0  or instr(?,bv_value)> 0) and bv_status=1");
		
		SQL_GET_IPCITY_BY_IPVALUE.append("SELECT ip.IP_COUNTRY,ip.IP_STATE FROM CCPS_IP_CITY ip WHERE ip.IPNUM_FROM<=? AND ip.IPNUM_TO>=?");
		
		
		SQL_GET_IS_ACTIVE_CHECK.append(" select count(*) from ccps_active_info a ");
		SQL_GET_IS_ACTIVE_CHECK.append(" where a.CAI_MER_NO=? and a.CAI_GW_NO=? and a.CAI_CARD_PART=? and a.CAI_EMAIL=?");
		SQL_GET_IS_ACTIVE_CHECK.append(" and (a.CAI_ACCOUNT=? or a.CAI_REG_DATE=? or a.CAI_FIRST_NAME=? or  a.CAI_LAST_NAME=?)");
		
		SQL_QUERY_CARDNO.append(" SELECT SUM(C)  FROM (  SELECT COUNT(*)C    FROM CCPS_TRADERECORD T,CCPS_CREDITINFO C WHERE 1=1 AND  t.tr_no=C.CI_TR_NO AND T.TR_STATUS=1  AND CI_SHA256=? ");
		SQL_QUERY_CARDNO.append(" UNION SELECT COUNT(*)  C FROM CCPS_OLDWHITELIST  OW WHERE OW.OW_CARDINFO=? )"  );
		SQL_QUERY_WHITE_COUNT.append(" select count(*) from ccps_whitelist w where w.wv_merno=? and w.wv_gw_no=?  and w.wv_status=1 ");
		SQL_QUERY_WHITE_COUNT.append(" and (w.wv_value=?  or w.wv_value=? or w.wv_value=? ) " );
		
		SQL_QUERY_BLACKLIST.append(" select b.bv_be_id, b.bv_value from ccps_blacklist b where b.BV_STATUS=1 AND b.BV_KIND = 1 AND ((b.BV_VALUE=? and b.bv_be_id = ?) OR (b.BV_VALUE=? and b.bv_be_id = ?) OR (b.BV_VALUE=? and b.bv_be_id = ?) OR (b.BV_VALUE=? and b.bv_be_id = ?) ");
		SQL_QUERY_BLACKLIST.append(" OR (b.BV_VALUE=? and b.bv_be_id = ?) OR (b.BV_VALUE=? and b.bv_be_id = ?) OR (b.BV_VALUE=? and b.bv_be_id = ?) )");
		
		SQL_QUERY_CITYBLACKLIST.append(" select b.bv_be_id, b.bv_value from ccps_blacklist b where b.BV_STATUS=1 AND b.BV_KIND = 1 AND ((b.BV_VALUE=? and b.bv_be_id = ?) OR (b.BV_VALUE=? and b.bv_be_id = ?) OR (b.BV_VALUE=? and b.bv_be_id = ?) OR (b.BV_VALUE=? and b.bv_be_id = ?) ");
		SQL_QUERY_CITYBLACKLIST.append(" OR (b.BV_VALUE=? and b.bv_be_id = ?) OR (b.BV_VALUE=? and b.bv_be_id = ?) OR (b.BV_VALUE=? and b.bv_be_id = ?) OR (b.BV_VALUE=? and b.bv_be_id = ?))");
		
		SQL_SAVE_BLACKLIST.append("insert into ccps_blacklist (bv_id, bv_be_id, bv_value, bv_status, bv_type, bv_login_name, bv_oprtime, bv_remark, bv_kind)");
		SQL_SAVE_BLACKLIST.append(" values (ccps_blacklist_seq.nextval, ?,?,1, 1, 'system', sysdate,'',1 )");
		
		SQL_QUERY_TELESIGN.append(" select tel.tel_tradeno, tel.tel_datetime, tel.tel_type, tel.tel_checkcode from ccps_telesign tel where tel.tel_tradeno = ? ");
		SQL_QUERY_TELESIGN.append(" and tel.tel_type = ? order by tel.tel_datetime desc ");
		
		SQL_QUERY_TEL_VALIDATION.append(" select t.tv_cardtype from ccps_mer_tel_validation t where t.tv_validated = 1 and t.tv_gw_no = ? ");
		
		SQL_GET_MAIL_MANYCARD.append("select count(1) from ccps_traderecord t left join ccps_creditinfo c on t.tr_no = c.ci_tr_no ");
		SQL_GET_MAIL_MANYCARD.append("where t.tr_datetime > (sysdate - ?/(24*60)) and t.tr_mer_no = ? and c.ci_email= ? ");
		SQL_GET_MAIL_MANYCARD.append("and c.ci_sha256 != ?");
		
		SQL_GET_HIGHRISKBLACK_LIST.append("SELECT cou.CL_CODE FROM CCPS_COUNTY_LIMIT cou  WHERE cou.CL_NAME = ? AND cou.CL_TYPE = ? ");
		SQL_GET_HIGHRISKBLACK_LIST.append("AND ((cou.CL_MER_NO = ? AND cou.cl_gw_no = ?) OR (cou.CL_MER_NO = ? AND cou.cl_gw_no = 0) OR");
		SQL_GET_HIGHRISKBLACK_LIST.append("(cou.CL_MER_NO = 0 AND cou.cl_gw_no = 0) )");
		
		
		SQL_GET_SHIPCOUNTRYWHITE_LIST.append("SELECT cou.CL_CODE FROM CCPS_COUNTY_LIMIT cou WHERE cou.CL_MER_NO=? AND cou.CL_GW_NO=? AND cou.CL_NAME=? AND cou.CL_TYPE=?");
		
		SQL_GET_SHIPCOUNTRYWHITE_LIST_ALL.append("SELECT cou.CL_CODE FROM CCPS_COUNTY_LIMIT cou WHERE cou.CL_MER_NO=? AND cou.CL_GW_NO=0  AND cou.CL_NAME=? AND cou.CL_TYPE=?");
		
		SQL_ISBLACKLIST_BYSHIPCOUNTRY.append("SELECT COUNT(*) FROM CCPS_COUNTY_LIMIT cou WHERE cou.CL_CODE=? AND cou.CL_NAME=? AND cou.CL_TYPE=? ");
		SQL_ISBLACKLIST_BYSHIPCOUNTRY.append("AND ((cou.CL_MER_NO=? AND cou.CL_GW_NO=?) OR (cou.CL_MER_NO=? AND cou.CL_GW_NO=0) OR (cou.CL_MER_NO=0 AND cou.CL_GW_NO=0))");
		
		SQL_GET_CHA_RATE.append("select cct.cc_id, cct.cc_cha_code, cct.cc_cardtype,");
		SQL_GET_CHA_RATE.append(" cct.cc_cha_rate, cct.cc_cha_limit,cct.CC_RSRATE");
		SQL_GET_CHA_RATE.append(" from ccps_cha_cardtype cct");
		SQL_GET_CHA_RATE.append(" where cct.cc_cha_code = ? and cct.cc_cardtype = ?");
		
		SQL_GET_MER_BACKLIST
				.append("select b.card_type,b.mb_bank_code,b.mb_key,b.MB_SENDSMS,b.mb_minamount,b.mb_maxamount  from CCPS_MER_BLACKLIST b where b.mb_type = 1 and b.MB_KIND=? and ((b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or    (b.mb_key = ? and upper(b.mb_value)= ?) or   (b.mb_key = ? and upper(b.mb_value) = ?) or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or (b.mb_key = ? and upper(b.mb_value) = ?) " +
						//" or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and instr(?,upper(b.mb_value))>0) or (b.mb_key = ? and upper(b.mb_value) = ?) " +
						")and ((b.mb_merno = 0 and b.mb_gwno = 0) or (b.mb_merno = ? and b.mb_gwno = 0) or (b.mb_merno = ? and b.mb_gwno = ?))  and b.mb_is_enc=?  " +
						"" +
						"and b.mb_value  not in (select mb.mb_value from CCPS_MER_BLACKLIST mb    where MB.MB_KIND=? AND mb.mb_type = 2   and ( (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or    (b.mb_key = ? and upper(b.mb_value) = ?) or   (b.mb_key = ? and upper(b.mb_value) = ?) or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) " +
						//" or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and instr(?,upper(b.mb_value))>0) or (b.mb_key = ? and upper(b.mb_value) = ?) " +
						" ) and ((mb.mb_merno = 0 and mb.mb_gwno = 0) or  (mb.mb_merno = ? and mb.mb_gwno = 0) or  (mb.mb_merno = ? and mb.mb_gwno = ?))  and mb.mb_is_enc=? )    ");
		
		
		
		SQL_GET_MER_BACKLIST_AES
		.append("select b.card_type,b.mb_bank_code,b.mb_key,b.MB_SENDSMS,b.mb_minamount,b.mb_maxamount  from CCPS_MER_BLACKLIST b where b.mb_type = 1 and b.MB_KIND=? and ((b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value)= ?) or   (b.mb_key = ? and upper(b.mb_value) = ?) or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ? )  or (b.mb_key = ? and upper(b.mb_value) = ?) " +
				" or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) " +
				")and ((b.mb_merno = 0 and b.mb_gwno = 0) or (b.mb_merno = ? and b.mb_gwno = 0) or (b.mb_merno = ? and b.mb_gwno = ?)) " +
				"" +
				"and F_ZNMH_SMK_CRYPT(b.mb_value,'',2) not in (select F_ZNMH_SMK_CRYPT(mb.mb_value,'',2) from CCPS_MER_BLACKLIST mb where MB.MB_KIND=? AND mb.mb_type = 2 and ( (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or    (b.mb_key = ? and upper(b.mb_value) = ?) or   (b.mb_key = ? and upper(b.mb_value) = ?) or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?)  or  (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) " +
				" or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) or (b.mb_key = ? and upper(b.mb_value) = ?) " +
				" ) and ((mb.mb_merno = 0 and mb.mb_gwno = 0) or  (mb.mb_merno = ? and mb.mb_gwno = 0) or  (mb.mb_merno = ? and mb.mb_gwno = ?))  )    ");

		SQL_GET_MER_BACKLIST1
		.append("select b.card_type,b.mb_bank_code,b.mb_key,b.MB_SENDSMS,b.mb_minamount,b.mb_maxamount  from CCPS_MER_BLACKLIST b where b.mb_type = 1 and b.MB_KIND=? and ((b.mb_key = ? and upper(b.mb_value) = ?)  or (b.mb_key = ? and upper(b.mb_value) = ?)   )and ((b.mb_merno = 0 and b.mb_gwno = 0) " +
				"or (b.mb_merno = ? and b.mb_gwno = 0) or   (  b.mb_merno in (select mbs.mbs_value from ccps_mer_black_set mbs where mbs.mbs_type=? ) or   b.mb_gwno in (select mbs.mbs_value from ccps_mer_black_set mbs where mbs.mbs_type=? ) ) ) " +
				" and  b.mb_remark like '%第一次%' " +
				"and b.mb_value not in (select mb.mb_value from CCPS_MER_BLACKLIST mb    where MB.MB_KIND=? AND mb.mb_type = 2   and ( (b.mb_key = ? and upper(b.mb_value) = ?)  or (b.mb_key = ? and upper(b.mb_value) = ?)  )       and ((mb.mb_merno = 0 and mb.mb_gwno = 0) or    (mb.mb_merno = ? and mb.mb_gwno = 0) or  (mb.mb_merno = ? and mb.mb_gwno = ?))  )    ");

		
		SQL_GET_MUST_BACKLIST.append("SELECT black.BV_BE_ID,black.BV_SENDSMS,black.bv_remark,black.BV_ISREPENDING,black.BV_LEVEL FROM CCPS_BLACKLIST_MUST black    left join ccps_black_element be on be.be_id=black.bv_be_id  WHERE black.BV_STATUS=1  AND (( instr(?,upper(bv_value))>0 and black.bv_be_id=?)  OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) ");
		SQL_GET_MUST_BACKLIST.append(" OR (upper(replace (bv_value,',',' '))=? and black.bv_be_id=?)  OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?)  OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?)" +
				" OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?)  OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?) OR (upper(black.BV_VALUE)=? and black.bv_be_id=?)" +
				"  or (instr(?, upper(bv_value)) > 0 and black.bv_be_id = ?)   or (instr(?, upper(bv_value)) > 0 and black.bv_be_id = ?) )  order by black.bv_level,be.be_number asc ");
		
		
		SQL_GET_MUST_BACKLIST1.append(" SELECT black.BV_BE_ID,black.BV_SENDSMS,black.bv_remark,black.BV_ISREPENDING,black.bv_value FROM CCPS_BLACKLIST_MUST black WHERE black.BV_STATUS=1  and black.bv_be_id in (18,17)");
	//,?,?,?,?,?,?,?
		SQL_SAVE_THREATMETRIX.append(" insert into  CCPS_THREAT_METRIX (TM_ID,TM_OPRTIME,TM_TR_NO,TRUE_IP,TRUE_IP_CITY,TRUE_IP_FIRST_SEEN,TRUE_IP_GEO,TRUE_IP_ISP,TRUE_IP_LAST_EVENT,TRUE_IP_LAST_UPDATE,TRUE_IP_LATITUDE,TRUE_IP_LONGITUDE,TRUE_IP_ORGANIZATION,TRUE_IP_REGION,TRUE_IP_RESULT,TRUE_IP_SCORE,TRUE_IP_WORST_SCORE," +
				"UA_AGENT,UA_BROWSER,UA_MOBILE,UA_OS,UA_PLATFORM,UA_PROXY," +
				"TRANSACTION_SHIPPING_CURRENCY,TRANSACTION_SHIPPING_AMOUNT,TRANSACTION_ID,TRANSACTION_CURRENCY,TRANSACTION_AUTH_CURRENCY,TRANSACTION_AUTH_AMOUNT,TRANSACTION_AMOUNT," +
				"POLICY,POLICY_SCORE," +
				"SUMMARY_REASON_CODE,SUMMARY_RISK_SCORE," +
				"TIME_ZONE,TIME_ZONE_DST_OFFSET,TIMEZONE_OFFSET_ANOMALY," +
				"REASON_CODE,RELATED_CUSTOM_ID,RELATED_DEVICE_ID,RELATED_REQUEST_ID,REQUEST_DURATION,REQUEST_ID,REQUEST_RESULT,REVIEW_STATUS,RISK_RATING," +
				"SCREEN_ASPECT_RATIO_ANOMALY,SCREEN_COLOR_DEPTH,SCREEN_RES,SCREEN_RES_ALT,SCREEN_RES_ANOMALY,SCREEN_DPI,SERVICE_TYPE,SESSION_ANOMALY,SESSION_ID," +
				"PROCESSING_PROVIDER,PROFILE_API_TIMEDELTA,PROFILE_ORG_ID,PROFILED_DOMAIN,PROFILED_DOMAIN_RESULT,PROFILED_DOMAIN_FIRST_SEEN,PROFILED_DOMAIN_LAST_EVENT,PROFILED_DOMAIN_LAST_UPDATE,PROFILED_DOMAIN_SCORE,PROFILED_DOMAIN_WORST_SCORE,PROFILED_URL,PROFILING_DATETIME,PROFILING_SITE_ID," +
				"PROXY_IP,PROXY_IP_CITY,PROXY_IP_FIRST_SEEN,PROXY_IP_GEO,PROXY_IP_ISP,PROXY_IP_LAST_EVENT,PROXY_IP_LAST_UPDATE,PROXY_IP_LATITUDE,PROXY_IP_LONGITUDE,PROXY_IP_RESULT,PROXY_IP_SCORE,PROXY_IP_WORST_SCORE,PROXY_IP_ORGANIZATION,PROXY_IP_REGION,PROXY_NAME,PROXY_TYPE," +
				"PLUGIN_ADOBE_ACROBAT,PLUGIN_DEVALVR,PLUGIN_FLASH,PLUGIN_HASH,PLUGIN_JAVA,PLUGIN_NUMBER,PLUGIN_QUICKTIME,PLUGIN_REALPLAYER,PLUGIN_SHOCKWAVE,PLUGIN_SILVERLIGHT,PLUGIN_SVG_VIEWER,PLUGIN_VLC_PLAYER,PLUGIN_WINDOWS_MEDIA_PLAYER,ORG_ID," +
				"OS,OS_ANOMALY,OS_DAYSUPDATED,OS_FONTS_HASH,OS_FONTS_NUMBER,OS_UPDATE_STRATEGY," +
				"PAGE_FINGERPRINT,PAGE_FINGERPRINT_CHECK,PAGE_FINGERPRINT_FIRST_SEEN,PAGE_FINGERPRINT_LAST_EVENT,PAGE_FINGERPRINT_LAST_UPDATE,PAGE_FINGERPRINT_RESULT,PAGE_FINGERPRINT_SCORE,PAGE_FINGERPRINT_WORST_SCORE,PAGE_FINGERPRINT_DIFF,PAGE_FINGERPRINT_MATCH,PAGE_ID,PAGE_SUMMARY,PAGE_TIME_ON," +
				"MIME_TYPE_HASH,MIME_TYPE_NUMBER,MULTIPLE_SESSION_ID,OFFSET_MEASURE_TIME,JB_ROOT,JB_ROOT_REASON,JS_BROWSER_STRING,JS_FONTS_HASH,JS_FONTS_LIST,JS_FONTS_NUMBER," +
				"IMAGE_ANOMALY,IMAGE_LOADED,HEADERS_NAME_VALUE_HASH,HEADERS_ORDER_STRING_HASH," +
				"HONEYPOT_FINGERPRINT,HONEYPOT_FINGERPRINT_CHECK,HONEYPOT_FINGERPRINT_DIFF,HONEYPOT_FINGERPRINT_MATCH,HONEYPOT_UNKNOWN_DIFF," +
				"HTTP_OS_SIG_ADV_MSS,HTTP_OS_SIG_RAW,HTTP_OS_SIG_RCV_MSS,HTTP_OS_SIG_SND_MSS,HTTP_OS_SIG_TTL,HTTP_OS_SIGNATURE,HTTP_REFERER,HTTP_REFERER_DOMAIN,HTTP_REFDOMAIN_RESULT,HTTP_REFDOMAIN_FIRST_SEEN,HTTP_REFDOMAIN_LAST_EVENT,HTTP_REFDOMAIN_LAST_UPDATE,HTTP_REFDOMAIN_SCORE,HTTP_REFDOMAIN_WORST_SCORE,HTTP_REFERER_URL," +
				"EVENT_TYPE,FIRST_PARTY_COOKIE,FLASH_ANOMALY,FLASH_LANG,FLASH_OS,FLASH_VERSION," +
				"FUZZY_DEVICE_ID,FUZZY_DEVICE_ID_CONFIDENCE,FUZZY_DEVICE_RESULT,FUZZY_DEVICE_FIRST_SEEN,FUZZY_DEVICE_LAST_EVENT,FUZZY_DEVICE_LAST_UPDATE,FUZZY_DEVICE_MATCH_RESULT,FUZZY_DEVICE_SCORE,FUZZY_DEVICE_WORST_SCORE," +
				"CUSTOM_POLICY_SCORE,DATETIME,DETECTED_FL,DEVICE_FINGERPRINT,DEVICE_ID,DEVICE_ID_CONFIDENCE,DEVICE_FIRST_SEEN,DEVICE_LAST_EVENT,DEVICE_LAST_UPDATE,DEVICE_MATCH_RESULT,DEVICE_RESULT,DEVICE_SCORE,DEVICE_WORST_SCORE," +
				"DNS_IP,DNS_IP_CITY,DNS_IP_GEO,DNS_IP_ISP,DNS_IP_LATITUDE,DNS_IP_LONGITUDE,DNS_IP_ORGANIZATION,DNS_IP_REGION,DRIVERS_LICENCE_NUMBER_HASH," +
				"ENABLED_CK,ENABLED_FL,ENABLED_IM,ENABLED_JS,ENABLED_SERVICES,ERROR_DETAIL,ETAG_GUID,CSS_IMAGE_LOADED," +
				"BROWSER_LANGUAGE,BROWSER_LANGUAGE_ANOMALY,BROWSER_PROCESS,BROWSER_STRING,BROWSER_STRING_ANOMALY,BROWSER_STRING_HASH,BROWSER_STRING_MISMATCH,BROWSER_VERSION," +
				"CC_BIN_NUMBER,CC_BIN_NUMBER_BRAND,CC_BIN_NUMBER_CATEGORY,CC_BIN_NUMBER_GEO,CC_BIN_NUMBER_ORG,CC_BIN_NUMBER_TYPE," +
				"CC_NUMBER_HASH,CC_NUMBER_HASH_RESULT,CC_NUMBER_HASH_FIRST_SEEN,CC_NUMBER_HASH_LAST_EVENT,CC_NUMBER_HASH_LAST_UPDATE,CC_NUMBER_HASH_SCORE,CC_NUMBER_HASH_WORST_SCORE,CIDR_NUMBER," +
				"ACCOUNT_EMAIL,ACCOUNT_EMAIL_RESULT,ACCOUNT_EMAIL_FIRST_SEEN,ACCOUNT_EMAIL_LAST_EVENT,ACCOUNT_EMAIL_LAST_UPDATE,ACCOUNT_EMAIL_SCORE,ACCOUNT_EMAIL_WORST_SCORE," +
				"ACCOUNT_NAME,ACCOUNT_NAME_RESULT,ACCOUNT_NAME_FIRST_SEEN,ACCOUNT_NAME_LAST_EVENT,ACCOUNT_NAME_LAST_UPDATE,ACCOUNT_NAME_SCORE,ACCOUNT_NAME_WORST_SCORE," +
				"ACCOUNT_TELEPHONE,ACCOUNT_TELEPHONE_RESULT,ACCOUNT_TELEPHONE_FIRST_SEEN,ACCOUNT_TELEPHONE_LAST_EVENT,ACCOUNT_TELEPHONE_LAST_UPDATE,ACCOUNT_TELEPHONE_SCORE,ACCOUNT_TELEPHONE_WORST_SCORE," +
				"AGENT_BRAND,AGENT_DEVICE_ID,AGENT_DEVICE_STATE,AGENT_EVENT_TIME,AGENT_HEALTH_DETAILS,AGENT_HEALTH_STATUS,AGENT_LANGUAGE,AGENT_LOCALE,AGENT_MODEL,AGENT_OS,AGENT_OS_VERSION,AGENT_SESSION_ID,AGENT_TYPE,AGENT_VERSION," +
				"UNKNOWN_SESSION,SHIPPING_ADDRESS,SHIPPING_ADDRESS_ATTRIBUTES,SHIPPING_ADDRESS_CITY,SHIPPING_ADDRESS_COUNTRY,SHIPPING_ADDRESS_STATE,SHIPPING_ADDRESS_STREET1,SHIPPING_ADDRESS_STREET2,SHIPPING_ADDRESS_ZIP," +
				"INPUT_IP_ADDRESS,INPUT_IP_ASSERT_HISTORY,INPUT_IP_CITY,INPUT_IP_FIRST_SEEN,INPUT_IP_GEO,INPUT_IP_ISP,INPUT_IP_LAST_EVENT,INPUT_IP_LAST_UPDATE,INPUT_IP_LATITUDE,INPUT_IP_LONGITUDE,INPUT_IP_ORGANIZATION,INPUT_IP_REGION,INPUT_IP_RESULT,INPUT_IP_SCORE," +
				"ACCOUNT_ADDRESS,ACCOUNT_ADDRESS_CITY,ACCOUNT_ADDRESS_COUNTRY,ACCOUNT_ADDRESS_STATE,ACCOUNT_ADDRESS_STREET1,ACCOUNT_ADDRESS_STREET2,ACCOUNT_ADDRESS_ZIP) values (CCPS_THREAT_METRIX_SEQ.nextval,sysdate,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
				",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?," +
				"?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?," +
				"?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?," +
				"?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
				")");
	}
	
	
}
 