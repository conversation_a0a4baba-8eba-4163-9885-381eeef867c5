package com.lot.sql;

/**
 * 
 * <p>Title: </p>
 * <p>Description: 保存银行sql语句</p>
 * <p>Copyright: Copyright (c) 2011 版权</p>
 * <p>Company: </p>
 * <AUTHOR>
 * @version V1.0 
 * @date 2011-7-7下午05:27:04
 */
public class BankSql {
	
	//根据银行返回的获取交易记录的值
	public static final StringBuffer SQL_GET_TRADINFO_BY_TRADENO = new StringBuffer();
	
	// 根据银行返回交易序列号获取信息
	public static final StringBuffer SQL_GET_TRADINFO_BY_TRREFERENCE = new StringBuffer();
	// 基础查询语句 用于按条件union
	public static final StringBuffer SQL_BASE_TRADINFO_BY_TRREFERENCE = new StringBuffer();
	
	//根据通道代码返回银行通道信息
	public static final StringBuffer SQL_GET_BANKCHANNEL_INFO = new StringBuffer();
	
	//根据银行返回的信息更新交易记录表
	public static final StringBuffer SQL_UPDATE_TRADE_TECORD = new StringBuffer();
	
	public static final StringBuffer SQL_UPDATE_AUTH_TECORD = new StringBuffer();	
	
	//根据网关接入号获取绑定的域名信息
	public static final StringBuffer SQL_GET_DOMAININFO_BY_GATEWAY = new StringBuffer();
	
	//保存发送邮件表 
	public static final StringBuffer SQL_SAVE_EMAIL = new StringBuffer();
	
	//生成vpn通道票据号
	public static final StringBuffer SQL_GET_MOTOVPN_INVOICENO = new StringBuffer();
	
	//保存moto的另外交易记录信息
	public static final StringBuffer SQL_SAVE_TRADE_DIFFINFO = new StringBuffer();
	
	//保存moto的另外交易记录信息
	public static final StringBuffer SQL_SAVE_3DTRADE_DIFFINFO = new StringBuffer();
	
	//获取moto vpn交易
	public static final StringBuffer SQL_GET_TRADE_DIFFINFO = new StringBuffer();
	
	//根据通道标识获取另外同一通道的值
	public static final StringBuffer SQL_GET_CHANNELINFO_BY_FLAG = new StringBuffer();
	
	//根据通道标示获取非3D通道的值
	public static final StringBuffer SQL_GET_THREEDCHANNELINFO_BY_FLAG = new StringBuffer();
	
	//修改交易记录表中的通道代码
	public static final StringBuffer SQL_UPDATE_TRADERECORD_CHANNELCODE = new StringBuffer();
	
	//修改moto vpn表
	public static final StringBuffer SQL_UPDATE_MOTO_DIFF = new StringBuffer();

	//判断是否已发送邮件
	public static final StringBuffer SQL_IS_EXISTS_SEND_EMAIL = new StringBuffer();
	

	//二次支付 查询网关接入号是否支持二次支付
	public static final StringBuffer SQL_QUERYSECOND_GATEWAY=new StringBuffer();
	public static final StringBuffer SQL_QUERYSECOND_PAYTYPE=new StringBuffer();
	//二次支付   更改第一笔交易为 二次付款状态
	public static final StringBuffer SQL_UPDATE_PRETRADE_TECORD = new StringBuffer();
	//二次支付  封装好parambean信息
	public static final StringBuffer SQL_PARAMBEAN_INFO = new StringBuffer();
	//二次支付 保存交易表记录
	public static final StringBuffer SQL_SAVE_SECOND_TRADE = new StringBuffer();
	/** 代理商信息保存修改，支持多级代理商 2015-01-23 add*/
	public static final StringBuffer SQL_SAVE_SECOND_TRADE_NEW = new StringBuffer();
	//二次支付保存持卡人信息表
	 public static final StringBuffer SQL_SAVE_SECOND_CREDIT = new StringBuffer();
	//二次支付更改交易表的银行code，通道code
  	public static final StringBuffer SQL_UPADTE_SECOND_TRADE = new StringBuffer();

	public static final StringBuffer SQL_GET_PARTCARDINFO = new StringBuffer();

	public static final StringBuffer SQL_UPDATE_TRADERECORD = new StringBuffer();
	
	public static final StringBuffer SQL_QUERY_CUURCODE = new StringBuffer();
	
	//保存SCB渣打辅助信息
	public static final StringBuffer SQL_SAVE_SCBFUNCTION = new StringBuffer();
	
	public static final StringBuffer SQL_QUERY_SCBBYTRNO = new StringBuffer();
	
	public static final StringBuffer SQL_UPDATE_SCBFUNCTION = new StringBuffer();
	
	public static final StringBuffer SQL_UPDATE_SECONDSCBFUNCTION = new StringBuffer();
	
	public static final StringBuffer SQL_GET_SCB_INVOICENO = new StringBuffer();
	
	public static final StringBuffer SQL_GET_MOTO3D_INVOICENO = new StringBuffer();
	
	public static final StringBuffer SQL_UPDATE_BANKORDERNO = new StringBuffer();
	
	public static final StringBuffer SQL_UPDATE_CHECKSTATUS = new StringBuffer();		//勾兑状态改为已勾兑
	
	public static final StringBuffer SQL_GETPAYTYPEFLAG_BOCVPN=new StringBuffer(); //得到通道二次支付标识，二次支付使用
	public static final StringBuffer SQL_GETPAYTYPEFLAG=new StringBuffer(); //得到通道二次支付标识，二次支付使用
	
	public static final StringBuffer SQL_SCBCHANNEL_INFO = new StringBuffer();
	
	public static final StringBuffer SQL_PARAMBEANSCB_INFO = new StringBuffer();

	public static final StringBuffer SQL_SAVE_MailAndIp = new StringBuffer();

	public static final StringBuffer SQL_GET_BlackList = new StringBuffer();

	public static final StringBuffer SQL_UPDATE_BlackList = new StringBuffer();

	public static final StringBuffer SQL_GET_WEBMONEY_SHOPINFO = new StringBuffer();
	
	public static final StringBuffer SQL_GET_POSP_SERVER_URL = new StringBuffer();
	public static final StringBuffer SQL_GET_SYSTRACE = new StringBuffer();
	public static final StringBuffer SQL_GET_SYSTRACE_ABC = new StringBuffer();
	
	
	static {
		SQL_GET_TRADINFO_BY_TRADENO.append("SELECT trade.tr_riskinfo,trade.TR_PASSRISKINFO,trade.tr_setscore,trade.tr_totalscore,trade.TR_NO, trade.TR_MER_ORDERNO, trade.TR_MER_NO, trade.TR_GW_NO, gateWay.GW_MD5KEY, trade.TR_CURRENCY, trade.TR_AMOUNT,");
		SQL_GET_TRADINFO_BY_TRADENO.append("trade.TR_BANKCURRENCY, trade.TR_BANKAMOUT, trade.TR_DATETIME, trade.TR_RETURNURL, trade.TR_REMARK,");
		SQL_GET_TRADINFO_BY_TRADENO.append("gateWay.GW_MAILTOHOLDER, gateWay.GW_MAILTOMER, mer.MER_EMAIL, credit.CI_EMAIL, credit.CI_FIRSTNAME, credit.CI_LASTNAME,credit.CI_IPCOUNTRY,credit.CI_IPADDRESS ");
		SQL_GET_TRADINFO_BY_TRADENO.append("FROM CCPS_TRADERECORD trade LEFT JOIN CCPS_GATEWAY gateWay ON trade.TR_GW_NO = gateWay.GW_NO ");
		SQL_GET_TRADINFO_BY_TRADENO.append("LEFT JOIN CCPS_MERCHANT mer on trade.TR_MER_NO = mer.MER_NO ");
		SQL_GET_TRADINFO_BY_TRADENO.append(" JOIN CCPS_CREDITINFO credit ON trade.TR_NO = credit.CI_TR_NO WHERE trade.TR_REFERENCE=?");
		
		//add by wj 修改or为union all
		SQL_BASE_TRADINFO_BY_TRREFERENCE.append("SELECT trade.tr_bank_code,trade.TR_ID,trade.TR_AUTHORIZELD, trade.TR_BATCHNO,trade.tr_riskinfo,trade.TR_PASSRISKINFO,trade.tr_setscore,trade.tr_totalscore,trade.TR_NO, trade.TR_MER_ORDERNO, trade.TR_MER_NO, trade.TR_GW_NO, gateWay.GW_MD5KEY, trade.TR_CURRENCY, trade.TR_AMOUNT,trade.TR_STATUS,trade.TR_BANKRETURNCODE,trade.TR_BANKINFO,");
		SQL_BASE_TRADINFO_BY_TRREFERENCE.append("trade.TR_BANKCURRENCY, trade.TR_BANKAMOUT, trade.TR_DATETIME, trade.TR_RETURNURL, trade.TR_REMARK, trade.TR_WEBSITE, trade.TR_CHA_CODE,gateWay.GW_INF_TYPE,gateWay.GW_RECURRING_FLAG,trade.TR_AUTH_TYPE,trade.TR_PM_ID,");
		SQL_BASE_TRADINFO_BY_TRREFERENCE.append("gateWay.GW_RETURNBILLADD,gateWay.GW_MAILTOHOLDER, gateWay.GW_MAILTOMER, gateWay.GW_RETURN_MODEL, mer.MER_EMAIL, credit.CI_CARDTYPE,credit.CI_EMAIL, credit.CI_FIRSTNAME, credit.CI_LASTNAME,credit.CI_IPCOUNTRY,credit.CI_IPADDRESS,credit.CI_CARDNOPART,credit.CI_ADDRESS, credit.CI_ZIPCODE ");
		SQL_BASE_TRADINFO_BY_TRREFERENCE.append(" ,trade.TR_BANKORDERNO,credit.ci_city,credit.ci_country,credit.CI_STATE,credit.ci_ext1,credit.ci_ext2 ");
		SQL_BASE_TRADINFO_BY_TRREFERENCE.append("FROM CCPS_TRADERECORD trade LEFT JOIN CCPS_GATEWAY gateWay ON trade.TR_GW_NO = gateWay.GW_NO ");
		SQL_BASE_TRADINFO_BY_TRREFERENCE.append("LEFT JOIN CCPS_MERCHANT mer on trade.TR_MER_NO = mer.MER_NO ");
		SQL_BASE_TRADINFO_BY_TRREFERENCE.append(" JOIN CCPS_CREDITINFO credit ON trade.TR_NO = credit.CI_TR_NO  ");
		
		SQL_GET_TRADINFO_BY_TRREFERENCE.append("SELECT tt.* from (");
		SQL_GET_TRADINFO_BY_TRREFERENCE.append("SELECT * FROM (");
		SQL_GET_TRADINFO_BY_TRREFERENCE.append(SQL_BASE_TRADINFO_BY_TRREFERENCE);
		SQL_GET_TRADINFO_BY_TRREFERENCE.append("WHERE trade.TR_REFERENCE=? UNION ALL ");
		SQL_GET_TRADINFO_BY_TRREFERENCE.append(SQL_BASE_TRADINFO_BY_TRREFERENCE);
		SQL_GET_TRADINFO_BY_TRREFERENCE.append("WHERE trade.TR_BANKORDERNO = ? ");
		SQL_GET_TRADINFO_BY_TRREFERENCE.append(")tradeUnion ORDER BY tradeUnion.TR_ID desc  ");		
		SQL_GET_TRADINFO_BY_TRREFERENCE.append(" ) tt where rownum < 2 ");
		
		SQL_GET_BANKCHANNEL_INFO.append("SELECT bank.BANK_ID,bank.BANK_CODE,bank.BANK_PAY_URL,bank.BANK_CHECK_URL, bank.BANK_REQ_URL,bank.BANK_ISDIRECT,cha.CHA_BILLADDRESS,");
		SQL_GET_BANKCHANNEL_INFO.append("cha.CHA_CODE,cha.CHA_MERNO,cha.CHA_VPC_ACCESSCODE, cha.CHA_SECURE_SECRET, cha.CHA_OBLIGATE1, cha.CHA_OBLIGATE2, cha.CHA_IS_3D,cha.CHA_3DS_PROVIDER,");
		SQL_GET_BANKCHANNEL_INFO.append("cha.CHA_CURRENCY, cha.CHA_SETTLEMENT_BANK, cha.CHA_ISDCC, cha.CHA_ISDELAY,method.PM_ID, method.PM_NAME,cha.CHA_NO3DPAYMENT ");
		SQL_GET_BANKCHANNEL_INFO.append("FROM CCPS_CHANNEL cha LEFT JOIN CCPS_BANK bank  ON cha.CHA_BANK_CODE=bank.BANK_CODE LEFT JOIN CCPS_PAYMENT_METHOD method  ON bank.BANK_PM_ID=method.PM_ID  ");
		SQL_GET_BANKCHANNEL_INFO.append("WHERE cha.CHA_CODE=?");
		
		SQL_UPDATE_TRADE_TECORD.append("UPDATE CCPS_TRADERECORD SET TR_STATUS = ?, TR_BANKORDERNO = ?, TR_BANKRETURNCODE = ?, TR_BANKINFO = ?,");
		SQL_UPDATE_TRADE_TECORD.append("TR_BANKDATETIME = systimestamp, TR_QUERYNO = ?, TR_AUTHORIZELD = ?, TR_BATCHNO = ?, TR_TERMINALNO = ? WHERE TR_NO = ?");
	
		SQL_UPDATE_AUTH_TECORD.append("UPDATE CCPS_TRADERECORD SET TR_STATUS = ?, TR_BANKORDERNO = ?, TR_BANKRETURNCODE = ?, TR_BANKINFO = ?,");
		SQL_UPDATE_AUTH_TECORD.append("TR_BANKDATETIME = systimestamp, TR_QUERYNO = ?, TR_AUTHORIZELD = ?, TR_BATCHNO = ?, TR_TERMINALNO = ? WHERE TR_NO = ?");

		
		SQL_GET_DOMAININFO_BY_GATEWAY.append("SELECT   domainInfo.DI_HLEPWEBSITE, domainInfo.DI_TEL, domainInfo.DI_FAX, domainInfo.DI_CSEMAIL,");
		SQL_GET_DOMAININFO_BY_GATEWAY.append("domainInfo.DI_FROMNAME, domainInfo.DI_EMAILTITLE, domainInfo.DI_THXCHOOSE, domainInfo.DI_CTTORDERNO,");
		SQL_GET_DOMAININFO_BY_GATEWAY.append("domainInfo.DI_ACQUIRER, sendEmail.E_SHOWNICK, sendEmail.E_EMAIL, sendEmail.E_PWD, sendEmail.E_PORT, ");
		SQL_GET_DOMAININFO_BY_GATEWAY.append("sendEmail.E_HOST, sendEmail.E_PROTOCOL, email.ET_SUBJECT, email.ET_CONTENT ");
		SQL_GET_DOMAININFO_BY_GATEWAY.append("FROM CCPS_DOMAIN_INFO domainInfo ");
		//SQL_GET_DOMAININFO_BY_GATEWAY.append("LEFT JOIN CCPS_DOMAIN domain ON domain.D_ID = domainInfo.DI_D_ID ");
		//SQL_GET_DOMAININFO_BY_GATEWAY.append("LEFT JOIN CCPS_MER_DOMAIN merDomain ON domainInfo.DI_D_ID = merDomain.MD_D_ID ");
		SQL_GET_DOMAININFO_BY_GATEWAY.append("LEFT JOIN CCPS_EMAIL_INFO sendEmail ON domainInfo.DI_E_ID = sendEmail.E_ID ");
		SQL_GET_DOMAININFO_BY_GATEWAY.append("LEFT JOIN CCPS_EMAIL_TEMPLATE email ON domainInfo.DI_ET_ID = email.ET_ID ");
		SQL_GET_DOMAININFO_BY_GATEWAY.append("WHERE 1=1 ");
		SQL_GET_DOMAININFO_BY_GATEWAY.append("AND domainInfo.DI_C_CODE=? ");
		//SQL_GET_DOMAININFO_BY_GATEWAY.append("WHERE  ((merDomain.MD_MER_NO=? AND merDomain.MD_GW_NO=?) ");
		//SQL_GET_DOMAININFO_BY_GATEWAY.append("OR (merDomain.MD_MER_NO=? AND merDomain.MD_GW_NO='0') ");
		//SQL_GET_DOMAININFO_BY_GATEWAY.append("OR (merDomain.MD_MER_NO='0' AND merDomain.MD_GW_NO='0')) ");
		//SQL_GET_DOMAININFO_BY_GATEWAY.append("AND domainInfo.DI_C_CODE=? ORDER BY merDomain.MD_GW_NO desc, merDomain.MD_MER_NO desc");
		
		SQL_SAVE_EMAIL.append("INSERT INTO CCPS_SENDMAILS (S_ID, S_REVEIVEMAIL, S_FROMNAME, S_SENDMAIL, S_SENDMAILPWD, S_TITLE, S_CONTENT, S_PROTOCOL,");
		SQL_SAVE_EMAIL.append("S_HOST, S_PORT, S_STATUS, S_MAXTIMES, S_TIMES, S_CREATETIME, S_SENDTIME, S_GWNO, S_MER_ORDERNO, S_TR_NO, S_TYPE)");
		SQL_SAVE_EMAIL.append("VALUES (CCPS_SENDMAILS_SEQ.nextval, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, systimestamp, ?, ?, ?, ?)");
		
		SQL_GET_MOTOVPN_INVOICENO.append("SELECT CCPS_MOTOVPN_INVOICE_SEQ.nextval FROM dual");
		
		
		SQL_GET_SCB_INVOICENO.append("SELECT CCPS_SCB_INVOICENO_SEQ.nextval FROM dual ");
		
		SQL_GET_SYSTRACE.append("SELECT systrace_seq.nextval FROM dual ");
		SQL_GET_SYSTRACE_ABC.append("SELECT SYSTRACE_ABC_SEQ.nextval FROM dual ");
		
		SQL_GET_MOTO3D_INVOICENO.append("SELECT CCPS_MOTO3D_INVOICENO_SEQ.nextval FROM dual ");
		
		SQL_SAVE_TRADE_DIFFINFO.append("INSERT INTO CCPS_CHANNEL_DIFFINFO (CDI_ID, CDI_TR_NO, CDI_SYS_STRACE, CDI_BILLNO, CDI_REVERSAL,");
		SQL_SAVE_TRADE_DIFFINFO.append("CDI_PRO_CODE, CDI_CNY_AMOUNT, CDI_INDEXNO, CDI_INDENTIFY_CODE, CDI_RETURNTIME, CDI_TRADECDOE, ");
		SQL_SAVE_TRADE_DIFFINFO.append("CDI_TRADEAMOUNT, CDI_RATE, CDI_REMARK, CDI_ISDCC, CDI_AMOUNT_SETTLEMENT, CDI_CURRENCY, ");
		SQL_SAVE_TRADE_DIFFINFO.append("CDI_CURRENCY_CODE, CDI_AUTH_RESP, CDI_REF_NO, CDI_REF_NO_RATE, CDI_TRANS_DATE, CDI_TRANS_TIME )");
		SQL_SAVE_TRADE_DIFFINFO.append(" VALUES (CCPS_CHANNEL_DIFFINFO_SEQ.nextval, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, ?, ?, ?, ");
		SQL_SAVE_TRADE_DIFFINFO.append("?, ?, ?, ?, ?, ?,?, ?, ?, ?)");
		
		SQL_SAVE_3DTRADE_DIFFINFO.append("INSERT INTO CCPS_CHANNEL_DIFFINFO (CDI_ID, CDI_TR_NO, CDI_SYS_STRACE, CDI_BILLNO, CDI_REVERSAL,");
		SQL_SAVE_3DTRADE_DIFFINFO.append("CDI_PRO_CODE, CDI_CNY_AMOUNT, CDI_INDEXNO, CDI_INDENTIFY_CODE, CDI_RETURNTIME, CDI_TRADECDOE, ");
		SQL_SAVE_3DTRADE_DIFFINFO.append("CDI_TRADEAMOUNT, CDI_RATE, CDI_REMARK, CDI_ISDCC, CDI_AMOUNT_SETTLEMENT, CDI_CURRENCY, ");
		SQL_SAVE_3DTRADE_DIFFINFO.append("CDI_CURRENCY_CODE, CDI_AUTH_RESP, CDI_REF_NO, CDI_REF_NO_RATE, CDI_TRANS_DATE, CDI_TRANS_TIME )");
		SQL_SAVE_3DTRADE_DIFFINFO.append(" VALUES (CCPS_CHANNEL_DIFFINFO_SEQ.nextval, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, ?, ?, ?, ");
		SQL_SAVE_3DTRADE_DIFFINFO.append("?, ?, ?, ?, ?, ?,?, ?, ?, ?)");
		
		SQL_GET_TRADE_DIFFINFO.append("SELECT C.CDI_CNY_AMOUNT,C.CDI_SYS_STRACE, C.CDI_AMOUNT_SETTLEMENT, C.CDI_CURRENCY_CODE FROM CCPS_CHANNEL_DIFFINFO C WHERE C.CDI_TR_NO=?");
		
		SQL_GET_CHANNELINFO_BY_FLAG.append("SELECT cha.CHA_ISDCC,cha.CHA_OBLIGATE2,cha.CHA_VPC_USER,cha.CHA_OBLIGATE1,cha.CHA_VPC_PASSWORD,cha.CHA_CODE,cha.CHA_MERNO,cha.CHA_VPC_ACCESSCODE, cha.CHA_SECURE_SECRET, cha.CHA_SETTLEMENT_BANK,cha.CHA_FEECURRENCY, ");
		SQL_GET_CHANNELINFO_BY_FLAG.append(" cha.CHA_CURRENCY,cha.CHA_FEEAMOUNT, cha.CHA_FEE_FAIL,cha.CHA_FEE_SUCCESS, cha.CHA_FEE_SUCCESS_AFTER, cha.CHA_IS_BACK, cha.CHA_IS_BACK_AFTER, ");
		SQL_GET_CHANNELINFO_BY_FLAG.append("rate.MR_TRADE_RATE,rate.MR_RESERVER_RATE, rate.MR_FEECURRENCY, rate.MR_FEEAMOUNT,rate.MR_LOW_FEEAMOUNT, rate.MR_FEE_FAIL, rate.MR_FEE_SUCCESS, ");
		SQL_GET_CHANNELINFO_BY_FLAG.append("rate.MR_FEE_SUCCESS_AFTER, rate.MR_IS_BACK, rate.MR_IS_BACK_AFTER, card.CC_CHA_RATE,card.CC_RSRATE ");
		SQL_GET_CHANNELINFO_BY_FLAG.append("FROM CCPS_CHANNEL cha LEFT JOIN CCPS_CHANNEL cc ON cha.CHA_FALG = cc.CHA_FALG ");
		SQL_GET_CHANNELINFO_BY_FLAG.append("LEFT JOIN CCPS_MER_RATE rate ON cha.CHA_CODE=rate.MR_CHA_CODE AND rate.MR_CARDTYPE=? AND rate.MR_MER_NO=? AND rate.MR_GW_NO=? ");
		SQL_GET_CHANNELINFO_BY_FLAG.append("LEFT JOIN CCPS_CHA_CARDTYPE card ON cha.CHA_CODE=card.CC_CHA_CODE AND card.CC_CARDTYPE=? ");
		SQL_GET_CHANNELINFO_BY_FLAG.append("WHERE cha.CHA_BANK_CODE=cc.CHA_BANK_CODE AND cc.CHA_CODE=? AND cha.CHA_ISDCC=? AND  cha.CHA_CURRENCY ='CNY'  ");

		SQL_GET_THREEDCHANNELINFO_BY_FLAG.append("SELECT cha.CHA_IS_3D,cha.CHA_ISDCC,cha.CHA_OBLIGATE2,cha.CHA_VPC_USER,cha.CHA_OBLIGATE1,cha.CHA_VPC_PASSWORD,cha.CHA_CODE,cha.CHA_MERNO,cha.CHA_VPC_ACCESSCODE, cha.CHA_SECURE_SECRET, cha.CHA_SETTLEMENT_BANK,cha.CHA_FEECURRENCY, ");
		SQL_GET_THREEDCHANNELINFO_BY_FLAG.append(" cha.CHA_CURRENCY,cha.CHA_FEEAMOUNT, cha.CHA_FEE_FAIL,cha.CHA_FEE_SUCCESS, cha.CHA_FEE_SUCCESS_AFTER, cha.CHA_IS_BACK, cha.CHA_IS_BACK_AFTER, ");
		SQL_GET_THREEDCHANNELINFO_BY_FLAG.append("rate.MR_TRADE_RATE,rate.MR_RESERVER_RATE, rate.MR_FEECURRENCY, rate.MR_FEEAMOUNT,rate.MR_LOW_FEEAMOUNT, rate.MR_FEE_FAIL, rate.MR_FEE_SUCCESS, ");
		SQL_GET_THREEDCHANNELINFO_BY_FLAG.append("rate.MR_FEE_SUCCESS_AFTER, rate.MR_IS_BACK, rate.MR_IS_BACK_AFTER, card.CC_CHA_RATE,card.CC_RSRATE ");
		SQL_GET_THREEDCHANNELINFO_BY_FLAG.append("FROM CCPS_CHANNEL cha LEFT JOIN CCPS_CHANNEL cc ON cha.CHA_FALG = cc.CHA_FALG ");
		SQL_GET_THREEDCHANNELINFO_BY_FLAG.append("LEFT JOIN CCPS_MER_RATE rate ON cha.CHA_CODE=rate.MR_CHA_CODE AND rate.MR_CARDTYPE=? AND rate.MR_MER_NO=? AND rate.MR_GW_NO=? ");
		SQL_GET_THREEDCHANNELINFO_BY_FLAG.append("LEFT JOIN CCPS_CHA_CARDTYPE card ON cha.CHA_CODE=card.CC_CHA_CODE AND card.CC_CARDTYPE=? ");
		SQL_GET_THREEDCHANNELINFO_BY_FLAG.append("WHERE cha.CHA_BANK_CODE=cc.CHA_BANK_CODE AND cc.CHA_CODE=? AND cha.CHA_IS_3D=? ");
		
		SQL_UPDATE_TRADERECORD_CHANNELCODE.append("UPDATE CCPS_TRADERECORD SET TR_RESEVER_RATE = ?,TR_TRADE_RATE = ?, TR_CHA_CODE = ?, TR_CHA_RATE  = ?,TR_CHA_RESV_RATE=?, TR_BANK_SPP_CURRENCY = ?,");
		SQL_UPDATE_TRADERECORD_CHANNELCODE.append("TR_BANK_SPP = ?, TR_CHA_SETT_BANK = ?, TR_FEE_FAIL_MER = ?, TR_FEE_SUCCESS_MER = ?, TR_FEE_SUCCESS_AFTER_MER = ?, ");
		SQL_UPDATE_TRADERECORD_CHANNELCODE.append("TR_IS_BACK_MER = ?,TR_IS_BACK_AFTER_MER = ?, TR_FEE_FAIL_CHA = ?,TR_FEE_SUCCESS_CHA = ?, TR_FEE_SUCCESS_AFTER_CHA = ?, ");
		SQL_UPDATE_TRADERECORD_CHANNELCODE.append("TR_IS_BACK_CHA = ?, TR_IS_BACK_AFTER_CHA = ?,TR_BANKCURRENCY=?,TR_BANKAMOUT=? ,TR_RATE_VALUE=? WHERE TR_NO = ?");
		
		SQL_UPDATE_MOTO_DIFF.append("UPDATE CCPS_CHANNEL_DIFFINFO SET CDI_PRO_CODE = ?,CDI_RETURNTIME = systimestamp,CDI_ISDCC = ?,CDI_AUTH_RESP = ?,");
		SQL_UPDATE_MOTO_DIFF.append("CDI_TRANS_DATE = ?, CDI_TRANS_TIME = ? ,CDI_REF_NO = ?  WHERE CDI_TR_NO = ?");
		
		SQL_IS_EXISTS_SEND_EMAIL.append("SELECT COUNT(*) FROM CCPS_SENDMAILS S WHERE (S.S_TYPE = ? OR S.S_TYPE = ?) AND S.S_TR_NO = ?");
		
		
		//二次支付
		
 		SQL_QUERYSECOND_GATEWAY.append(" SELECT GW_SECONDPAY FROM CCPS_GATEWAY   WHERE GW_NO=?");
		SQL_QUERYSECOND_PAYTYPE.append(" SELECT  m.MC_PAYTYPE FROM  ccps_mer_channel m where 1 = 1 ");
		SQL_QUERYSECOND_PAYTYPE.append(" and m.mc_cha_code in (SELECT c.cha_code    from ccps_traderecord t, ccps_channel c  WHERE t.tr_no = ? ");
		SQL_QUERYSECOND_PAYTYPE.append(" and t.tr_cha_code in    (select cl.cha_code from ccps_channel cl  where c.CHA_FALG = cl.CHA_FALG  and (c.CHA_FALG != '' or c.CHA_FALG is not null))) and m.mc_status=1 and m.mc_gw_no=?  and m.mc_cardtype=? ");
		
		SQL_UPDATE_PRETRADE_TECORD.append(" UPDATE CCPS_TRADERECORD SET TR_IS_REPAY = 2 WHERE TR_NO = ? ");
		
		SQL_PARAMBEAN_INFO.append(" SELECT   T.*, G.*, C.*, M.*,CT.*,CL.*,R.*,CC.* ,A.*,B.* ,AM.*");
		SQL_PARAMBEAN_INFO.append(" FROM CCPS_TRADERECORD T LEFT JOIN CCPS_GATEWAY G  ON T.TR_GW_NO = G.GW_NO"); 
		SQL_PARAMBEAN_INFO.append(" LEFT JOIN CCPS_MER_CHANNEL C ON T.TR_GW_NO = C.MC_GW_NO  LEFT JOIN CCPS_MERCHANT M ON T.TR_MER_NO = M.MER_NO   ");
		SQL_PARAMBEAN_INFO.append(" LEFT JOIN CCPS_CREDITINFO CT ON T.TR_NO = CT.CI_TR_NO  ");
		SQL_PARAMBEAN_INFO.append(" LEFT JOIN CCPS_CHANNEL CL ON C.MC_CHA_CODE = CL.CHA_CODE  ");
		SQL_PARAMBEAN_INFO.append(" LEFT JOIN CCPS_MER_RATE R  ON (R.MR_GW_NO = T.TR_GW_NO AND C.MC_BANK_CODE = R.MR_BANK_CODE AND C.MC_CHA_CODE = R.MR_CHA_CODE AND C.MC_CARDTYPE = R.MR_CARDTYPE)  ");
		SQL_PARAMBEAN_INFO.append(" LEFT JOIN CCPS_CHA_CARDTYPE CC  ON (C.MC_CHA_CODE = CC.CC_CHA_CODE AND CC.CC_CARDTYPE = C.MC_CARDTYPE)  ");
		SQL_PARAMBEAN_INFO.append(" LEFT JOIN CCPS_AGENT A ON T.TR_AGENT_NO=A.AGENT_NO  ");
		SQL_PARAMBEAN_INFO.append(" LEFT JOIN CCPS_BANK B ON CL.CHA_BANK_CODE=B.BANK_CODE  ");
		SQL_PARAMBEAN_INFO.append(" LEFT JOIN CCPS_AGENT_MER AM ON T.TR_AGENT_NO=AM.AM_AGENT_NO ");
		SQL_PARAMBEAN_INFO.append(" WHERE 1 = 1   AND c.MC_STATUS =1  AND C.MC_PAYTYPE = 2   AND CT.CI_CARDTYPE = C.MC_CARDTYPE ");
		SQL_PARAMBEAN_INFO.append(" AND T.TR_NO = ?  ");
		
		SQL_SAVE_SECOND_TRADE.append(" INSERT INTO CCPS_TRADERECORD (TR_ID, TR_NO, TR_MER_ORDERNO,  TR_MER_NO, TR_GW_NO, TR_CURRENCY, TR_AMOUNT, TR_STATUS, TR_TRADE_RATE, ");
		SQL_SAVE_SECOND_TRADE.append(" TR_SPP_CURRENCY, TR_SPP,TR_LOW_SPP, TR_AGENT_NO, TR_AGENT_RATE, TR_AGENT_SPP_CURRENCY, TR_AGENT_SPP, TR_RESEVER_RATE, TR_RATE_VALUE, TR_BANKCURRENCY, ");
		SQL_SAVE_SECOND_TRADE.append(" TR_BANKAMOUT, TR_BANK_CODE, TR_CHA_CODE, TR_ISDELAY, TR_CHA_RATE,TR_CHA_RESV_RATE, TR_BANK_SPP_CURRENCY, TR_BANK_SPP, TR_CHA_SETT_BANK, TR_PAYSTARTTIME, TR_PAYENDTIME, TR_DATETIME, TR_RETURNURL,");
		SQL_SAVE_SECOND_TRADE.append(" TR_WEBSITE, TR_SUBMITURL, TR_ISDCC, TR_INF_TYPE, TR_FEE_FAIL_MER, TR_FEE_SUCCESS_MER, TR_FEE_SUCCESS_AFTER_MER, TR_IS_BACK_MER, TR_IS_BACK_AFTER_MER, ");
		SQL_SAVE_SECOND_TRADE.append(" TR_FEE_FAIL_AGENT, TR_FEE_SUCCESS_AGENT, TR_FEE_SUCCESS_AFTER_AGENT, TR_IS_BACK_AGENT, TR_IS_BACK_AFTER_AGENT, TR_FEE_FAIL_CHA, TR_FEE_SUCCESS_CHA, ");
		SQL_SAVE_SECOND_TRADE.append(" TR_FEE_SUCCESS_AFTER_CHA, TR_IS_BACK_CHA, TR_IS_BACK_AFTER_CHA, TR_CARDTYPE, TR_REMARK,TR_RISKINFO,TR_SETSCORE,TR_TOTALSCORE,TR_PASSRISKINFO,TR_PM_ID,TR_AUTH_TYPE,TR_NOTIFICATION_URL) ");
		SQL_SAVE_SECOND_TRADE.append(" select    CCPS_TRADERECORD_SEQ.nextval, ?, TR_MER_ORDERNO,TR_MER_NO, TR_GW_NO, TR_CURRENCY, TR_AMOUNT, -1, TR_TRADE_RATE, ");
		SQL_SAVE_SECOND_TRADE.append(" TR_SPP_CURRENCY, TR_SPP,TR_LOW_SPP, TR_AGENT_NO, TR_AGENT_RATE, TR_AGENT_SPP_CURRENCY, TR_AGENT_SPP, TR_RESEVER_RATE, TR_RATE_VALUE, TR_BANKCURRENCY, ");
		SQL_SAVE_SECOND_TRADE.append(" TR_BANKAMOUT, TR_BANK_CODE, TR_CHA_CODE, TR_ISDELAY, TR_CHA_RATE,TR_CHA_RESV_RATE, TR_BANK_SPP_CURRENCY, TR_BANK_SPP, TR_CHA_SETT_BANK, TR_PAYSTARTTIME, TR_PAYENDTIME, TR_DATETIME, TR_RETURNURL, ");
		SQL_SAVE_SECOND_TRADE.append(" TR_WEBSITE, TR_SUBMITURL, TR_ISDCC, TR_INF_TYPE, TR_FEE_FAIL_MER, TR_FEE_SUCCESS_MER, TR_FEE_SUCCESS_AFTER_MER, TR_IS_BACK_MER, TR_IS_BACK_AFTER_MER, ");
		SQL_SAVE_SECOND_TRADE.append(" TR_FEE_FAIL_AGENT, TR_FEE_SUCCESS_AGENT, TR_FEE_SUCCESS_AFTER_AGENT, TR_IS_BACK_AGENT, TR_IS_BACK_AFTER_AGENT, TR_FEE_FAIL_CHA, TR_FEE_SUCCESS_CHA, ");
		SQL_SAVE_SECOND_TRADE.append(" TR_FEE_SUCCESS_AFTER_CHA, TR_IS_BACK_CHA, TR_IS_BACK_AFTER_CHA, TR_CARDTYPE, TR_REMARK,TR_RISKINFO,TR_SETSCORE,TR_TOTALSCORE,TR_PASSRISKINFO,TR_PM_ID,TR_AUTH_TYPE,TR_NOTIFICATION_URL ");
		SQL_SAVE_SECOND_TRADE.append(" from CCPS_TRADERECORD where    TR_NO=? ");
		
		SQL_SAVE_SECOND_TRADE_NEW.append(" INSERT INTO CCPS_TRADERECORD (TR_ID, TR_NO, TR_MER_ORDERNO,  TR_MER_NO, TR_GW_NO, TR_CURRENCY, TR_AMOUNT, TR_STATUS, TR_TRADE_RATE, ");
		// 原代理商信息不再保存  TR_AGENT_NO, TR_AGENT_RATE, TR_AGENT_SPP_CURRENCY, TR_AGENT_SPP, 
		SQL_SAVE_SECOND_TRADE_NEW.append(" TR_SPP_CURRENCY, TR_SPP,TR_LOW_SPP, TR_RESEVER_RATE, TR_RATE_VALUE, TR_BANKCURRENCY, ");
		SQL_SAVE_SECOND_TRADE_NEW.append(" TR_BANKAMOUT, TR_BANK_CODE, TR_CHA_CODE, TR_ISDELAY, TR_CHA_RATE,TR_CHA_RESV_RATE, TR_BANK_SPP_CURRENCY, TR_BANK_SPP, TR_CHA_SETT_BANK, TR_PAYSTARTTIME, TR_PAYENDTIME, TR_DATETIME, TR_RETURNURL,");
		SQL_SAVE_SECOND_TRADE_NEW.append(" TR_WEBSITE, TR_SUBMITURL, TR_ISDCC, TR_INF_TYPE, TR_FEE_FAIL_MER, TR_FEE_SUCCESS_MER, TR_FEE_SUCCESS_AFTER_MER, TR_IS_BACK_MER, TR_IS_BACK_AFTER_MER, ");
		// 原代理商信息不再保存  TR_FEE_FAIL_AGENT, TR_FEE_SUCCESS_AGENT, TR_FEE_SUCCESS_AFTER_AGENT, TR_IS_BACK_AGENT, TR_IS_BACK_AFTER_AGENT,
		SQL_SAVE_SECOND_TRADE_NEW.append(" TR_FEE_FAIL_CHA, TR_FEE_SUCCESS_CHA, ");
		SQL_SAVE_SECOND_TRADE_NEW.append(" TR_FEE_SUCCESS_AFTER_CHA, TR_IS_BACK_CHA, TR_IS_BACK_AFTER_CHA, TR_CARDTYPE, TR_REMARK,TR_RISKINFO,TR_SETSCORE,TR_TOTALSCORE,TR_PASSRISKINFO,TR_PM_ID,TR_AUTH_TYPE,TR_NOTIFICATION_URL,");
		/** 添加新的代理商信息 */
		SQL_SAVE_SECOND_TRADE_NEW.append(" TR_AGENT_NO1,TR_AGENT_NO2,TR_AGENT_NO3,TR_AS_CURRENCY1,TR_AS_CURRENCY2,TR_AS_CURRENCY3,TR_AS_RATE1,TR_AS_RATE2,TR_AS_RATE3,TR_AS_STATUS1,TR_AS_STATUS2,TR_AS_STATUS3");
		
		SQL_SAVE_SECOND_TRADE_NEW.append(" )  select CCPS_TRADERECORD_SEQ.nextval, ?, TR_MER_ORDERNO,TR_MER_NO, TR_GW_NO, TR_CURRENCY, TR_AMOUNT, -1, TR_TRADE_RATE, ");
		// 原代理商信息不再保存  TR_AGENT_NO, TR_AGENT_RATE, TR_AGENT_SPP_CURRENCY, TR_AGENT_SPP, 
		SQL_SAVE_SECOND_TRADE_NEW.append(" TR_SPP_CURRENCY, TR_SPP,TR_LOW_SPP, TR_RESEVER_RATE, TR_RATE_VALUE, TR_BANKCURRENCY, ");
		SQL_SAVE_SECOND_TRADE_NEW.append(" TR_BANKAMOUT, TR_BANK_CODE, TR_CHA_CODE, TR_ISDELAY, TR_CHA_RATE,TR_CHA_RESV_RATE, TR_BANK_SPP_CURRENCY, TR_BANK_SPP, TR_CHA_SETT_BANK, TR_PAYSTARTTIME, TR_PAYENDTIME, TR_DATETIME, TR_RETURNURL, ");
		SQL_SAVE_SECOND_TRADE_NEW.append(" TR_WEBSITE, TR_SUBMITURL, TR_ISDCC, TR_INF_TYPE, TR_FEE_FAIL_MER, TR_FEE_SUCCESS_MER, TR_FEE_SUCCESS_AFTER_MER, TR_IS_BACK_MER, TR_IS_BACK_AFTER_MER, ");
		// 原代理商信息不再保存  TR_FEE_FAIL_AGENT, TR_FEE_SUCCESS_AGENT, TR_FEE_SUCCESS_AFTER_AGENT, TR_IS_BACK_AGENT, TR_IS_BACK_AFTER_AGENT,
		SQL_SAVE_SECOND_TRADE_NEW.append(" TR_FEE_FAIL_CHA, TR_FEE_SUCCESS_CHA, ");
		SQL_SAVE_SECOND_TRADE_NEW.append(" TR_FEE_SUCCESS_AFTER_CHA, TR_IS_BACK_CHA, TR_IS_BACK_AFTER_CHA, TR_CARDTYPE, TR_REMARK,TR_RISKINFO,TR_SETSCORE,TR_TOTALSCORE,TR_PASSRISKINFO,TR_PM_ID,TR_AUTH_TYPE,TR_NOTIFICATION_URL, ");
		/** 添加新的代理商信息 */
		SQL_SAVE_SECOND_TRADE_NEW.append(" TR_AGENT_NO1,TR_AGENT_NO2,TR_AGENT_NO3,TR_AS_CURRENCY1,TR_AS_CURRENCY2,TR_AS_CURRENCY3,TR_AS_RATE1,TR_AS_RATE2,TR_AS_RATE3,0,0,0");
		SQL_SAVE_SECOND_TRADE_NEW.append(" from CCPS_TRADERECORD where TR_NO=? ");
		
		SQL_SAVE_SECOND_CREDIT.append(" insert into CCPS_CREDITINFO (CI_ID, CI_TR_NO, CI_EMAIL, CI_TEL, CI_FIRSTNAME, CI_LASTNAME, CI_COUNTRY, CI_STATE, CI_CITY, CI_ZIPCODE, CI_CELLPNONE, CI_ADDRESS, CI_IPADDRESS, CI_IPCOUNTRY, CI_MER_IPADDRESS, CI_ISSUINGBANK, ");
		SQL_SAVE_SECOND_CREDIT.append(" CI_ISSUINGBANKTEL, CI_CARDCOUNTRY, CI_FROMNAME, CI_ACQUIRER, CI_ISSENDEMAIL, CI_OS, CI_BROWER, CI_LANG, CI_TIMEZONE, CI_RESOLUTION, CI_COOKIEID, CI_OLD_COOKIE, CI_COOKIE_FLAG, CI_SHA256, CI_CARDNOPART, CI_CARDTYPE, ");
		SQL_SAVE_SECOND_CREDIT.append(" CI_REMARK, CI_PAYMENT_METHOD, CI_EBANX_NAME, CI_EBANX_EMAIL, CI_EBANX_TYPE, CI_QIWI_USER, CI_PPRO_ACCOUNT_NUMBER, CI_PPRO_BANKCODE,CI_CARDNO_ENCRYPT ,CI_QIWI_COUNTRYCODE ) ");
		SQL_SAVE_SECOND_CREDIT.append(" SELECT Ccps_Creditinfo_Seq.nextval,?,CI_EMAIL, CI_TEL, CI_FIRSTNAME, CI_LASTNAME, CI_COUNTRY, CI_STATE, CI_CITY, CI_ZIPCODE, CI_CELLPNONE, CI_ADDRESS, CI_IPADDRESS, CI_IPCOUNTRY, CI_MER_IPADDRESS, CI_ISSUINGBANK, ");
		SQL_SAVE_SECOND_CREDIT.append(" CI_ISSUINGBANKTEL, CI_CARDCOUNTRY, CI_FROMNAME, CI_ACQUIRER, CI_ISSENDEMAIL, CI_OS, CI_BROWER, CI_LANG,  CI_TIMEZONE, CI_RESOLUTION, CI_COOKIEID, CI_OLD_COOKIE, CI_COOKIE_FLAG, CI_SHA256, CI_CARDNOPART, CI_CARDTYPE, ");
		SQL_SAVE_SECOND_CREDIT.append(" CI_REMARK, CI_PAYMENT_METHOD, CI_EBANX_NAME, CI_EBANX_EMAIL, CI_EBANX_TYPE, CI_QIWI_USER, CI_PPRO_ACCOUNT_NUMBER, CI_PPRO_BANKCODE ,CI_CARDNO_ENCRYPT, CI_QIWI_COUNTRYCODE ");
		SQL_SAVE_SECOND_CREDIT.append(" from    CCPS_CREDITINFO where CI_TR_NO =? ");
		
		SQL_UPADTE_SECOND_TRADE.append(" update CCPS_TRADERECORD set TR_BANK_CODE=?, TR_CHA_CODE=? ,TR_TRADE_RATE =?,TR_SPP_CURRENCY  =?,TR_SPP =?,TR_LOW_SPP=?,TR_RESEVER_RATE =?, ");
		SQL_UPADTE_SECOND_TRADE.append(" TR_RATE_VALUE =?,TR_BANKCURRENCY =?,TR_BANKAMOUT = ? , TR_CHA_RATE =?,TR_CHA_RESV_RATE=?,TR_BANK_SPP_CURRENCY =?, ");
		SQL_UPADTE_SECOND_TRADE.append(" TR_BANK_SPP=?,TR_FEE_FAIL_MER =?,TR_FEE_SUCCESS_MER =?,TR_FEE_SUCCESS_AFTER_MER =?,TR_IS_BACK_MER =?,TR_IS_BACK_AFTER_MER =?, ");
		SQL_UPADTE_SECOND_TRADE.append(" TR_FEE_FAIL_CHA =?,TR_FEE_SUCCESS_CHA =?,TR_FEE_SUCCESS_AFTER_CHA =?,TR_IS_BACK_CHA =?,TR_IS_BACK_AFTER_CHA =? ,TR_ISDELAY=?,TR_CHA_SETT_BANK=?, TR_DATETIME=SYSTIMESTAMP ");
		SQL_UPADTE_SECOND_TRADE.append("  where TR_NO=?  ");
		
		SQL_GET_PARTCARDINFO.append("select  c.ci_cardnopart from ccps_creditinfo c where c.ci_tr_no = ? ");
		
		SQL_UPDATE_TRADERECORD.append("update ccps_traderecord t set t.TR_STATUS = ? where t.TR_NO = ?");
		
		SQL_QUERY_CUURCODE.append(" select c.curr_code from ccps_currency c where c.curr_name = ?");
		
		SQL_SAVE_SCBFUNCTION.append(" insert into CCPS_SCB_FUNCTION(SCB_ID, SCB_TR_NO, SCB_ACS_RETURN_URL,SCB_UNIQUENUMBER,SCB_ORDERID, SCB_IS3D,SCB_REFERCENO,SCB_XID,SCB_SECURITYLEVEL,SCB_CAV,SCB_TIME,SCB_TYPE,SCB_AUTHNO,SCB_SIGVERSTATUS,SCB_PARESSTATUS)");
		SQL_SAVE_SCBFUNCTION.append(" values(CCPS_SCBFUNCTION_SEQ.nextval,?,?,?,?,?,?,?,?,?,sysdate,?,?,?,?)");
		
		SQL_QUERY_SCBBYTRNO.append(" select SCB_ID, SCB_TR_NO, SCB_ACS_RETURN_URL,SCB_UNIQUENUMBER,SCB_ORDERID, SCB_IS3D from CCPS_SCB_FUNCTION where SCB_TR_NO = ?");
	
		SQL_UPDATE_SCBFUNCTION.append(" update CCPS_SCB_FUNCTION set SCB_ACS_RETURN_URL = ? , SCB_ORDERID = ?,SCB_IS3D = ? where SCB_TR_NO = ? ");
	
		SQL_UPDATE_SECONDSCBFUNCTION.append(" update CCPS_SCB_FUNCTION set SCB_SECURITYLEVEL = ? , SCB_CAV = ?,SCB_XID = ? where SCB_TR_NO = ? ");
	
		SQL_UPDATE_BANKORDERNO.append(" update ccps_traderecord t set t.tr_bankorderno = ?  where t.tr_no = ?");
	
		SQL_UPDATE_CHECKSTATUS.append(" update ccps_traderecord t set t.tr_checked = 1 , t.tr_checkdatetime = sysdate  where t.tr_no = ? ");
		
		
		SQL_GETPAYTYPEFLAG_BOCVPN.append("select MC_PAYTYPE from ccps_mer_channel mc where 1 = 1 and mc.mc_cha_code in (  SELECT cha.CHA_CODE  FROM CCPS_CHANNEL cha ");
		SQL_GETPAYTYPEFLAG_BOCVPN.append(" LEFT JOIN CCPS_CHANNEL cc ON cha.CHA_FALG = cc.CHA_FALG WHERE cha.CHA_BANK_CODE = cc.CHA_BANK_CODE  AND cc.CHA_CODE = ?) and MC.MC_STATUS = 1 and mc.mc_gw_no = ? and mc.MC_CARDTYPE=? ");
		
		SQL_GETPAYTYPEFLAG.append(" select MC_PAYTYPE  from ccps_mer_channel mc where 1 = 1  and MC.MC_STATUS = 1  and mc.MC_CHA_CODE =? and mc.mc_gw_no = ? and mc.MC_CARDTYPE=? ");
		
		SQL_SCBCHANNEL_INFO.append("select sh.sc_cha_currency,sh.sc_mid, sh.sc_tid,sh.sc_mer_city,sh.sc_mer_country, sh.sc_mer_name,");
		SQL_SCBCHANNEL_INFO.append(" sh.sc_mer_acquire_citycode, sh.sc_mer_type, sh.sc_processor_id, sh.sc_txn_pwd,");
		SQL_SCBCHANNEL_INFO.append(" sh.sc_digit_mid, sh.sc_remark from ccps_scbchannel_info sh where sh.sc_cha_code = ? and upper(sh.sc_cha_currency)= ? and sh.sc_status =1 ");
	
		SQL_PARAMBEANSCB_INFO.append("select ch.mc_id, ch.mc_cha_code, ch.mc_pm_id, ch.mc_open_author, ch.mc_auto_author, ch.mc_paytype from ccps_mer_channel ch , ccps_channel c ");
		SQL_PARAMBEANSCB_INFO.append(" where ch.mc_gw_no = ?  and  ch.mc_cha_code = ? and ch.mc_cardtype = ? and ch.mc_status = 1 and ch.mc_cha_code =  c.cha_code");
	
		SQL_SAVE_MailAndIp.append("insert into ccps_blacklist (BV_ID,BV_BE_ID,BV_VALUE,BV_STATUS,BV_TYPE,BV_LOGIN_NAME,BV_OPRTIME,BV_REMARK,BV_KIND) ");
		SQL_SAVE_MailAndIp.append(" values(CCPS_BLACKLIST_SEQ.nextval, ?,?,?,?,?,sysdate,?,?)");
		
		SQL_GET_BlackList.append("select b.bv_be_id ,b.bv_value, b.bv_status, b.bv_id, b.bv_kind from ccps_blacklist b where ((b.bv_be_id = ? and upper(b.bv_value) = ?) ");
		SQL_GET_BlackList.append(" or (b.bv_be_id = ? and b.bv_value = ?)) and b.bv_kind = ?");
		
		SQL_UPDATE_BlackList.append("update ccps_blacklist b set b.bv_status = ? , b.bv_type = ? , b.bv_login_name = ? , b.bv_oprtime = sysdate,");
		SQL_UPDATE_BlackList.append(" b.bv_remark = ? where b.bv_id = ?");
		
		SQL_GET_WEBMONEY_SHOPINFO.append("select sp.sp_id,sp.sp_website,sp.sp_shopid,sp.sp_megastock,sp.sp_datetime,sp.sp_operater,sp.sp_remark");
		SQL_GET_WEBMONEY_SHOPINFO.append(" from ccps_webmoney_shopinfo sp where  ? like '%'||sp.sp_website||'%'");
			
		SQL_GET_POSP_SERVER_URL.append("SELECT BANK_CODE,BANK_PAY_URL FROM CCPS_BANK WHERE BANK_CODE = ?");
	}
}
 